import React, { useState, useEffect, useCallback, useRef } from 'react';
import { 
  Button, 
  Spin, 
  message,
  Tooltip,
  Card,
  Modal,
  Tag,
  Select,
  Empty,
  Input,
  Row,
  Col,
  Typography,
  Avatar
} from 'antd';
import { 
  EditOutlined,
  ReloadOutlined,
  DragOutlined,
  DeleteOutlined,
  Exclamation<PERSON>ircleOutlined,
  RollbackOutlined,
  TeamOutlined,
  DatabaseOutlined,
  CommentOutlined,
  HolderOutlined,
  UserOutlined,
  ManOutlined,
  WomanOutlined,
  CheckOutlined,
  CloseOutlined
} from '@ant-design/icons';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
  useSortable,
} from '@dnd-kit/sortable';
import {
  CSS,
} from '@dnd-kit/utilities';

import { 
  getClazzStudents,
  createClazzStudent,
  batchDeleteClazzStudents,
  batchUpdateClazzStudentOrder,
  batchCreateClazzStudents,
  type ClassStudentWithStudentInfoResponse,
  type ClassStudentCreate,
  type ClassStudentBatchOrderRequest,
  type ClassStudentBatchCreateRequest,
  type ClassStudentBatchDeleteRequest} from '../../../services/system/clazz';
import { getStudents, type StudentResponse } from '../../../services/system/student';
import { showError } from '../../../utils/errorHandler';
import { getGlobalTenantInfo } from '../../../components/GlobalTenantSelector';

const { Text } = Typography;
const { Search } = Input;
const { Option } = Select;
const { confirm } = Modal;

// 可拖拽的班级学员项组件
interface SortableClazzStudentItemProps {
  clazzStudent: ClassStudentWithStudentInfoResponse;
  index: number;
  isSortMode?: boolean;
  isEditMode?: boolean;
  onDelete: (clazzStudentId: number) => void;
  getGenderIcon: (gender: number) => JSX.Element;
}

const SortableClazzStudentItem: React.FC<SortableClazzStudentItemProps> = ({
  clazzStudent,
  index,
  isSortMode = false,
  isEditMode = false,
  onDelete,
  getGenderIcon
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: `student-${clazzStudent.id}` });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition: transition || 'all 0.2s',
    opacity: isDragging ? 0.5 : 1,
    backgroundColor: 'transparent',
    border: '1px solid #f0f0f0',
    borderRadius: '6px',
    padding: '12px',
    marginBottom: '8px',
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      onMouseEnter={(e) => {
        e.currentTarget.style.backgroundColor = '#f5f5f5';
        e.currentTarget.style.borderColor = '#d9d9d9';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.backgroundColor = 'transparent';
        e.currentTarget.style.borderColor = '#f0f0f0';
      }}
    >
      <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
        {isSortMode && (
          <div 
            {...listeners}
            style={{ 
              cursor: 'grab',
              color: '#999',
              fontSize: '14px'
            }}
          >
            <HolderOutlined />
          </div>
        )}

        {/* 序号 */}
        <div style={{ 
          backgroundColor: '#666',
          color: 'white',
          width: '20px',
          height: '20px',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '12px',
          fontWeight: 'bold',
          flexShrink: 0
        }}>
          {index + 1}
        </div>

        {/* 头像占位 */}
        <div style={{ 
          width: '48px', 
          height: '48px', 
          borderRadius: '50%',
          backgroundColor: '#f5f5f5',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexShrink: 0
        }}>
          <Avatar 
            icon={<UserOutlined />} 
            size={48} 
            style={{ backgroundColor: '#d9d9d9', color: '#999' }}
          />
        </div>

        {/* 姓名和性别 */}
        <div style={{ 
          flex: 1, 
          minWidth: 0,
          height: '48px',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between'
        }}>
          {/* 姓名行：姓名 + info图标 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
            <Text strong style={{ 
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              fontSize: '14px',
              lineHeight: '20px'
            }}>
              {clazzStudent.name}
            </Text>
            {/* 备注图标（悬停显示备注） */}
            {clazzStudent.notes && (
              <Tooltip title={clazzStudent.notes} placement="top">
                <CommentOutlined 
                  style={{ 
                    color: '#999', 
                    fontSize: '12px',
                    flexShrink: 0
                  }} 
                />
              </Tooltip>
            )}
          </div>
          {/* 性别图标行：底部对齐 */}
          <div style={{ alignSelf: 'flex-start' }}>
            {getGenderIcon(clazzStudent.gender)}
          </div>
        </div>

        {/* 编辑状态下的删除按钮 */}
        {isEditMode && (
          <Tooltip title="删除">
            <Button
              type="text"
              icon={<DeleteOutlined />}
              size="small"
              danger
              onClick={() => onDelete(clazzStudent.id)}
              style={{ marginLeft: '4px' }}
            />
          </Tooltip>
        )}
      </div>
    </div>
  );
};

interface ClazzStudentsTabProps {
  clazzId: number;
}

const ClazzStudentsTab: React.FC<ClazzStudentsTabProps> = ({ clazzId }) => {
  // 拖拽排序传感器
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // 班级学员相关状态
  const [clazzStudents, setClazzStudents] = useState<ClassStudentWithStudentInfoResponse[]>([]);
  const [studentsLoading, setStudentsLoading] = useState(false);
  const [availableStudents, setAvailableStudents] = useState<StudentResponse[]>([]);
  const [selectedTenantId, setSelectedTenantId] = useState<number | undefined>(undefined);
  
  // 排序模式相关状态
  const [isSortMode, setIsSortMode] = useState(false);
  
  // 编辑模式相关状态
  const [isEditMode, setIsEditMode] = useState(false);
  
  // 学员库搜索相关状态
  const [libraryLoading, setLibraryLoading] = useState(false);
  const [searchName, setSearchName] = useState('');
  const [searchNameValue, setSearchNameValue] = useState('');
  const [searchNotes, setSearchNotes] = useState('');
  const [searchNotesValue, setSearchNotesValue] = useState('');
  const [searchGender, setSearchGender] = useState<number | undefined>(undefined); // 性别筛选

  // 跟踪已选中的学员ID列表（本班级中的学员）
  const [selectedStudentIds, setSelectedStudentIds] = useState<Set<number>>(new Set());

  // 创建对搜索框的引用
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const searchNameInputRef = useRef<any>(null);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const searchNotesInputRef = useRef<any>(null);

  // 获取租户信息
  const loadGlobalTenant = useCallback(() => {
    const tenantInfo = getGlobalTenantInfo();
    if (tenantInfo) {
      setSelectedTenantId(tenantInfo.id);
      return tenantInfo;
    } else {
      setSelectedTenantId(undefined);
      return null;
    }
  }, []);

  // 监听全局租户变化
  useEffect(() => {
    loadGlobalTenant();

    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'globalTenant') {
        loadGlobalTenant();
      }
    };

    const handleGlobalTenantChange = (e: Event) => {
      const customEvent = e as CustomEvent;
      const { id } = customEvent.detail;
      if (id) {
        setSelectedTenantId(id);
      } else {
        setSelectedTenantId(undefined);
      }
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('globalTenantChanged', handleGlobalTenantChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('globalTenantChanged', handleGlobalTenantChange);
    };
  }, [loadGlobalTenant]);

  // 获取班级学员列表
  const fetchClazzStudents = useCallback(async () => {
    if (!clazzId || !selectedTenantId) return;
    
    try {
      setStudentsLoading(true);
      const response = await getClazzStudents(clazzId, selectedTenantId);
      setClazzStudents(response.items);
      // 更新已选中的学员ID列表
      const selectedIds = new Set(response.items.map(item => item.sid));
      setSelectedStudentIds(selectedIds);
    } catch (error) {
      showError(error, '获取班级学员失败');
    } finally {
      setStudentsLoading(false);
    }
  }, [clazzId, selectedTenantId]);

  // 获取学员库
  const fetchStudentLibrary = useCallback(async () => {
    if (!selectedTenantId) {
      setAvailableStudents([]);
      return;
    }

    setLibraryLoading(true);
    try {
      const students = await getStudents(selectedTenantId, {
        active: 1,
        limit: 100,
        skip: 0,
        // 使用服务端搜索功能
        name: searchName || undefined,
        notes: searchNotes || undefined,
        gender: searchGender
      });
      
      setAvailableStudents(students.items);
    } catch (error) {
      showError(error, '获取学员库失败');
    } finally {
      setLibraryLoading(false);
    }
  }, [selectedTenantId, searchName, searchNotes, searchGender]);

  // 切换排序模式
  const toggleSortMode = () => {
    if (!isSortMode && !clazzStudents.length) {
      message.warning('没有可排序的学员');
      return;
    }
    setIsSortMode(!isSortMode);
    // 退出排序模式时，也要退出编辑模式
    if (isSortMode) {
      setIsEditMode(false);
    }
  };

  // 切换编辑模式
  const toggleEditMode = () => {
    setIsEditMode(!isEditMode);
    // 进入编辑模式时，退出排序模式
    if (!isEditMode) {
      setIsSortMode(false);
    }
  };

  // 处理拖拽结束
  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    if (!active.id || !over?.id || !clazzId || !selectedTenantId) return;

    // 解析拖拽ID，提取真实ID
    const parseId = (id: string | number) => {
      const idStr = id.toString();
      if (idStr.startsWith('student-')) {
        return parseInt(idStr.replace('student-', ''));
      }
      return 0;
    };

    const activeId = parseId(active.id);
    const overId = parseId(over.id);

    // 检查是否是学员拖拽
    if (isSortMode && activeId !== overId && activeId > 0 && overId > 0) {
      const oldIndex = clazzStudents.findIndex((student) => student.id === activeId);
      const newIndex = clazzStudents.findIndex((student) => student.id === overId);

      if (oldIndex === -1 || newIndex === -1) return;

      const newClazzStudents = arrayMove(clazzStudents, oldIndex, newIndex);
      setClazzStudents(newClazzStudents);
      
      // 立即调用API更新排序
      try {
        const orderData: ClassStudentBatchOrderRequest = {
          tenant_id: selectedTenantId,
          class_id: clazzId,
          class_students: newClazzStudents.map((student, index) => ({
            id: student.id,
            priority: index + 1
          }))
        };
        
        await batchUpdateClazzStudentOrder(orderData);
        message.success('学员排序更新成功');
      } catch (error) {
        // 如果API调用失败，恢复原始顺序
        setClazzStudents(clazzStudents);
        showError(error, '学员排序更新失败');
      }
    }
  };

  // 删除学员
  const handleDeleteStudent = (clazzStudentId: number) => {
    confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: '确定要从班级中移除这个学员吗？',
      okText: '确认',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await batchDeleteClazzStudents({
            tenant_id: selectedTenantId!,
            class_id: clazzId,
            class_student_ids: [clazzStudentId]
          });
          message.success('删除成功');
          fetchClazzStudents();
        } catch (error) {
          showError(error, '删除学员失败');
        }
      },
    });
  };

  // 刷新学员列表
  const handleRefreshStudents = () => {
    fetchClazzStudents();
  };

  // 处理姓名搜索
  const handleNameSearch = (value: string) => {
    if (!isEditMode) return;
    setSearchName(value);
    setSearchNameValue(value);
  };

  // 处理姓名搜索输入框值变化
  const handleNameInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!isEditMode) return;
    setSearchNameValue(e.target.value);
  };

  // 处理备注搜索
  const handleNotesSearch = (value: string) => {
    if (!isEditMode) return;
    setSearchNotes(value);
    setSearchNotesValue(value);
  };

  // 处理备注搜索输入框值变化
  const handleNotesInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!isEditMode) return;
    setSearchNotesValue(e.target.value);
  };

  // 重置学员库搜索
  const handleResetLibrarySearch = () => {
    if (!isEditMode) return;
    setSearchGender(undefined);
    setSearchName('');
    setSearchNameValue('');
    setSearchNotes('');
    setSearchNotesValue('');
  };

  // 处理学员库点击 - 添加或删除班级-学员关系
  const handleLibraryStudentClick = async (student: StudentResponse) => {
    if (!isEditMode || !clazzId) return;
    
    if (!selectedTenantId) {
      message.error('请先在顶部导航栏选择租户');
      return;
    }

    const isSelected = selectedStudentIds.has(student.id);
    
    try {
      if (isSelected) {
        // 删除班级-学员关系
        const clazzStudent = clazzStudents.find(cs => cs.sid === student.id);
        if (clazzStudent) {
          await batchDeleteClazzStudents({
            tenant_id: selectedTenantId,
            class_id: clazzId,
            class_student_ids: [clazzStudent.id]
          });
          message.success('已从班级中移除学员');
          
          // 更新选中状态
          const newSelectedIds = new Set(selectedStudentIds);
          newSelectedIds.delete(student.id);
          setSelectedStudentIds(newSelectedIds);
          
          // 重新获取本班级学员
          await fetchClazzStudents();
        }
      } else {
        // 创建班级-学员关系
        const createData: ClassStudentCreate = {
          tenant_id: selectedTenantId,
          cid: clazzId,
          sid: student.id,
          priority: clazzStudents.length + 1
        };
        
        await createClazzStudent(clazzId, createData);
        message.success('已添加学员到班级');
        
        // 更新选中状态
        const newSelectedIds = new Set(selectedStudentIds);
        newSelectedIds.add(student.id);
        setSelectedStudentIds(newSelectedIds);
        
        // 重新获取本班级学员
        await fetchClazzStudents();
      }
    } catch (error) {
      showError(error, isSelected ? '移除学员失败' : '添加学员失败');
    }
  };

  // 批量添加所有学员库中的学员到班级
  const handleSelectAll = () => {
    if (!isEditMode || !clazzId || !selectedTenantId || !availableStudents.length) return;

    // 获取未选中的学员ID列表
    const unselectedStudentIds = availableStudents
      .filter(student => !selectedStudentIds.has(student.id))
      .map(student => student.id);

    if (unselectedStudentIds.length === 0) {
      message.info('所有学员已在班级中');
      return;
    }

    confirm({
      title: '确认全选',
      icon: <ExclamationCircleOutlined />,
      content: `确定要将所有 ${unselectedStudentIds.length} 个学员添加到班级中吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        try {
          const requestData: ClassStudentBatchCreateRequest = {
            tenant_id: selectedTenantId,
            class_id: clazzId,
            student_ids: unselectedStudentIds
          };

          await batchCreateClazzStudents(requestData);
          message.success(`成功添加 ${unselectedStudentIds.length} 个学员到班级`);
          
          // 重新获取本班级学员
          await fetchClazzStudents();
        } catch (error) {
          showError(error, '批量添加学员失败');
        }
      },
    });
  };

  // 批量移除所有班级中的学员
  const handleDeselectAll = () => {
    if (!isEditMode || !clazzId || !selectedTenantId || !clazzStudents.length) return;

    confirm({
      title: '确认取消全选',
      icon: <ExclamationCircleOutlined />,
      content: `确定要将所有 ${clazzStudents.length} 个学员从班级中移除吗？`,
      okText: '确认',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          const requestData: ClassStudentBatchDeleteRequest = {
            tenant_id: selectedTenantId,
            class_id: clazzId,
            class_student_ids: clazzStudents.map(cs => cs.id)
          };

          await batchDeleteClazzStudents(requestData);
          message.success(`成功移除 ${clazzStudents.length} 个学员`);
          
          // 重新获取本班级学员
          await fetchClazzStudents();
        } catch (error) {
          showError(error, '批量移除学员失败');
        }
      },
    });
  };

  // 获取性别图标
  const getGenderIcon = (gender: number) => {
    const genderMap: { [key: number]: { name: string; color: string; icon: JSX.Element } } = {
      0: { name: '未知', color: 'default', icon: <UserOutlined /> },
      1: { name: '男', color: 'blue', icon: <ManOutlined /> },
      2: { name: '女', color: 'pink', icon: <WomanOutlined /> },
    };
    const genderInfo = genderMap[gender] || { name: '未知', color: 'default', icon: <UserOutlined /> };
    return (
      <Tag color={genderInfo.color} icon={genderInfo.icon}>
        {genderInfo.name}
      </Tag>
    );
  };

  useEffect(() => {
    if (selectedTenantId && clazzId) {
      fetchClazzStudents();
    }
  }, [fetchClazzStudents, selectedTenantId, clazzId]);

  // 监听学员库搜索参数变化
  useEffect(() => {
    if (isEditMode) {
      fetchStudentLibrary();
    }
  }, [fetchStudentLibrary, isEditMode, searchName, searchNotes, searchGender]);

  // 监听ESC键退出编辑状态
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isEditMode) {
        setIsEditMode(false);
        message.info('已退出编辑模式');
      }
    };

    // 添加键盘事件监听器
    document.addEventListener('keydown', handleKeyDown);

    // 清理函数
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isEditMode]);

  return (
    <Row gutter={16} style={{ margin: '0px', height: '400px' }}>
      {/* 左侧分栏：本班级学员 */}
      <Col span={8} style={{ height: '100%' }}>
        <Card
          title={
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <span>
                <TeamOutlined style={{ marginRight: 8 }} />
                {isSortMode ? '学员排序' : isEditMode ? '学员编辑' : '本班级学员'}
              </span>
              <div style={{ display: 'flex', gap: '4px' }}>
                <Tooltip title="刷新列表">
                  <Button
                    type="primary"
                    icon={<ReloadOutlined />}
                    onClick={handleRefreshStudents}
                    loading={studentsLoading}
                    disabled={isSortMode || isEditMode}
                    size="small"
                  />
                </Tooltip>
                <Tooltip title={isEditMode ? "退出编辑" : "编辑"}>
                  <Button
                    type={isEditMode ? "primary" : "text"}
                    icon={isEditMode ? <RollbackOutlined /> : <EditOutlined />}
                    onClick={toggleEditMode}
                    disabled={isSortMode}
                    size="small"
                  />
                </Tooltip>
                <Tooltip title={isSortMode ? "退出排序" : "调整排序"}>
                  <Button
                    type={isSortMode ? "primary" : "text"}
                    icon={<DragOutlined />}
                    onClick={toggleSortMode}
                    disabled={isEditMode}
                    size="small"
                  />
                </Tooltip>
              </div>
            </div>
          }
          style={{ height: '100%' }}
          styles={{ 
            body: {
              height: 'calc(100% - 57px)',
              overflow: 'auto',
              padding: '12px'
            }
          }}
        >
          <DndContext 
            sensors={sensors} 
            collisionDetection={closestCenter} 
            onDragEnd={handleDragEnd}
          >
            <Spin spinning={studentsLoading}>
              {clazzStudents.length === 0 ? (
                <Empty
                  description="暂无学员数据"
                  style={{ marginTop: '60px' }}
                />
              ) : (
                <SortableContext 
                  items={clazzStudents.map(student => `student-${student.id}`)} 
                  strategy={verticalListSortingStrategy}
                >
                  <div>
                    {clazzStudents.map((clazzStudent, index) => (
                      <SortableClazzStudentItem
                        key={clazzStudent.id}
                        clazzStudent={clazzStudent}
                        index={index}
                        isSortMode={isSortMode}
                        isEditMode={isEditMode}
                        onDelete={handleDeleteStudent}
                        getGenderIcon={getGenderIcon}
                      />
                    ))}
                  </div>
                </SortableContext>
              )}
            </Spin>
          </DndContext>
        </Card>
      </Col>

      {/* 右侧分栏：学员库 */}
      <Col span={16} style={{ height: '100%' }}>
        <Card
          title={
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <span>
                <DatabaseOutlined style={{ marginRight: 8 }} />
                学员库
                {!isEditMode && (
                  <span style={{ fontSize: '12px', color: '#999', marginLeft: '8px' }}>
                    （请先点击"编辑"按钮）
                  </span>
                )}
                {isEditMode && (
                  <span style={{ fontSize: '12px', color: '#999', marginLeft: '8px' }}>
                    （请输入条件缩小搜索范围）
                  </span>
                )}
              </span>
              <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                <Select
                  placeholder="性别"
                  value={searchGender}
                  onChange={setSearchGender}
                  style={{ width: 100 }}
                  size="small"
                  disabled={!isEditMode}
                  allowClear
                >
                  <Option value={0}>未知</Option>
                  <Option value={1}>男</Option>
                  <Option value={2}>女</Option>
                </Select>
                <Search
                  placeholder="搜索姓名"
                  allowClear
                  onSearch={handleNameSearch}
                  style={{ width: 120 }}
                  ref={searchNameInputRef}
                  value={searchNameValue}
                  onChange={handleNameInputChange}
                  size="small"
                  disabled={!isEditMode}
                />
                <Search
                  placeholder="搜索备注"
                  allowClear
                  onSearch={handleNotesSearch}
                  style={{ width: 120 }}
                  ref={searchNotesInputRef}
                  value={searchNotesValue}
                  onChange={handleNotesInputChange}
                  size="small"
                  disabled={!isEditMode}
                />
                <Tooltip title="重置搜索">
                  <Button
                    type="primary"
                    icon={<ReloadOutlined />}
                    onClick={handleResetLibrarySearch}
                    size="small"
                    disabled={!isEditMode}
                  />
                </Tooltip>
                {/* Flipflop按钮：全选/取消全选 */}
                {isEditMode && (
                  (() => {
                    const hasUnselectedStudents = availableStudents.filter(student => !selectedStudentIds.has(student.id)).length > 0;
                    const hasSelectedStudents = clazzStudents.length > 0;
                    
                    if (hasUnselectedStudents) {
                      // 显示全选按钮
                      return (
                        <Tooltip title="添加以下所有学员">
                          <Button
                            icon={<CheckOutlined />}
                            onClick={handleSelectAll}
                            size="small"
                            disabled={availableStudents.length === 0}
                            style={{
                              border: 'none',
                              backgroundColor: '#52c41a',
                              color: 'white',
                              boxShadow: 'none'
                            }}
                          />
                        </Tooltip>
                      );
                    } else if (hasSelectedStudents) {
                      // 显示取消全选按钮
                      return (
                        <Tooltip title="移除以下所有学员">
                          <Button
                            icon={<CloseOutlined />}
                            onClick={handleDeselectAll}
                            size="small"
                            style={{
                              border: 'none',
                              backgroundColor: '#ff4d4f',
                              color: 'white',
                              boxShadow: 'none'
                            }}
                          />
                        </Tooltip>
                      );
                    }
                    return null;
                  })()
                )}
              </div>
            </div>
          }
          style={{ 
            height: '100%',
            opacity: isEditMode ? 1 : 0.6
          }}
          styles={{ 
            body: {
              height: 'calc(100% - 57px)',
              overflow: 'auto',
              padding: '12px'
            }
          }}
        >
          <Spin spinning={libraryLoading}>
            {!isEditMode ? (
              <Empty
                description={
                  <div>
                    <div>学员库暂不可用</div>
                    <div style={{ fontSize: '12px', color: '#999', marginTop: '8px' }}>
                      请先点击左上角的"编辑"按钮进入编辑模式
                    </div>
                  </div>
                }
                style={{ marginTop: '60px' }}
              />
            ) : availableStudents.length === 0 ? (
              <Empty
                description="暂无符合条件的学员"
                style={{ marginTop: '60px' }}
              />
            ) : (
              <div>
                {availableStudents.map((student) => {
                  const isSelected = selectedStudentIds.has(student.id);
                  return (
                    <div
                      key={student.id}
                      style={{
                        border: isSelected ? '1px solid #1890ff' : '1px solid #f0f0f0',
                        borderRadius: '6px',
                        padding: '12px',
                        marginBottom: '8px',
                        cursor: isEditMode ? 'pointer' : 'not-allowed',
                        transition: 'all 0.2s',
                        backgroundColor: isSelected ? '#e6f7ff' : 'transparent',
                        opacity: isEditMode ? 1 : 0.6
                      }}
                      onMouseEnter={(e) => {
                        if (isEditMode && !isSelected) {
                          e.currentTarget.style.backgroundColor = '#f5f5f5';
                          e.currentTarget.style.borderColor = '#d9d9d9';
                        }
                      }}
                      onMouseLeave={(e) => {
                        if (isEditMode && !isSelected) {
                          e.currentTarget.style.backgroundColor = 'transparent';
                          e.currentTarget.style.borderColor = '#f0f0f0';
                        }
                      }}
                      onClick={() => {
                        if (isEditMode) {
                          handleLibraryStudentClick(student);
                        }
                      }}
                    >
                      <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                        {/* 头像 */}
                        <div style={{ 
                          width: '48px', 
                          height: '48px', 
                          borderRadius: '50%',
                          backgroundColor: '#f5f5f5',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          flexShrink: 0
                        }}>
                          <Avatar 
                            icon={<UserOutlined />} 
                            size={48} 
                            style={{ backgroundColor: '#d9d9d9', color: '#999' }}
                          />
                        </div>

                        {/* 姓名和性别 */}
                        <div style={{ 
                          flex: 1, 
                          minWidth: 0,
                          height: '48px',
                          display: 'flex',
                          flexDirection: 'column',
                          justifyContent: 'space-between'
                        }}>
                          {/* 姓名行：姓名 + 备注图标 */}
                          <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                            <Text strong style={{ 
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap',
                              fontSize: '14px',
                              lineHeight: '20px'
                            }}>
                              {student.name}
                            </Text>
                            {/* 备注图标（悬停显示备注） */}
                            {student.notes && (
                              <Tooltip title={student.notes} placement="top">
                                <CommentOutlined 
                                  style={{ 
                                    color: '#999', 
                                    fontSize: '12px',
                                    flexShrink: 0
                                  }} 
                                />
                              </Tooltip>
                            )}
                          </div>
                          {/* 性别标签行：底部对齐 */}
                          <div style={{ alignSelf: 'flex-start' }}>
                            {getGenderIcon(student.gender)}
                          </div>
                        </div>

                        {/* 选中状态图标（靠右） */}
                        {isSelected && (
                          <div style={{ display: 'flex', alignItems: 'center' }}>
                            <Tag color="success" style={{ margin: 0, fontSize: '12px' }}>
                              已选
                            </Tag>
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </Spin>
        </Card>
      </Col>
    </Row>
  );
};

export default ClazzStudentsTab; 