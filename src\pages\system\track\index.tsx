import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Card, Table, Button, Space, Input, Tooltip, Avatar, Breadcrumb, DatePicker } from 'antd';
import { ReloadOutlined, UserOutlined, HomeOutlined, FieldTimeOutlined } from '@ant-design/icons';
import type { TableProps } from 'antd/es/table';
import type { InputRef } from 'antd';
import dayjs from 'dayjs';
import { 
  getClazzes,
  type ClazzResponse
} from '../../../services/system/clazz';
import { showError } from '../../../utils/errorHandler';
import { getGlobalTenantInfo, type GlobalTenantInfo } from '../../../components/GlobalTenantSelector';
import { Link, useNavigate } from 'react-router-dom';

const { Search } = Input;
const { RangePicker } = DatePicker;

const TrackManagement: React.FC = () => {
  const [clazzes, setClazzes] = useState<ClazzResponse[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedTenantId, setSelectedTenantId] = useState<number | undefined>(undefined);
  const [searchName, setSearchName] = useState('');
  const [searchNameValue, setSearchNameValue] = useState('');
  const [timeRange, setTimeRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);
  const [tableKey, setTableKey] = useState<number>(0);

  // 分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 创建对搜索框的引用
  const searchNameInputRef = useRef<InputRef>(null);

  const navigate = useNavigate();

  // 从 GlobalTenantSelector 读取全局租户信息
  const loadGlobalTenant = useCallback(() => {
    const tenantInfo: GlobalTenantInfo | null = getGlobalTenantInfo();
    if (tenantInfo) {
      setSelectedTenantId(tenantInfo.id);
      return tenantInfo;
    } else {
      setSelectedTenantId(undefined);
      return null;
    }
  }, []);

  // 监听全局租户变化
  useEffect(() => {
    // 初始加载
    loadGlobalTenant();

    // 监听 localStorage 变化（跨标签页）
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'globalTenant') {
        loadGlobalTenant();
      }
    };

    // 监听自定义事件（同一页面内的更新）
    const handleGlobalTenantChange = (e: Event) => {
      const customEvent = e as CustomEvent;
      const { id } = customEvent.detail;
      if (id) {
        setSelectedTenantId(id);
      } else {
        setSelectedTenantId(undefined);
      }
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('globalTenantChanged', handleGlobalTenantChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('globalTenantChanged', handleGlobalTenantChange);
    };
  }, [loadGlobalTenant]);

  // 获取班级列表
  const fetchClazzes = useCallback(async () => {
    if (!selectedTenantId) {
      setClazzes([]);
      return;
    }

    try {
      setLoading(true);
      
      const response = await getClazzes({
        tenant_id: selectedTenantId,
        skip: (pagination.current - 1) * pagination.pageSize,
        limit: pagination.pageSize,
        active: 1,
        sort_by: 'id',
        sort_order: 'desc',
        ...(searchName && { name: searchName }),
        start_time: timeRange?.[0]?.toISOString(),
        end_time: timeRange?.[1]?.toISOString(),
      });
      
      setClazzes(response.items);
      setPagination(prev => ({
        ...prev,
        total: response.total
      }));
    } catch (error) {
      showError(error, '获取班级列表失败');
    } finally {
      setLoading(false);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedTenantId, searchName, timeRange, pagination.current, pagination.pageSize]);

  useEffect(() => {
    if (selectedTenantId) {
      fetchClazzes();
    }
  }, [fetchClazzes, selectedTenantId]);

  // 处理名称搜索
  const handleNameSearch = (value: string) => {
    setSearchName(value);
    setSearchNameValue(value);
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  const handleNameInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchNameValue(e.target.value);
  };

  // 处理时间范围变化
  const handleTimeRangeChange = (dates: [dayjs.Dayjs | null, dayjs.Dayjs | null] | null) => {
    if (dates && dates[0] && dates[1]) {
      setTimeRange([dates[0], dates[1]]);
    } else {
      setTimeRange(null);
    }
    // 重置分页到第一页
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  // 处理重置刷新
  const handleRefresh = async () => {
    setSearchName('');
    setSearchNameValue('');
    setTimeRange(null);

    setPagination(prev => ({
      ...prev,
      current: 1,
      pageSize: 10,
    }));

    setTableKey(prevKey => prevKey + 1);
    
    if (selectedTenantId) {
      await fetchClazzes();
    }
  };

  // 处理表格变化
  const handleTableChange: TableProps<ClazzResponse>['onChange'] = (paginationConfig) => {
    if (paginationConfig) {
      setPagination(prev => ({
        ...prev,
        current: paginationConfig.current || 1,
        pageSize: paginationConfig.pageSize || 10,
      }));
    }
  };

  // 处理跟踪
  const handleTrack = (record: ClazzResponse) => {
    // 跳转到班级练习跟踪页
    navigate(`/system/track/exercises/${record.id}`);
  };

  // 表格列定义
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 60,
    },
    {
      title: '图片',
      dataIndex: 'pic',
      key: 'pic',
      width: 60,
      render: (pic: string | null) => (
        <Avatar
          size={40}
          src={pic}
          icon={<UserOutlined />}
          shape="square"
          style={{ backgroundColor: '#f0f0f0' }}
        />
      ),
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: '25%',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: {
        showTitle: false,
      },
      render: (text: string | null) => (
        <Tooltip title={text} placement="topLeft">
          {text || '-'}
        </Tooltip>
      ),
    },
    {
      title: '开班时间',
      key: 'classTime',
      width: 200,
      render: (_: unknown, record: ClazzResponse) => {
        if (record.btime && record.etime) {
          const startTime = dayjs(record.btime).format('YYYY-MM-DD');
          const endTime = dayjs(record.etime).format('YYYY-MM-DD');
          return `${startTime} 至 ${endTime}`;
        }
        return '-';
      },
    },
    {
      title: '创建时间',
      dataIndex: 'ctime',
      key: 'ctime',
      width: 160,
      render: (text: string) => text ? dayjs(text).format('YYYY-MM-DD HH:mm') : '-',
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      render: (_: unknown, record: ClazzResponse) => (
        <Space size="middle">
          <Tooltip title="跟踪">
            <Button
              type="primary"
              style={{ backgroundColor: '#1890ff', borderColor: '#1890ff' }}
              icon={<FieldTimeOutlined />}
              size="small"
              onClick={() => handleTrack(record)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div>
      {/* 面包屑导航 */}
      <Breadcrumb 
        style={{ marginBottom: 16 }}
        items={[
          {
            title: (
              <Link to="/system/home">
                <HomeOutlined /> 主页
              </Link>
            ),
          },
          {
            title: '租户空间',
          },
          {
            title: '教学跟踪',
          },
        ]}
      />

      <Card
        title="教学跟踪"
        extra={
          <Space>
            <Search
              placeholder="搜索班级名称"
              allowClear
              onSearch={handleNameSearch}
              style={{ width: 160 }}
              ref={searchNameInputRef}
              value={searchNameValue}
              onChange={handleNameInputChange}
            />
            <RangePicker
              placeholder={['开始时间', '结束时间']}
              style={{ width: 250 }}
              value={timeRange}
              onChange={handleTimeRangeChange}
              allowClear
            />
            <Tooltip title="重置刷新">
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
              />
            </Tooltip>
          </Space>
        }
      >
        <Table
          key={tableKey}
          columns={columns}
          dataSource={clazzes}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          locale={{
            emptyText: selectedTenantId ? '暂无班级数据' : '请先在顶部导航栏选择租户'
          }}
          onChange={handleTableChange}
          sortDirections={['ascend', 'descend', 'ascend']}
          showSorterTooltip={false}
        />
      </Card>
    </div>
  );
};

export default TrackManagement; 