import React, { useState, useEffect } from 'react';
import { Drawer, Button, Input, message, Spin, Space } from 'antd';
import { EditOutlined, SaveOutlined, CloseOutlined } from '@ant-design/icons';
import { updateScene, type SceneResponse } from '../../../services/system/scene';

interface ScriptDrawerProps {
  visible: boolean;
  onClose: () => void;
  scene: SceneResponse | null;
  onSuccess: () => void;
  loading?: boolean; // 外部传入的加载状态
}

const { TextArea } = Input;

const ScriptDrawer: React.FC<ScriptDrawerProps> = ({ 
  visible, 
  onClose, 
  scene, 
  onSuccess, 
  loading: externalLoading = false 
}) => {
  const [pvScripts, setPvScripts] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [isEditing, setIsEditing] = useState<boolean>(false);
  
  // 保存原始值用于比较
  const [originalValue, setOriginalValue] = useState<string>('');

  // 当抽屉打开且有场景数据时，初始化剧情脚本内容
  useEffect(() => {
    if (visible && scene) {
      const scripts = scene.pv_scripts || '';
      setPvScripts(scripts);
      setOriginalValue(scripts);
    }
  }, [visible, scene]);

  // 开始编辑
  const handleStartEdit = () => {
    setIsEditing(true);
  };

  // 取消编辑
  const handleCancelEdit = () => {
    setPvScripts(originalValue);
    setIsEditing(false);
  };

  // 处理保存
  const handleSave = async () => {
    if (!scene) return;
    
    try {
      setLoading(true);
      
      // 检查是否有变化
      if (pvScripts === originalValue) {
        message.info('没有数据变化');
        setIsEditing(false);
        return;
      }
      
      // 调用API更新剧情脚本
      await updateScene(scene.id, { pv_scripts: pvScripts });
      
      message.success('保存成功');
      setOriginalValue(pvScripts); // 更新原始值
      setIsEditing(false); // 退出编辑状态
      onSuccess();
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败');
    } finally {
      setLoading(false);
    }
  };

  // 清空状态
  useEffect(() => {
    if (!visible) {
      setPvScripts('');
      setOriginalValue('');
      setIsEditing(false); // 关闭抽屉时重置编辑状态
    }
  }, [visible]);

  return (
    <Drawer
      title={`剧情脚本${isEditing ? '编辑' : '查看'} - ${scene?.title || ''}`}
      placement="right"
      width="70%"
      onClose={onClose}
      open={visible}
      maskClosable={true}
      extra={
        isEditing ? (
          <Space>
            <Button 
              type="primary" 
              icon={<SaveOutlined />}
              onClick={handleSave} 
              disabled={loading || externalLoading}
            >
              保存
            </Button>
            <Button 
              icon={<CloseOutlined />}
              onClick={handleCancelEdit}
              disabled={loading || externalLoading}
            >
              取消
            </Button>
          </Space>
        ) : (
          <Button 
            type="primary" 
            icon={<EditOutlined />}
            onClick={handleStartEdit}
            disabled={loading || externalLoading}
          >
            编辑
          </Button>
        )
      }
    >
      <Spin spinning={loading || externalLoading} tip={externalLoading ? "正在加载场景详情..." : "正在保存..."}>
        {externalLoading && !scene ? (
          <div style={{ height: '400px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <div>正在加载场景详情...</div>
          </div>
        ) : (
          <div style={{ height: '100%' }}>
            <TextArea
              placeholder={isEditing ? "请输入剧情脚本内容" : "暂无剧情脚本内容"}
              value={pvScripts}
              onChange={(e) => setPvScripts(e.target.value)}
              readOnly={!isEditing}
              rows={30}
              style={{ 
                height: 'calc(100vh - 120px)', 
                overflow: 'auto',
                resize: 'none',
                backgroundColor: isEditing ? '#fff' : '#f5f5f5',
                cursor: isEditing ? 'text' : 'default'
              }}
            />
          </div>
        )}
      </Spin>
    </Drawer>
  );
};

export default ScriptDrawer; 