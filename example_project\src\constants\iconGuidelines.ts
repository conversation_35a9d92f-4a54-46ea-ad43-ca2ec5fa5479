/**
 * 图标使用规范和最佳实践
 * 
 * 1. 优先使用 react-icons 中的 FontAwesome 图标
 * 2. 保持功能图标的语义一致性
 * 3. 统一图标尺寸和样式
 */

// 推荐的图标选择原则
export const ICON_GUIDELINES = {
  // 功能类图标 - 使用 FontAwesome
  COMMUNICATION: {
    mention: 'HiOutlineAtSymbol', // @ 符号用于提及/指定（更细的线条）
    voice: 'FaMicrophone',     // 麦克风用于语音输入
    send: 'FaPaperPlane',      // 纸飞机用于发送消息
    call: 'FaPhone',           // 电话用于通话
    video: 'FaVideo',          // 摄像头用于视频
  },
  
  // 导航类图标 - 使用 Ant Design（保持系统一致性）
  NAVIGATION: {
    back: 'RollbackOutlined',
    menu: 'MenuOutlined',
    close: 'CloseOutlined',
    expand: 'FullscreenOutlined',
  },
  
  // 状态类图标 - 使用 Ant Design
  STATUS: {
    loading: 'LoadingOutlined',
    success: 'CheckCircleOutlined',
    error: 'ExclamationCircleOutlined',
    warning: 'WarningOutlined',
  }
} as const;

// 图标尺寸标准
export const ICON_SIZES = {
  SMALL: '14px',
  MEDIUM: '16px',
  LARGE: '20px',
  XLARGE: '24px',
} as const;

export default {};