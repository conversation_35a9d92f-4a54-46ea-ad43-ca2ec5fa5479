import React from 'react';
import { Button, Tooltip } from 'antd';
import type { ButtonProps } from 'antd';

interface FunctionButtonProps extends Omit<ButtonProps, 'type' | 'icon'> {
  icon: React.ReactNode;
  tooltip: string;
  onClick?: () => void;
}

/**
 * 场景功能栏按钮组件
 * 统一样式和行为的可复用按钮
 */
export const FunctionButton: React.FC<FunctionButtonProps> = ({
  icon,
  tooltip,
  onClick,
  disabled = false,
  className = '',
  ...props
}) => {
  return (
    <Tooltip title={tooltip}>
      <Button
        type="text"
        icon={icon}
        onClick={onClick}
        disabled={disabled}
        className={`scene-function-button ${className}`}
        {...props}
      />
    </Tooltip>
  );
};

export default FunctionButton;