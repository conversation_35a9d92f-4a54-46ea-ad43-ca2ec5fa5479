import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Card, Table, Button, Space, Input, Tooltip, Modal, DatePicker, Breadcrumb, Select } from 'antd';
import { PlusOutlined, EditOutlined, StopOutlined, ReloadOutlined, ExclamationCircleOutlined, DeleteFilled, RollbackOutlined, UndoOutlined, HomeOutlined, EyeOutlined, BookOutlined } from '@ant-design/icons';
import type { SortOrder } from 'antd/es/table/interface';
import type { TableProps } from 'antd/es/table';
import dayjs from 'dayjs';
import { 
  getQuestions, 
  getQuestion,
  updateQuestion,
  type QuestionResponse
} from '../../../services/system/question';
import { getSubjects, type SubjectResponse } from '../../../services/system/subject';
import { showError, showSuccess } from '../../../utils/errorHandler';
import { getGlobalTenantInfo, type GlobalTenantInfo } from '../../../components/GlobalTenantSelector';
import QuestionForm from './QuestionForm';
import PromptDrawer from './PromptDrawer';
import { Link, useNavigate } from 'react-router-dom';

const { Search } = Input;
const { confirm } = Modal;
const { RangePicker } = DatePicker;

const QuestionManagement: React.FC = () => {
  const [questions, setQuestions] = useState<QuestionResponse[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [currentQuestion, setCurrentQuestion] = useState<QuestionResponse | null>(null);
  const [questionLoading, setQuestionLoading] = useState(false);
  const [selectedTenantId, setSelectedTenantId] = useState<number | undefined>(undefined);
  const [searchTitle, setSearchTitle] = useState('');
  const [searchTitleValue, setSearchTitleValue] = useState(''); // 用于存储标题输入框的当前值
  const [searchNotes, setSearchNotes] = useState('');
  const [searchNotesValue, setSearchNotesValue] = useState(''); // 用于存储备注输入框的当前值
  const [isRecycleBin, setIsRecycleBin] = useState(false);
  const [dateRange, setDateRange] = useState<[string, string] | null>(null);
  const [tableKey, setTableKey] = useState<number>(0);
  
  // 主题筛选器相关状态
  const [selectedSubjectId, setSelectedSubjectId] = useState<number | undefined>(undefined);
  const [subjects, setSubjects] = useState<SubjectResponse[]>([]);
  const [subjectsLoading, setSubjectsLoading] = useState(false);
  const [subjectsLoaded, setSubjectsLoaded] = useState(false); // 标记是否已加载主题列表
  
  const navigate = useNavigate();
  
  // 分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  
  // 排序状态
  const [sortOrders, setSortOrders] = useState<{
    [key: string]: 'asc' | 'desc' | undefined
  }>({
    id: 'desc',
    ctime: undefined
  });

  // 创建对搜索框和日期选择器的引用
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const searchTitleInputRef = useRef<any>(null);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const searchNotesInputRef = useRef<any>(null);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const datePickerRef = useRef<any>(null);

  // 从 GlobalTenantSelector 读取全局租户信息
  const loadGlobalTenant = useCallback(() => {
    const tenantInfo: GlobalTenantInfo | null = getGlobalTenantInfo();
    if (tenantInfo) {
      setSelectedTenantId(tenantInfo.id);
      return tenantInfo;
    } else {
      setSelectedTenantId(undefined);
      return null;
    }
  }, []);

  // 监听全局租户变化
  useEffect(() => {
    // 初始加载
    loadGlobalTenant();

    // 监听 localStorage 变化（跨标签页）
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'globalTenant') {
        loadGlobalTenant();
      }
    };

    // 监听自定义事件（同一页面内的更新）
    const handleGlobalTenantChange = (e: Event) => {
      const customEvent = e as CustomEvent;
      const { id } = customEvent.detail;
      if (id) {
        setSelectedTenantId(id);
      } else {
        setSelectedTenantId(undefined);
      }
      // 租户变化时重置主题状态
      setSelectedSubjectId(undefined);
      setSubjects([]);
      setSubjectsLoaded(false);
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('globalTenantChanged', handleGlobalTenantChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('globalTenantChanged', handleGlobalTenantChange);
    };
  }, [loadGlobalTenant]);

  // 获取问题列表
  const fetchQuestions = useCallback(async () => {
    if (!selectedTenantId) {
      setQuestions([]);
      return;
    }

    try {
      setLoading(true);
      
      let sortBy: 'id' | 'ctime' | undefined;
      let sortOrder: 'asc' | 'desc' | undefined;
      
      if (sortOrders.id) {
        sortBy = 'id';
        sortOrder = sortOrders.id;
      } else if (sortOrders.ctime) {
        sortBy = 'ctime';
        sortOrder = sortOrders.ctime;
      }
      
      const response = await getQuestions(selectedTenantId, {
        skip: (pagination.current - 1) * pagination.pageSize,
        limit: pagination.pageSize,
        active: isRecycleBin ? 0 : 1, // 使用active参数进行服务端过滤
        sort_by: sortBy,
        sort_order: sortOrder,
        subject_id: selectedSubjectId, // 添加主题ID筛选
        start_time: dateRange ? dateRange[0] : undefined,
        end_time: dateRange ? dateRange[1] : undefined,
        title: searchTitle || undefined, // 添加标题搜索参数
        notes: searchNotes || undefined, // 添加备注搜索参数
      });
      
      setQuestions(response.items);
      setPagination(prev => ({
        ...prev,
        total: response.total
      }));
    } catch (error) {
      showError(error, '获取问题列表失败');
    } finally {
      setLoading(false);
    }
  }, [selectedTenantId, searchTitle, searchNotes, dateRange, selectedSubjectId, sortOrders, isRecycleBin, pagination.current, pagination.pageSize]);

  useEffect(() => {
    if (selectedTenantId) {
      fetchQuestions();
    }
  }, [fetchQuestions]);



  // 处理标题搜索
  const handleTitleSearch = (value: string) => {
    setSearchTitle(value);
    setSearchTitleValue(value);
  };

  // 处理标题搜索输入框值变化，但不触发搜索
  const handleTitleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTitleValue(e.target.value);
  };

  // 处理备注搜索
  const handleNotesSearch = (value: string) => {
    setSearchNotes(value);
    setSearchNotesValue(value);
  };

  // 处理备注搜索输入框值变化，但不触发搜索
  const handleNotesInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchNotesValue(e.target.value);
  };

  // 处理日期范围变化
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleDateRangeChange = (dates: any) => {
    if (dates && dates[0] && dates[1]) {
      // 转换为ISO 8601格式，保持本地时区
      const startDate = dayjs(dates[0].format('YYYY-MM-DD 00:00:00')).format('YYYY-MM-DDTHH:mm:ss');
      const endDate = dayjs(dates[1].format('YYYY-MM-DD 23:59:59')).format('YYYY-MM-DDTHH:mm:ss');
      setDateRange([startDate, endDate]);
    } else {
      setDateRange(null);
    }
  };

  // 获取所有主题列表
  const fetchAllSubjects = async () => {
    if (!selectedTenantId || subjectsLoaded) {
      return;
    }

    try {
      setSubjectsLoading(true);
      const response = await getSubjects(selectedTenantId, {
        limit: 100, // 获取100条主题
        sort_by: 'id',
        sort_order: 'desc',
      });
      setSubjects(response.items);
      setSubjectsLoaded(true);
    } catch (error) {
      showError(error, '获取主题列表失败');
      setSubjects([]);
    } finally {
      setSubjectsLoading(false);
    }
  };

  // 处理主题选择器聚焦（点击时获取主题列表）
  const handleSubjectFocus = () => {
    fetchAllSubjects();
  };

  // 处理主题选择
  const handleSubjectChange = (value: number | undefined) => {
    setSelectedSubjectId(value);
  };

  // 切换回收站模式
  const toggleRecycleBin = () => {
    setIsRecycleBin(!isRecycleBin);
    // 重置分页状态，避免切换模式时出现显示异常
    setPagination(prev => ({
      ...prev,
      current: 1,
    }));
  };

  // 处理添加问题
  const handleAdd = () => {
    if (!selectedTenantId) {
      showError(null, '请先在顶部导航栏选择租户');
      return;
    }
    setCurrentQuestion(null);
    setModalVisible(true);
  };

  // 处理编辑问题
  const handleEdit = (record: QuestionResponse) => {
    if (!selectedTenantId) {
      showError(null, '请先在顶部导航栏选择租户');
      return;
    }
    setCurrentQuestion(record);
    setModalVisible(true);
  };

  // 处理禁用问题（将删除改为禁用）
  const handleDisable = (id: number) => {
    const actionText = '禁用';
    confirm({
      title: `确认${actionText}`,
      icon: <ExclamationCircleOutlined />,
      content: `确定要${actionText}这个问题吗？禁用后可在回收站中恢复。`,
      okText: '确认',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          // 将问题禁用（设置active为0）
          await updateQuestion(id, { active: 0 });
          showSuccess(`${actionText}成功`);
          fetchQuestions();
        } catch (error: unknown) {
          showError(error, `${actionText}问题失败`);
        }
      },
    });
  };

  // 处理恢复问题
  const handleRestore = async (questionId: number) => {
    try {
      await updateQuestion(questionId, { active: 1 });
      showSuccess('恢复成功');
      fetchQuestions();
    } catch (error) {
      showError(error, '恢复问题失败');
    }
  };

  // 表单提交成功后的回调
  const handleFormSuccess = () => {
    setModalVisible(false);
    fetchQuestions();
  };

  // 处理重置刷新
  const handleRefresh = async () => {
    // 清空搜索框
    setSearchTitle('');
    setSearchTitleValue('');

    // 清空备注搜索框
    setSearchNotes('');
    setSearchNotesValue('');

    // 清空时间区间控件
    setDateRange(null);

    // 清空主题筛选器
    setSelectedSubjectId(undefined);
    setSubjects([]);
    setSubjectsLoaded(false);

    // 重置排序为ID降序
    setSortOrders({
      id: 'desc',
      ctime: undefined
    });

    // 重置分页状态
    setPagination(prev => ({
      ...prev,
      current: 1,
      pageSize: 10,
    }));

    setTableKey(prevKey => prevKey + 1);
    
    // 数据会通过useEffect自动重新加载
  };

  // 处理表格排序变化
  const handleTableChange: TableProps<QuestionResponse>['onChange'] = (paginationConfig, _filters, sorter) => {
    if (paginationConfig) {
      setPagination(prev => ({
        ...prev,
        current: paginationConfig.current || 1,
        pageSize: paginationConfig.pageSize || 10,
      }));
    }

    interface SorterInfo {
      field?: string;
      order?: 'ascend' | 'descend';
    }

    const currentSorter = Array.isArray(sorter) ? sorter[0] : sorter;
    
    const newSortOrders: { [key: string]: "asc" | "desc" | undefined } = {
      id: undefined,
      ctime: undefined
    };

    if (currentSorter && (currentSorter as SorterInfo).field) {
      const field = (currentSorter as SorterInfo).field;
      const order = (currentSorter as SorterInfo).order;
      
      if (field === 'id') {
        if (order === 'ascend') {
          newSortOrders.id = 'asc';
        } else if (order === 'descend') {
          newSortOrders.id = 'desc';
        }
      } else if (field === 'ctime') {
        if (order === 'ascend') {
          newSortOrders.ctime = 'asc';
        } else if (order === 'descend') {
          newSortOrders.ctime = 'desc';
        }
      }
    }

    if (Object.values(newSortOrders).every(v => v === undefined)) {
      newSortOrders.id = 'desc';
    }

    setSortOrders(newSortOrders);
  };

  // 处理查看详情
  const handleViewDetail = (record: QuestionResponse) => {
    navigate(`/system/question/${record.id}`);
  };

  // 处理编辑提示词
  const handleEditPrompt = async (record: QuestionResponse) => {
    try {
      setQuestionLoading(true);
      setDrawerVisible(true);
      
      // 调用API获取完整的问题信息
      const questionDetail = await getQuestion(record.id);
      setCurrentQuestion(questionDetail);
    } catch (error) {
      showError(error, '获取问题详情失败');
      setDrawerVisible(false);
    } finally {
      setQuestionLoading(false);
    }
  };

  // 表格列定义
  const getColumns = () => {
    const baseColumns = [
      {
        title: 'ID',
        dataIndex: 'id',
        key: 'id',
        width: 80,
        sorter: true,
        sortOrder: sortOrders.id === 'asc' ? 'ascend' as SortOrder : sortOrders.id === 'desc' ? 'descend' as SortOrder : false as unknown as SortOrder,
      },
      {
        title: '标题',
        dataIndex: 'title',
        key: 'title',
        width: '30%',
        ellipsis: {
          showTitle: false,
        },
        render: (text: string) => (
          <Tooltip title={text}>
            {text}
          </Tooltip>
        ),
      },
      {
        title: '备注',
        dataIndex: 'notes',
        key: 'notes',
        width: '20%',
        ellipsis: {
          showTitle: false,
        },
        render: (text: string) => (
          <Tooltip title={text} placement="topLeft">
            {text || '-'}
          </Tooltip>
        ),
      },
      {
        title: '创建时间',
        dataIndex: 'ctime',
        key: 'ctime',
        render: (text: string) => text ? new Date(text).toLocaleString() : '-',
        sorter: true,
        sortOrder: sortOrders.ctime === 'asc' ? 'ascend' as SortOrder : sortOrders.ctime === 'desc' ? 'descend' as SortOrder : undefined,
      },
    ];

    // 根据是否为回收站模式，显示不同的操作按钮
    if (isRecycleBin) {
      // 回收站模式下的操作列
      baseColumns.push({
        title: '操作',
        key: 'action',
        width: 200,
        render: (_: unknown, record: QuestionResponse) => (
          <Space size="small">
            <Tooltip title="详情">
              <Button
                type="primary"
                icon={<EyeOutlined />}
                size="small"
                onClick={() => handleViewDetail(record)}
              />
            </Tooltip>
            <Tooltip title="编辑">
              <Button
                type="primary"
                icon={<EditOutlined />}
                size="small"
                onClick={() => handleEdit(record)}
              />
            </Tooltip>
            <Tooltip title="恢复">
              <Button
                type="primary"
                style={{ backgroundColor: '#52c41a', borderColor: '#52c41a' }}
                icon={<UndoOutlined />}
                size="small"
                onClick={() => handleRestore(record.id)}
              />
            </Tooltip>
          </Space>
        ),
      } as never);
    } else {
      // 正常模式下的操作列
      baseColumns.push({
        title: '操作',
        key: 'action',
        width: 200,
        render: (_: unknown, record: QuestionResponse) => (
          <Space size="small">
            <Tooltip title="详情">
              <Button
                type="primary"
                icon={<EyeOutlined />}
                size="small"
                onClick={() => handleViewDetail(record)}
              />
            </Tooltip>
            <Tooltip title="编辑">
              <Button
                type="primary"
                icon={<EditOutlined />}
                size="small"
                onClick={() => handleEdit(record)}
              />
            </Tooltip>
            <Tooltip title="提示词">
              <Button
                type="primary"
                style={{ backgroundColor: '#1890ff', borderColor: '#1890ff' }}
                icon={<BookOutlined />}
                size="small"
                onClick={() => handleEditPrompt(record)}
              />
            </Tooltip>
            <Tooltip title="禁用">
              <Button
                type="primary"
                style={{ backgroundColor: '#ff4d4f', borderColor: '#ff4d4f' }}
                icon={<StopOutlined />}
                size="small"
                onClick={() => handleDisable(record.id)}
              />
            </Tooltip>
          </Space>
        ),
      } as never);
    }

    return baseColumns;
  };

  return (
    <div>
      {/* 面包屑导航 */}
      <Breadcrumb 
        style={{ marginBottom: 16 }}
        items={[
          {
            title: (
              <Link to="/system/home">
                <HomeOutlined /> 主页
              </Link>
            ),
          },
          {
            title: '平台管理',
          },
          {
            title: '问题库',
          },
        ]}
      />

      <Card
        title={isRecycleBin ? "问题回收站" : "问题库"}
        extra={
          <Space>
            <Select
              placeholder="选择主题"
              allowClear
              showSearch
              loading={subjectsLoading}
              filterOption={(input, option) => {
                const label = option?.label || option?.children;
                return String(label).toLowerCase().includes(input.toLowerCase());
              }}
              onFocus={handleSubjectFocus}
              style={{ width: 160 }}
              value={selectedSubjectId}
              onChange={handleSubjectChange}
              notFoundContent={subjectsLoading ? '搜索中...' : subjects.length === 0 ? '点击获取主题列表' : '暂无数据'}
            >
              {subjects.map(subject => (
                <Select.Option key={subject.id} value={subject.id}>
                  {subject.name}
                </Select.Option>
              ))}
            </Select>
            <Search
              placeholder="搜索标题"
              allowClear
              onSearch={handleTitleSearch}
              style={{ width: 160 }}
              ref={searchTitleInputRef}
              value={searchTitleValue}
              onChange={handleTitleInputChange}
            />
            <Search
              placeholder="搜索备注"
              allowClear
              onSearch={handleNotesSearch}
              style={{ width: 160 }}
              ref={searchNotesInputRef}
              value={searchNotesValue}
              onChange={handleNotesInputChange}
            />
            <RangePicker
              onChange={handleDateRangeChange}
              placeholder={['开始时间', '结束时间']}
              style={{ width: 240 }}
              showTime={false}
              format="YYYY-MM-DD"
              ref={datePickerRef}
              value={dateRange ? [dayjs(dateRange[0]), dayjs(dateRange[1])] : null}
            />
            {/* 添加问题按钮只在非回收站模式下显示 */}
            {!isRecycleBin && selectedTenantId && (
              <Tooltip title="添加问题">
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAdd}
                />
              </Tooltip>
            )}
            <Tooltip title="重置刷新">
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
              />
            </Tooltip>
            {/* 回收站/返回按钮 */}
            {isRecycleBin ? (
              <Tooltip title="返回">
                <Button
                  type="primary"
                  icon={<RollbackOutlined />}
                  onClick={toggleRecycleBin}
                />
              </Tooltip>
            ) : (
              <Tooltip title="回收站">
                <Button
                  type="default"
                  icon={<DeleteFilled />}
                  onClick={toggleRecycleBin}
                />
              </Tooltip>
            )}
          </Space>
        }
      >
        <Table
          key={tableKey}
          columns={getColumns()}
          dataSource={questions}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          locale={{
            emptyText: selectedTenantId ? (isRecycleBin ? '回收站中没有问题' : '暂无问题数据') : '请先在顶部导航栏选择租户'
          }}
          onChange={handleTableChange}
          sortDirections={['ascend', 'descend', 'ascend']}
          showSorterTooltip={false}
        />
      </Card>

      {selectedTenantId && (
        <QuestionForm
          visible={modalVisible}
          onCancel={() => setModalVisible(false)}
          onSuccess={handleFormSuccess}
          question={currentQuestion}
          tenantId={selectedTenantId}
        />
      )}

      {/* 提示词编辑抽屉 */}
      {selectedTenantId && (
        <PromptDrawer
          visible={drawerVisible}
          onClose={() => {
            setDrawerVisible(false);
            setCurrentQuestion(null);
            setQuestionLoading(false);
          }}
          onSuccess={handleFormSuccess}
          question={currentQuestion}
          loading={questionLoading}
        />
      )}
    </div>
  );
};

export default QuestionManagement; 