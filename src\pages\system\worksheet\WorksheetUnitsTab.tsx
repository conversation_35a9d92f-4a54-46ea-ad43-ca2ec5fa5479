import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Empty, Spin, Typography, Button, message, Tooltip, Modal, Space } from 'antd';
import { FileTextOutlined, QuestionCircleOutlined, ReloadOutlined, PlusOutlined, EditOutlined, DeleteOutlined, HolderOutlined, CommentOutlined, DragOutlined } from '@ant-design/icons';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragStartEvent,
  useDroppable,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
  useSortable,
} from '@dnd-kit/sortable';
import {
  CSS,
} from '@dnd-kit/utilities';
import { 
  getWorksheetUnits, 
  getWorksheetUnitQuestions,
  batchDeleteUnits,
  batchUpdateUnitOrder,
  batchDeleteWorksheetAsms,
  batchMoveWorksheetAsms,
  batchUpdateWorksheetAsmOrder,
  type WorksheetUnitResponse,
  type WorksheetUnitQuestionResponse,
  type UnitBatchOrderRequest,
  type UnitBatchDeleteRequest,
  type WorksheetAsmBatchDeleteRequest,
  type WorksheetAsmBatchMoveRequest,
  type WorksheetAsmBatchOrderRequest
} from '../../../services/system/worksheet';
import { getGlobalTenantInfo } from '../../../components/GlobalTenantSelector';
import { showError } from '../../../utils/errorHandler';
import UnitFormDrawer from './UnitFormDrawer';
import QuestionSelectorDrawer from './QuestionSelectorDrawer';

const { Text } = Typography;

interface WorksheetUnitsTabProps {
  worksheetId?: string;
}

// 可放置的单元列表项组件
interface DroppableUnitItemProps {
  unit: WorksheetUnitResponse;
  index: number;
  isSelected: boolean;
  isSortMode?: boolean;
  isDraggingQuestion?: boolean;
  selectedUnitId?: number | null;
  onSelect: (unit: WorksheetUnitResponse) => void;
  onEdit: (unit: WorksheetUnitResponse) => void;
  onDelete: (unit: WorksheetUnitResponse) => void;
}

const DroppableUnitItem: React.FC<DroppableUnitItemProps> = ({ 
  unit, 
  index, 
  isSelected, 
  isSortMode = false,
  isDraggingQuestion = false,
  selectedUnitId,
  onSelect, 
  onEdit, 
  onDelete 
}) => {
  const {
    isOver,
    setNodeRef: setDroppableRef,
  } = useDroppable({
    id: `unit-${unit.id}`,
    disabled: isSortMode || !isDraggingQuestion, // 排序模式或非拖动问题时禁用
  });

  // 检查是否是拖拽到当前所在的单元
  const isSameUnit = selectedUnitId === unit.id && isDraggingQuestion;
  const canDrop = isOver && isDraggingQuestion && !isSameUnit;
  const cannotDrop = isOver && isDraggingQuestion && isSameUnit;

  const style = {
    cursor: 'pointer',
    backgroundColor: isSelected ? '#e6f7ff' : (canDrop ? '#f0f9ff' : (cannotDrop ? '#fff2f0' : 'transparent')),
    border: isSelected ? '1px solid #1890ff' : (canDrop ? '2px dashed #1890ff' : (cannotDrop ? '2px dashed #ff4d4f' : '1px solid transparent')),
    borderRadius: '6px',
    padding: '12px',
    marginBottom: '8px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    transition: 'all 0.2s ease',
  };

  return (
    <div
      ref={setDroppableRef}
      style={style}
      onClick={() => onSelect(unit)}
      onMouseEnter={(e) => {
        if (!isSelected && !isOver) {
          e.currentTarget.style.backgroundColor = '#f5f5f5';
        }
      }}
      onMouseLeave={(e) => {
        if (!isSelected && !isOver) {
          e.currentTarget.style.backgroundColor = 'transparent';
        }
      }}
    >
      <div style={{ display: 'flex', alignItems: 'center', flex: 1 }}>
        {isSortMode && (
          <div 
            style={{ 
              marginRight: '12px', 
              cursor: 'grab',
              color: '#999',
              fontSize: '14px'
            }}
          >
            <HolderOutlined />
          </div>
        )}
        <div style={{ 
          backgroundColor: isSelected ? '#1890ff' : '#666',
          color: 'white',
          width: '20px',
          height: '20px',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '12px',
          fontWeight: 'bold',
          marginRight: '12px'
        }}>
          {index + 1}
        </div>
                 <div style={{ flex: 1 }}>
           <Text strong={isSelected} style={{ color: isSelected ? '#1890ff' : undefined }}>
             {unit.name}
           </Text>
           {canDrop && (
             <div style={{ fontSize: '12px', color: '#1890ff', marginTop: '2px' }}>
               放置问题到此单元
             </div>
           )}
           {cannotDrop && (
             <div style={{ fontSize: '12px', color: '#ff4d4f', marginTop: '2px' }}>
               问题已在当前单元中
             </div>
           )}
         </div>
      </div>
      <Space size="small">
        <Tooltip title={isSortMode ? "排序模式下不可编辑" : "编辑单元"}>
          <Button
            type="text"
            icon={<EditOutlined />}
            size="small"
            disabled={isSortMode}
            onClick={(e) => {
              e.stopPropagation();
              onEdit(unit);
            }}
          />
        </Tooltip>
        <Tooltip title={isSortMode ? "排序模式下不可删除" : "删除单元"}>
          <Button
            type="text"
            icon={<DeleteOutlined />}
            size="small"
            danger
            disabled={isSortMode}
            onClick={(e) => {
              e.stopPropagation();
              onDelete(unit);
            }}
          />
        </Tooltip>
      </Space>
    </div>
  );
};

// 可拖拽的单元列表项组件
interface SortableUnitItemProps {
  unit: WorksheetUnitResponse;
  index: number;
  isSelected: boolean;
  isSortMode?: boolean;
  onSelect: (unit: WorksheetUnitResponse) => void;
  onEdit: (unit: WorksheetUnitResponse) => void;
  onDelete: (unit: WorksheetUnitResponse) => void;
}

const SortableUnitItem: React.FC<SortableUnitItemProps> = ({ 
  unit, 
  index, 
  isSelected, 
  isSortMode = false,
  onSelect, 
  onEdit, 
  onDelete 
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: `unit-${unit.id}` });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    cursor: 'pointer',
    backgroundColor: isSelected ? '#e6f7ff' : 'transparent',
    border: isSelected ? '1px solid #1890ff' : '1px solid transparent',
    borderRadius: '6px',
    padding: '12px',
    marginBottom: '8px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      onClick={() => onSelect(unit)}
      onMouseEnter={(e) => {
        if (!isSelected) {
          e.currentTarget.style.backgroundColor = '#f5f5f5';
        }
      }}
      onMouseLeave={(e) => {
        if (!isSelected) {
          e.currentTarget.style.backgroundColor = 'transparent';
        }
      }}
    >
      <div style={{ display: 'flex', alignItems: 'center', flex: 1 }}>
        {isSortMode && (
          <div 
            {...listeners}
            style={{ 
              marginRight: '12px', 
              cursor: 'grab',
              color: '#999',
              fontSize: '14px'
            }}
          >
            <HolderOutlined />
          </div>
        )}
        <div style={{ 
          backgroundColor: isSelected ? '#1890ff' : '#666',
          color: 'white',
          width: '20px',
          height: '20px',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '12px',
          fontWeight: 'bold',
          marginRight: '12px'
        }}>
          {index + 1}
        </div>
        <div style={{ flex: 1 }}>
          <Text strong={isSelected} style={{ color: isSelected ? '#1890ff' : undefined }}>
            {unit.name}
          </Text>
        </div>
      </div>
      <Space size="small">
        <Tooltip title={isSortMode ? "排序模式下不可编辑" : "编辑单元"}>
          <Button
            type="text"
            icon={<EditOutlined />}
            size="small"
            disabled={isSortMode}
            onClick={(e) => {
              e.stopPropagation();
              onEdit(unit);
            }}
          />
        </Tooltip>
        <Tooltip title={isSortMode ? "排序模式下不可删除" : "删除单元"}>
          <Button
            type="text"
            icon={<DeleteOutlined />}
            size="small"
            danger
            disabled={isSortMode}
            onClick={(e) => {
              e.stopPropagation();
              onDelete(unit);
            }}
          />
        </Tooltip>
      </Space>
    </div>
  );
};

// 可拖拽的问题列表项组件
interface SortableQuestionItemProps {
  question: WorksheetUnitQuestionResponse;
  index: number;
  isSelected: boolean;
  isSortMode?: boolean;
  onSelect: (questionId: number, isMultiSelect: boolean) => void;
  onDelete: (question: WorksheetUnitQuestionResponse) => void;
}

const SortableQuestionItem: React.FC<SortableQuestionItemProps> = ({ 
  question, 
  index, 
  isSelected, 
  isSortMode = false,
  onSelect, 
  onDelete 
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: `question-${question.id}` });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    cursor: isSortMode ? 'pointer' : (isSelected ? 'grab' : 'pointer'),
    backgroundColor: isSelected ? '#e6f7ff' : '#fafafa',
    border: isSelected ? '1px solid #1890ff' : '1px solid #f0f0f0',
    borderRadius: '6px',
    padding: '12px',
    marginBottom: '8px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
  };

  const handleClick = (e: React.MouseEvent) => {
    // 排序模式下不允许选中操作
    if (isSortMode) return;
    
    const isMultiSelect = e.ctrlKey || e.metaKey;
    onSelect(question.id, isMultiSelect);
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...(!isSortMode ? listeners : {})}
      onClick={handleClick}
      onMouseEnter={(e) => {
        if (!isSelected) {
          e.currentTarget.style.backgroundColor = '#f5f5f5';
        }
      }}
      onMouseLeave={(e) => {
        if (!isSelected) {
          e.currentTarget.style.backgroundColor = '#fafafa';
        }
      }}
    >
      <div style={{ display: 'flex', alignItems: 'center', flex: 1 }}>
        {isSortMode && (
          <div 
            {...listeners}
            style={{ 
              marginRight: '12px', 
              cursor: 'grab',
              color: '#999',
              fontSize: '14px'
            }}
          >
            <HolderOutlined />
          </div>
        )}
        <div style={{ 
          backgroundColor: isSelected ? '#1890ff' : '#1890ff',
          color: 'white',
          width: '20px',
          height: '20px',
          borderRadius: '2px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '12px',
          fontWeight: 'bold',
          marginRight: '12px'
        }}>
          {index + 1}
        </div>
        <div style={{ flex: 1 }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <Text strong={isSelected} style={{ color: isSelected ? '#1890ff' : undefined }}>
              {question.title}
            </Text>
            {question.notes && (
              <Tooltip title={`备注：${question.notes}`}>
                <CommentOutlined style={{ color: '#999', fontSize: '14px' }} />
              </Tooltip>
            )}
          </div>
        </div>
      </div>
      <Tooltip title={isSortMode ? "排序模式下不可删除" : "删除问题"}>
        <Button
          type="text"
          icon={<DeleteOutlined />}
          size="small"
          danger
          disabled={isSortMode}
          onClick={(e) => {
            e.stopPropagation();
            onDelete(question);
          }}
        />
      </Tooltip>
    </div>
  );
};

const WorksheetUnitsTab: React.FC<WorksheetUnitsTabProps> = ({ worksheetId }) => {
  const [units, setUnits] = useState<WorksheetUnitResponse[]>([]);
  const [questions, setQuestions] = useState<WorksheetUnitQuestionResponse[]>([]);
  const [selectedUnitId, setSelectedUnitId] = useState<number | null>(null);
  const [selectedUnitName, setSelectedUnitName] = useState<string>('');
  const [unitsLoading, setUnitsLoading] = useState(false);
  const [questionsLoading, setQuestionsLoading] = useState(false);
  
  // 问题多选状态
  const [selectedQuestionIds, setSelectedQuestionIds] = useState<number[]>([]);
  
  // 排序模式相关状态
  const [isUnitSortMode, setIsUnitSortMode] = useState(false);
  const [isQuestionSortMode, setIsQuestionSortMode] = useState(false);
  
  // 拖动状态管理
  const [isDraggingQuestion, setIsDraggingQuestion] = useState(false);
  

  
  // Drawer相关状态
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [drawerMode, setDrawerMode] = useState<'create' | 'edit'>('create');
  const [drawerUnit, setDrawerUnit] = useState<WorksheetUnitResponse | undefined>();
  
  // 问题选择器抽屉状态
  const [questionSelectorVisible, setQuestionSelectorVisible] = useState(false);
  
  // 拖拽排序传感器
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        // 允许拖拽，具体行为在handleDragEnd中控制
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // 获取当前租户ID
  const getCurrentTenantId = (): number | null => {
    const tenantInfo = getGlobalTenantInfo();
    return tenantInfo?.id || null;
  };

  // 切换单元排序模式
  const toggleUnitSortMode = () => {
    if (!isUnitSortMode && !units.length) {
      message.warning('没有可排序的单元');
      return;
    }
    setIsUnitSortMode(!isUnitSortMode);
  };

  // 切换问题排序模式
  const toggleQuestionSortMode = () => {
    if (!isQuestionSortMode) {
      if (!selectedUnitId) {
        message.warning('请先选择一个单元');
        return;
      }
      if (!questions.length) {
        message.warning('没有可排序的问题');
        return;
      }
    }
    setIsQuestionSortMode(!isQuestionSortMode);
  };

  // 获取单元列表
  const fetchUnits = async () => {
    if (!worksheetId) return;
    
    const tenantId = getCurrentTenantId();
    if (!tenantId) {
      message.error('请先选择租户');
      return;
    }

    setUnitsLoading(true);
    try {
      const response = await getWorksheetUnits(parseInt(worksheetId), tenantId);
      setUnits(response);
      
      // 如果有单元数据且没有选中的单元，自动选中第一个
      if (response.length > 0 && !selectedUnitId) {
        setSelectedUnitId(response[0].id);
        setSelectedUnitName(response[0].name);
      }
    } catch (error) {
      showError(error, '获取单元列表失败');
    } finally {
      setUnitsLoading(false);
    }
  };

  // 获取单元问题列表
  const fetchQuestions = async (unitId: number) => {
    if (!worksheetId) return;
    
    const tenantId = getCurrentTenantId();
    if (!tenantId) {
      message.error('请先选择租户');
      return;
    }

    setQuestionsLoading(true);
    try {
      const response = await getWorksheetUnitQuestions(
        parseInt(worksheetId), 
        unitId, 
        tenantId
      );
      setQuestions(response);
    } catch (error) {
      showError(error, '获取问题列表失败');
    } finally {
      setQuestionsLoading(false);
    }
  };

  // 处理单元选择
  const handleUnitSelect = (unit: WorksheetUnitResponse) => {
    setSelectedUnitId(unit.id);
    setSelectedUnitName(unit.name);
    setQuestions([]); // 清空当前问题列表
  };

  // 刷新数据
  const handleRefresh = () => {
    fetchUnits();
    if (selectedUnitId) {
      fetchQuestions(selectedUnitId);
    }
  };

  // 打开创建单元抽屉
  const handleOpenCreateDrawer = () => {
    setDrawerMode('create');
    setDrawerUnit(undefined);
    setDrawerVisible(true);
  };

  // 打开编辑单元抽屉
  const handleOpenEditDrawer = (unit: WorksheetUnitResponse) => {
    setDrawerMode('edit');
    setDrawerUnit(unit);
    setDrawerVisible(true);
  };

  // 关闭抽屉
  const handleCloseDrawer = () => {
    setDrawerVisible(false);
    setDrawerUnit(undefined);
  };

  // 抽屉操作成功回调
  const handleDrawerSuccess = async () => {
    // 重新获取单元列表
    await fetchUnits();
    
    // 如果是创建模式，选中最后一个单元（新创建的）
    if (drawerMode === 'create') {
      // 延迟一下确保数据更新完成
      setTimeout(() => {
        if (units.length > 0) {
          const lastUnit = units[units.length - 1];
          setSelectedUnitId(lastUnit.id);
          setSelectedUnitName(lastUnit.name);
        }
      }, 100);
    }
  };

  // 删除单元
  const handleDeleteUnit = (unit: WorksheetUnitResponse) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除单元"${unit.name}"吗？此操作不可撤销。`,
      okText: '确定',
      cancelText: '取消',
      okType: 'danger',
      onOk: async () => {
        try {
          const tenantId = getCurrentTenantId();
          if (!worksheetId || !tenantId) return;
          
          const deleteData: UnitBatchDeleteRequest = {
            tenant_id: tenantId,
            worksheet_id: parseInt(worksheetId),
            unit_ids: [unit.id]
          };
          
          await batchDeleteUnits(deleteData);
          message.success('单元删除成功');
          
          // 如果删除的是当前选中的单元，清空选中状态
          if (selectedUnitId === unit.id) {
            setSelectedUnitId(null);
            setSelectedUnitName('');
            setQuestions([]);
          }
          
          // 重新获取单元列表
          await fetchUnits();
        } catch (error) {
          showError(error, '删除单元失败');
        }
      }
    });
  };

  // 打开问题选择器抽屉
  const handleOpenQuestionSelector = () => {
    if (!selectedUnitId) {
      message.warning('请先选择一个单元');
      return;
    }
    setQuestionSelectorVisible(true);
  };

  // 关闭问题选择器抽屉
  const handleCloseQuestionSelector = () => {
    setQuestionSelectorVisible(false);
  };

  // 问题选择器成功回调
  const handleQuestionSelectorSuccess = () => {
    if (selectedUnitId) {
      fetchQuestions(selectedUnitId);
    }
  };

  // 处理问题选择
  const handleQuestionSelect = (questionId: number, isMultiSelect: boolean) => {
    if (isMultiSelect) {
      // 多选模式：Ctrl/Cmd + 点击，切换选中状态
      setSelectedQuestionIds(prev => {
        if (prev.includes(questionId)) {
          return prev.filter(id => id !== questionId);
        } else {
          return [...prev, questionId];
        }
      });
    } else {
      // 普通点击：如果已选中则取消选中，否则选中
      setSelectedQuestionIds(prev => {
        if (prev.includes(questionId)) {
          // 如果当前问题已选中，则取消选中
          return prev.filter(id => id !== questionId);
        } else {
          // 如果当前问题未选中，则选中（单选模式）
          return [questionId];
        }
      });
    }
  };

  // 处理拖拽开始
  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    if (active.id.toString().startsWith('question-') && !isQuestionSortMode) {
      setIsDraggingQuestion(true);
    }
  };

  // 处理拖拽结束
  const handleDragEnd = async (event: DragEndEvent) => {
    // 重置拖拽状态
    setIsDraggingQuestion(false);
    const { active, over } = event;

    if (!active.id || !over?.id || !worksheetId) return;

    const tenantId = getCurrentTenantId();
    if (!tenantId) return;

    // 解析拖拽ID，提取类型和真实ID
    const parseId = (id: string | number) => {
      const idStr = id.toString();
      if (idStr.startsWith('unit-')) {
        return { type: 'unit', id: parseInt(idStr.replace('unit-', '')) };
      } else if (idStr.startsWith('question-')) {
        return { type: 'question', id: parseInt(idStr.replace('question-', '')) };
      }
      return { type: 'unknown', id: 0 };
    };

    const activeInfo = parseId(active.id);
    const overInfo = parseId(over.id);

    // 检查是否是单元拖拽
    if (activeInfo.type === 'unit' && overInfo.type === 'unit') {
      // 单元之间的拖拽排序（只在单元排序模式下执行）
      if (isUnitSortMode && activeInfo.id !== overInfo.id) {
        const oldIndex = units.findIndex((unit) => unit.id === activeInfo.id);
        const newIndex = units.findIndex((unit) => unit.id === overInfo.id);

        const newUnits = arrayMove(units, oldIndex, newIndex);
        setUnits(newUnits);
        
        // 立即调用API更新排序
        try {
          const orderData: UnitBatchOrderRequest = {
            tenant_id: tenantId,
            worksheet_id: parseInt(worksheetId),
            units: newUnits.map((unit, index) => ({
              id: unit.id,
              priority: index + 1
            }))
          };
          
          await batchUpdateUnitOrder(orderData);
          message.success('单元排序更新成功');
        } catch (error) {
          // 如果API调用失败，恢复原始顺序
          setUnits(units);
          showError(error, '单元排序更新失败');
        }
      }
    } else if (activeInfo.type === 'question') {
      // 【问题拖拽处理】：区分移动操作和排序操作
      const activeQuestion = questions.find(q => q.id === activeInfo.id);
      
      if (activeQuestion) {
        // 【情况一：拖拽到单元上 = 移动操作】（非排序模式下允许）
        if (overInfo.type === 'unit' && !isQuestionSortMode) {
          const targetUnit = units.find(unit => unit.id === overInfo.id);
          if (!targetUnit) return;
          
          const targetUnitId = targetUnit.id;
          
          // 检查是否拖拽到当前单元
          if (targetUnitId === selectedUnitId) {
            message.info('问题已在当前单元中，无需移动');
            return;
          }
          
          // 获取要移动的问题ID列表（支持批量移动选中的问题）
          let questionIdsToMove: number[];
          if (selectedQuestionIds.includes(activeQuestion.id)) {
            // 如果拖拽的问题在选中列表中，移动所有选中的问题
            questionIdsToMove = selectedQuestionIds;
          } else {
            // 否则只移动当前拖拽的问题
            questionIdsToMove = [activeQuestion.id];
          }
          
          // 执行跨单元移动操作
          try {
            const moveData: WorksheetAsmBatchMoveRequest = {
              tenant_id: tenantId,
              worksheet_id: parseInt(worksheetId),
              target_unit_id: targetUnitId,
              asm_ids: questionIdsToMove
            };
            
            const result = await batchMoveWorksheetAsms(moveData);
            message.success(`🔄 成功移动 ${result.success_count} 个问题到"${targetUnit.name}"`);
            
            // 清空选中状态
            setSelectedQuestionIds([]);
            
            // 重新获取当前单元的问题列表
            if (selectedUnitId) {
              await fetchQuestions(selectedUnitId);
            }
          } catch (error) {
            showError(error, '移动问题失败');
          }
        } 
        // 【情况二：拖拽到问题上 = 排序操作】（只在问题排序模式下执行）
        else if (overInfo.type === 'question' && isQuestionSortMode) {
          const overQuestion = questions.find(q => q.id === overInfo.id);
          if (overQuestion && activeInfo.id !== overInfo.id) {
            const oldIndex = questions.findIndex((q) => q.id === activeInfo.id);
            const newIndex = questions.findIndex((q) => q.id === overInfo.id);
            
            const newQuestions = arrayMove(questions, oldIndex, newIndex);
            setQuestions(newQuestions);
            
            // 立即调用API更新排序
            try {
              const orderData: WorksheetAsmBatchOrderRequest = {
                tenant_id: tenantId,
                worksheet_id: parseInt(worksheetId),
                unit_id: selectedUnitId || undefined,
                items: newQuestions.map((question, index) => ({
                  id: question.id,
                  priority: index + 1
                }))
              };
              
              await batchUpdateWorksheetAsmOrder(orderData);
              message.success('📝 问题排序更新成功');
            } catch (error) {
              // 如果API调用失败，恢复原始顺序
              setQuestions(questions);
              showError(error, '问题排序更新失败');
            }
          }
        }
      }
    }
  };

  // 删除问题
  const handleDeleteQuestion = (question: WorksheetUnitQuestionResponse) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要从"${selectedUnitName}"中删除问题"${question.title}"吗？此操作不可撤销。`,
      okText: '确定',
      cancelText: '取消',
      okType: 'danger',
      onOk: async () => {
        try {
          const tenantId = getCurrentTenantId();
          if (!worksheetId || !tenantId || !selectedUnitId) return;
          
          const deleteData: WorksheetAsmBatchDeleteRequest = {
            tenant_id: tenantId,
            worksheet_id: parseInt(worksheetId),
            unit_id: selectedUnitId,
            asm_ids: [question.id]
          };
          
          await batchDeleteWorksheetAsms(deleteData);
          message.success('问题删除成功');
          
          // 重新获取问题列表
          await fetchQuestions(selectedUnitId);
        } catch (error) {
          showError(error, '删除问题失败');
        }
      }
    });
  };

  // 初始化加载数据
  useEffect(() => {
    fetchUnits();
  }, [worksheetId]);

  // 当选中的单元改变时，获取问题列表
  useEffect(() => {
    if (selectedUnitId) {
      fetchQuestions(selectedUnitId);
      // 清空问题选中状态
      setSelectedQuestionIds([]);
    }
  }, [selectedUnitId]);

  // 监听租户切换事件
  useEffect(() => {
    const handleTenantChange = () => {
      setUnits([]);
      setQuestions([]);
      setSelectedUnitId(null);
      setSelectedUnitName('');
      fetchUnits();
    };

    window.addEventListener('globalTenantChanged', handleTenantChange);
    return () => {
      window.removeEventListener('globalTenantChanged', handleTenantChange);
    };
  }, [worksheetId]);

  if (!worksheetId) {
    return (
      <div style={{ 
        height: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '40px'
      }}>
        <Empty
          description="工作表ID不可用"
          style={{ color: '#999' }}
        />
      </div>
    );
  }

  const currentTenantId = getCurrentTenantId();
  if (!currentTenantId) {
    return (
      <div style={{ 
        height: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '40px'
      }}>
        <Empty
          description="请先在顶部导航栏选择租户"
          style={{ color: '#999' }}
        />
      </div>
    );
  }

  return (
    <div style={{ height: '100%', padding: '0px' }}>
      <DndContext 
        sensors={sensors} 
        collisionDetection={closestCenter} 
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
      >
        <Row gutter={16} style={{ height: '100%' }}>
          {/* 左侧分栏：单元列表 */}
          <Col span={8} style={{ height: '100%' }}>
            <Card
              title={
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <span>
                    <FileTextOutlined style={{ marginRight: 8 }} />
                    {isUnitSortMode ? '单元排序' : '单元列表'}
                  </span>
                  <div style={{ display: 'flex', gap: '4px' }}>
                    <Tooltip title="添加单元">
                      <Button
                        type="primary"
                        icon={<PlusOutlined />}
                        onClick={handleOpenCreateDrawer}
                        disabled={isUnitSortMode}
                        size="small"
                      />
                    </Tooltip>
                    <Tooltip title="刷新列表">
                      <Button
                        type="primary"
                        icon={<ReloadOutlined />}
                        onClick={handleRefresh}
                        loading={unitsLoading}
                        disabled={isUnitSortMode}
                        size="small"
                      />
                    </Tooltip>
                    <Tooltip title={isUnitSortMode ? "退出排序" : "调整排序"}>
                      <Button
                        type={isUnitSortMode ? "primary" : "text"}
                        icon={<DragOutlined />}
                        onClick={toggleUnitSortMode}
                        size="small"
                      />
                    </Tooltip>
                  </div>
                </div>
              }
              style={{ height: '100%' }}
              styles={{ 
                body: {
                  height: 'calc(100% - 57px)',
                  overflow: 'hidden',
                  padding: '12px'
                }
              }}
            >
              <Spin spinning={unitsLoading}>
                {units.length === 0 ? (
                  <Empty
                    description="暂无单元数据"
                    style={{ marginTop: '60px' }}
                  />
                ) : (
                  isUnitSortMode ? (
                    <SortableContext 
                      items={units.map(unit => `unit-${unit.id}`)} 
                      strategy={verticalListSortingStrategy}
                    >
                      <div>
                        {units.map((unit, index) => (
                          <SortableUnitItem
                            key={unit.id}
                            unit={unit}
                            index={index}
                            isSelected={selectedUnitId === unit.id}
                            isSortMode={isUnitSortMode}
                            onSelect={handleUnitSelect}
                            onEdit={handleOpenEditDrawer}
                            onDelete={handleDeleteUnit}
                          />
                        ))}
                      </div>
                    </SortableContext>
                  ) : (
                    <div>
                      {units.map((unit, index) => (
                        <DroppableUnitItem
                          key={unit.id}
                          unit={unit}
                          index={index}
                          isSelected={selectedUnitId === unit.id}
                          isSortMode={isUnitSortMode}
                          isDraggingQuestion={isDraggingQuestion}
                          selectedUnitId={selectedUnitId}
                          onSelect={handleUnitSelect}
                          onEdit={handleOpenEditDrawer}
                          onDelete={handleDeleteUnit}
                        />
                      ))}
                    </div>
                  )
                )}
              </Spin>
            </Card>
          </Col>

          {/* 右侧分栏：问题列表 */}
          <Col span={16} style={{ height: '100%' }}>
            <Card
              title={
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <span>
                    <QuestionCircleOutlined style={{ marginRight: 8 }} />
                    {isQuestionSortMode 
                      ? `"${selectedUnitName}" 问题排序` 
                      : (selectedUnitName ? `"${selectedUnitName}" 问题列表` : '问题列表')
                    }
                  </span>
                  <div style={{ display: 'flex', gap: '4px' }}>
                    {selectedQuestionIds.length > 0 && !isQuestionSortMode && (
                      <Tooltip title={`已选中 ${selectedQuestionIds.length} 个问题`}>
                        <Button
                          type="text"
                          size="small"
                          style={{ color: '#1890ff' }}
                        >
                          已选中 {selectedQuestionIds.length} 项
                        </Button>
                      </Tooltip>
                    )}
                    <Tooltip title="编辑问题列表">
                      <Button
                        type="text"
                        icon={<EditOutlined />}
                        onClick={handleOpenQuestionSelector}
                        disabled={!selectedUnitId || isQuestionSortMode}
                        size="small"
                      />
                    </Tooltip>
                    <Tooltip title={isQuestionSortMode ? "退出排序" : "调整排序"}>
                      <Button
                        type={isQuestionSortMode ? "primary" : "text"}
                        icon={<DragOutlined />}
                        onClick={toggleQuestionSortMode}
                        disabled={!selectedUnitId}
                        size="small"
                      />
                    </Tooltip>
                  </div>
                </div>
              }
              style={{ height: '100%' }}
              styles={{ 
                body: {
                  height: 'calc(100% - 57px)',
                  overflow: 'auto',
                  padding: '12px'
                }
              }}
            >
              <Spin spinning={questionsLoading}>
                {!selectedUnitId ? (
                  <Empty
                    description="请选择左侧的单元查看对应的问题列表"
                    style={{ marginTop: '60px' }}
                  />
                ) : questions.length === 0 ? (
                  <Empty
                    description="该单元暂无问题数据"
                    style={{ marginTop: '60px' }}
                  />
                ) : (
                  <div>
                    <div style={{ marginBottom: '12px', fontSize: '12px', color: '#666' }}>
                      {isQuestionSortMode 
                        ? '排序模式：拖拽问题可调整顺序，完成后请点击"保存排序"按钮。' 
                        : '提示：点击问题可切换选中状态，拖拽问题到左侧单元可移动，按住 Ctrl/Cmd 键可多选。'
                      }
                    </div>
                    {isQuestionSortMode ? (
                      <SortableContext 
                        items={questions.map(q => `question-${q.id}`)} 
                        strategy={verticalListSortingStrategy}
                      >
                        <div>
                          {questions.map((question, index) => (
                            <SortableQuestionItem
                              key={question.id}
                              question={question}
                              index={index}
                              isSelected={selectedQuestionIds.includes(question.id)}
                              isSortMode={isQuestionSortMode}
                              onSelect={handleQuestionSelect}
                              onDelete={handleDeleteQuestion}
                            />
                          ))}
                        </div>
                      </SortableContext>
                    ) : (
                      <SortableContext 
                        items={questions.map(q => `question-${q.id}`)} 
                        strategy={verticalListSortingStrategy}
                      >
                        <div>
                          {questions.map((question, index) => (
                            <SortableQuestionItem
                              key={question.id}
                              question={question}
                              index={index}
                              isSelected={selectedQuestionIds.includes(question.id)}
                              isSortMode={isQuestionSortMode}
                              onSelect={handleQuestionSelect}
                              onDelete={handleDeleteQuestion}
                            />
                          ))}
                        </div>
                      </SortableContext>
                    )}
                  </div>
                )}
              </Spin>
            </Card>
          </Col>
        </Row>
      </DndContext>

      {/* 单元表单抽屉 */}
      <UnitFormDrawer
        visible={drawerVisible}
        mode={drawerMode}
        worksheetId={worksheetId}
        unit={drawerUnit}
        onClose={handleCloseDrawer}
        onSuccess={handleDrawerSuccess}
      />

      {/* 问题选择器抽屉 */}
      <QuestionSelectorDrawer
        visible={questionSelectorVisible}
        worksheetId={worksheetId}
        unitId={selectedUnitId || undefined}
        unitName={selectedUnitName}
        initialSelectedQuestions={questions.map(q => ({ qid: q.qid }))}
        onClose={handleCloseQuestionSelector}
        onSuccess={handleQuestionSelectorSuccess}
      />
    </div>
  );
};

export default WorksheetUnitsTab; 