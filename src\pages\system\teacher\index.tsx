import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Card, Table, Button, Space, Input, Tooltip, Tag, Breadcrumb, Avatar, DatePicker, Modal } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, ReloadOutlined, HomeOutlined, UserOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import type { SortOrder } from 'antd/es/table/interface';
import type { TableProps } from 'antd/es/table';
import dayjs from 'dayjs';
import { 
  getTeachers, 
  deleteTeacher,
  type TeacherResponse
} from '../../../services/system/teacher';
import { showError, showSuccess } from '../../../utils/errorHandler';
import { getGlobalTenantInfo, type GlobalTenantInfo } from '../../../components/GlobalTenantSelector';
import TeacherForm from './TeacherForm';
import AvatarUploader from './AvatarUploader';
import { Link } from 'react-router-dom';

const { Search } = Input;
const { confirm } = Modal;
const { RangePicker } = DatePicker;

// 性别映射
const genderMap: Record<number, { name: string; color: string }> = {
  0: { name: '未知', color: 'default' },
  1: { name: '男', color: 'blue' },
  2: { name: '女', color: 'pink' },
};

const TeacherManagement: React.FC = () => {
  const [teachers, setTeachers] = useState<TeacherResponse[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [avatarUploaderVisible, setAvatarUploaderVisible] = useState(false);
  const [currentTeacher, setCurrentTeacher] = useState<TeacherResponse | null>(null);
  const [selectedTenantId, setSelectedTenantId] = useState<number | undefined>(undefined);
  const [searchName, setSearchName] = useState('');
  const [searchNameValue, setSearchNameValue] = useState(''); // 用于存储姓名输入框的当前值
  const [searchIntro, setSearchIntro] = useState('');
  const [searchIntroValue, setSearchIntroValue] = useState(''); // 用于存储简介输入框的当前值
  const [searchNotes, setSearchNotes] = useState('');
  const [searchNotesValue, setSearchNotesValue] = useState(''); // 用于存储备注输入框的当前值
  const [timeRange, setTimeRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);

  const [tableKey, setTableKey] = useState<number>(0);
  
  // 分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  
  // 排序状态
  const [sortOrders, setSortOrders] = useState<{
    [key: string]: 'asc' | 'desc' | undefined
  }>({
    id: 'desc',
    ctime: undefined
  });

  // 创建对搜索框的引用
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const searchNameInputRef = useRef<any>(null);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const searchIntroInputRef = useRef<any>(null);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const searchNotesInputRef = useRef<any>(null);

  // 从 GlobalTenantSelector 读取全局租户信息
  const loadGlobalTenant = useCallback(() => {
    const tenantInfo: GlobalTenantInfo | null = getGlobalTenantInfo();
    if (tenantInfo) {
      setSelectedTenantId(tenantInfo.id);
      return tenantInfo;
    } else {
      setSelectedTenantId(undefined);
      return null;
    }
  }, []);

  // 监听全局租户变化
  useEffect(() => {
    // 初始加载
    loadGlobalTenant();

    // 监听 localStorage 变化（跨标签页）
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'globalTenant') {
        loadGlobalTenant();
      }
    };

    // 监听自定义事件（同一页面内的更新）
    const handleGlobalTenantChange = (e: Event) => {
      const customEvent = e as CustomEvent;
      const { id } = customEvent.detail;
      if (id) {
        setSelectedTenantId(id);
      } else {
        setSelectedTenantId(undefined);
      }
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('globalTenantChanged', handleGlobalTenantChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('globalTenantChanged', handleGlobalTenantChange);
    };
  }, [loadGlobalTenant]);

  // 获取教师列表
  const fetchTeachers = useCallback(async () => {
    if (!selectedTenantId) {
      setTeachers([]);
      return;
    }

    try {
      setLoading(true);
      
      let sortBy: 'id' | 'ctime' | undefined;
      let sortOrder: 'asc' | 'desc' | undefined;
      
      if (sortOrders.id) {
        sortBy = 'id';
        sortOrder = sortOrders.id;
      } else if (sortOrders.ctime) {
        sortBy = 'ctime';
        sortOrder = sortOrders.ctime;
      }
      
      const response = await getTeachers(selectedTenantId, {
        skip: (pagination.current - 1) * pagination.pageSize,
        limit: pagination.pageSize,
        sort_by: sortBy,
        sort_order: sortOrder,
        name: searchName || undefined, // 添加姓名搜索参数
        intro: searchIntro || undefined, // 添加简介搜索参数
        notes: searchNotes || undefined, // 添加备注搜索参数
        start_time: timeRange?.[0]?.toISOString(),
        end_time: timeRange?.[1]?.toISOString(),
      });
      
      setTeachers(response.items);
      setPagination(prev => ({
        ...prev,
        total: response.total
      }));
    } catch (error) {
      showError(error, '获取教师列表失败');
    } finally {
      setLoading(false);
    }
  }, [selectedTenantId, searchName, searchIntro, searchNotes, timeRange, sortOrders, pagination.current, pagination.pageSize]);

  useEffect(() => {
    if (selectedTenantId) {
      fetchTeachers();
    }
  }, [fetchTeachers]);

  // 处理姓名搜索
  const handleNameSearch = (value: string) => {
    setSearchName(value);
    setSearchNameValue(value);
  };

  // 处理姓名搜索输入框值变化，但不触发搜索
  const handleNameInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchNameValue(e.target.value);
  };

  // 处理简介搜索
  const handleIntroSearch = (value: string) => {
    setSearchIntro(value);
    setSearchIntroValue(value);
  };

  // 处理简介搜索输入框值变化，但不触发搜索
  const handleIntroInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchIntroValue(e.target.value);
  };

  // 处理备注搜索
  const handleNotesSearch = (value: string) => {
    setSearchNotes(value);
    setSearchNotesValue(value);
  };

  // 处理备注搜索输入框值变化，但不触发搜索
  const handleNotesInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchNotesValue(e.target.value);
  };

  // 处理时间范围变化
  const handleTimeRangeChange = (dates: [dayjs.Dayjs | null, dayjs.Dayjs | null] | null) => {
    if (dates && dates[0] && dates[1]) {
      setTimeRange([dates[0], dates[1]]);
    } else {
      setTimeRange(null);
    }
  };



  // 处理添加教师
  const handleAdd = () => {
    if (!selectedTenantId) {
      showError(null, '请先在顶部导航栏选择租户');
      return;
    }
    setCurrentTeacher(null);
    setModalVisible(true);
  };

  // 处理编辑教师
  const handleEdit = (record: TeacherResponse) => {
    if (!selectedTenantId) {
      showError(null, '请先在顶部导航栏选择租户');
      return;
    }
    setCurrentTeacher(record);
    setModalVisible(true);
  };

  // 处理删除教师
  const handleDelete = (record: TeacherResponse) => {
    confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: `确定要删除教师"${record.name}"吗？此操作不可逆。`,
      okText: '确认删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await deleteTeacher(record.id);
          showSuccess('删除成功');
          fetchTeachers();
        } catch (error: unknown) {
          showError(error, '删除教师失败');
        }
      },
    });
  };



  // 处理上传头像
  const handleUploadAvatar = (record: TeacherResponse) => {
    setCurrentTeacher(record);
    setAvatarUploaderVisible(true);
  };

  // 头像上传成功后的回调
  const handleAvatarUploadSuccess = () => {
    setAvatarUploaderVisible(false);
    fetchTeachers();
  };

  // 表单提交成功后的回调
  const handleFormSuccess = () => {
    setModalVisible(false);
    fetchTeachers();
  };

  // 处理重置刷新
  const handleRefresh = async () => {
    // 清空搜索框
    setSearchName('');
    setSearchNameValue('');

    // 清空简介搜索框
    setSearchIntro('');
    setSearchIntroValue('');

    // 清空备注搜索框
    setSearchNotes('');
    setSearchNotesValue('');

    // 重置时间范围
    setTimeRange(null);

    // 重置排序为ID降序
    setSortOrders({
      id: 'desc',
      ctime: undefined
    });

    // 重置分页状态
    setPagination(prev => ({
      ...prev,
      current: 1,
      pageSize: 10,
    }));

    setTableKey(prevKey => prevKey + 1);
    
    // 数据会通过useEffect自动重新加载
  };

  // 处理表格排序变化
  const handleTableChange: TableProps<TeacherResponse>['onChange'] = (paginationConfig, _filters, sorter) => {
    if (paginationConfig) {
      setPagination(prev => ({
        ...prev,
        current: paginationConfig.current || 1,
        pageSize: paginationConfig.pageSize || 10,
      }));
    }

    interface SorterInfo {
      field?: string;
      order?: 'ascend' | 'descend';
    }

    const currentSorter = Array.isArray(sorter) ? sorter[0] : sorter;
    
    const newSortOrders: { [key: string]: "asc" | "desc" | undefined } = {
      id: undefined,
      ctime: undefined
    };

    if (currentSorter && (currentSorter as SorterInfo).field) {
      const field = (currentSorter as SorterInfo).field;
      const order = (currentSorter as SorterInfo).order;
      
      if (field === 'id') {
        if (order === 'ascend') {
          newSortOrders.id = 'asc';
        } else if (order === 'descend') {
          newSortOrders.id = 'desc';
        }
      } else if (field === 'ctime') {
        if (order === 'ascend') {
          newSortOrders.ctime = 'asc';
        } else if (order === 'descend') {
          newSortOrders.ctime = 'desc';
        }
      }
    }

    if (Object.values(newSortOrders).every(v => v === undefined)) {
      newSortOrders.id = 'desc';
    }

    setSortOrders(newSortOrders);
  };

  // 表格列定义
  const getColumns = () => {
    const baseColumns = [
      {
        title: 'ID',
        dataIndex: 'id',
        key: 'id',
        width: 80,
        sorter: true,
        sortOrder: sortOrders.id === 'asc' ? 'ascend' as SortOrder : sortOrders.id === 'desc' ? 'descend' as SortOrder : false as unknown as SortOrder,
      },
      {
        title: '头像',
        dataIndex: 'avatar',
        key: 'avatar',
        width: 80,
        render: (avatar: string | null, record: TeacherResponse) => (
          <div
            style={{ cursor: 'pointer' }}
            onClick={() => handleUploadAvatar(record)}
          >
            <Avatar
              size={40}
              src={avatar}
              icon={<UserOutlined />}
              shape="circle"
              style={{ backgroundColor: '#f0f0f0' }}
            />
          </div>
        ),
      },
      {
        title: '姓名',
        dataIndex: 'name',
        key: 'name',
        width: 80,
        ellipsis: {
          showTitle: false,
        },
        render: (text: string) => (
          <Tooltip title={text} placement="topLeft">
            {text}
          </Tooltip>
        ),
      },
      {
        title: '性别',
        dataIndex: 'gender',
        key: 'gender',
        width: 80,
        render: (gender: number) => {
          const genderInfo = genderMap[gender] || { name: '未知', color: 'default' };
          return <Tag color={genderInfo.color}>{genderInfo.name}</Tag>;
        },
      },
      {
        title: '简介',
        dataIndex: 'intro',
        key: 'intro',
        width: '35%',
        ellipsis: {
          showTitle: false,
        },
        render: (text: string) => (
          <Tooltip title={text} placement="topLeft">
            {text || '-'}
          </Tooltip>
        ),
      },
      {
        title: '备注',
        dataIndex: 'notes',
        key: 'notes',
        width: '15%',
        ellipsis: {
          showTitle: false,
        },
        render: (text: string) => (
          <Tooltip title={text} placement="topLeft">
            {text || '-'}
          </Tooltip>
        ),
      },
      {
        title: '创建时间',
        dataIndex: 'ctime',
        key: 'ctime',
        width: 160,
        sorter: true,
        sortOrder: sortOrders.ctime === 'asc' ? 'ascend' as SortOrder : sortOrders.ctime === 'desc' ? 'descend' as SortOrder : false as unknown as SortOrder,
        render: (text: string) => new Date(text).toLocaleString(),
      },
    ];

    // 操作列
    const actionColumn = {
      title: '操作',
      dataIndex: '',
      key: 'action',
      width: 150,
      render: (_text: unknown, record: TeacherResponse) => (
        <Space size="small">
          <Tooltip title="编辑">
            <Button 
              type="primary"
              icon={<EditOutlined />} 
              onClick={() => handleEdit(record)}
              size="small"
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button 
              type="text"
              danger
              icon={<DeleteOutlined />} 
              onClick={() => handleDelete(record)}
              size="small"
              style={{ backgroundColor: '#ff4d4f', color: 'white' }}
            />
          </Tooltip>
        </Space>
      ),
    };
    baseColumns.push(actionColumn);

    return baseColumns;
  };

  return (
    <div>
      {/* 面包屑导航 */}
      <Breadcrumb 
        style={{ marginBottom: 16 }}
        items={[
          {
            title: (
              <Link to="/system/home">
                <HomeOutlined /> 主页
              </Link>
            ),
          },
          {
            title: '租户空间',
          },
          {
            title: '老师管理',
          },
        ]}
      />

      <Card
        title="老师管理"
        extra={
          <Space>
            <Search
              placeholder="搜索姓名"
              allowClear
              onSearch={handleNameSearch}
              style={{ width: 160 }}
              ref={searchNameInputRef}
              value={searchNameValue}
              onChange={handleNameInputChange}
            />
            <Search
              placeholder="搜索简介"
              allowClear
              onSearch={handleIntroSearch}
              style={{ width: 160 }}
              ref={searchIntroInputRef}
              value={searchIntroValue}
              onChange={handleIntroInputChange}
            />
            <Search
              placeholder="搜索备注"
              allowClear
              onSearch={handleNotesSearch}
              style={{ width: 160 }}
              ref={searchNotesInputRef}
              value={searchNotesValue}
              onChange={handleNotesInputChange}
            />
            <RangePicker
              placeholder={['开始时间', '结束时间']}
              style={{ width: 250 }}
              value={timeRange}
              onChange={handleTimeRangeChange}
              allowClear
            />
            {/* 添加教师按钮 */}
            {selectedTenantId && (
              <Tooltip title="添加教师">
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAdd}
                />
              </Tooltip>
            )}
            <Tooltip title="重置刷新">
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
              />
            </Tooltip>
          </Space>
        }
      >
        <Table
          key={tableKey}
          columns={getColumns()}
          dataSource={teachers}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          locale={{
            emptyText: selectedTenantId ? '暂无教师数据' : '请先在顶部导航栏选择租户'
          }}
          onChange={handleTableChange}
          sortDirections={['ascend', 'descend', 'ascend']}
          showSorterTooltip={false}
        />
      </Card>

      {selectedTenantId && (
        <TeacherForm
          visible={modalVisible}
          onCancel={() => setModalVisible(false)}
          onSuccess={handleFormSuccess}
          teacher={currentTeacher}
          tenantId={selectedTenantId}
        />
      )}

      {/* 头像上传弹窗 */}
      {selectedTenantId && currentTeacher && (
        <AvatarUploader
          visible={avatarUploaderVisible}
          onCancel={() => setAvatarUploaderVisible(false)}
          onSuccess={handleAvatarUploadSuccess}
          teacherId={currentTeacher.id}
          tenantId={selectedTenantId}
          currentAvatar={currentTeacher.avatar}
        />
      )}
    </div>
  );
};

export default TeacherManagement; 