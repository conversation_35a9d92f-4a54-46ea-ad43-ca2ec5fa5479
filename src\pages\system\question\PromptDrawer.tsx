import React, { useState, useEffect } from 'react';
import { Drawer, Button, Input, message, Spin, Tabs } from 'antd';
import { updateQuestion, type QuestionResponse } from '../../../services/system/question';

interface PromptDrawerProps {
  visible: boolean;
  onClose: () => void;
  question: QuestionResponse | null;
  onSuccess: () => void;
  loading?: boolean; // 外部传入的加载状态
}

const { TextArea } = Input;

const PromptDrawer: React.FC<PromptDrawerProps> = ({ visible, onClose, question, onSuccess, loading: externalLoading = false }) => {
  const [pvSkills, setPvSkills] = useState<string>('');
  const [pvRules, setPvRules] = useState<string>('');
  const [pvFormats, setPvFormats] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<string>('skills');
  
  // 保存原始值用于比较
  const [originalValues, setOriginalValues] = useState<{
    pvSkills: string;
    pvRules: string;
    pvFormats: string;
  }>({ pvSkills: '', pvRules: '', pvFormats: '' });

  // 当抽屉打开且有问题数据时，初始化提示词内容
  useEffect(() => {
    if (visible && question) {
      const skills = question.pv_skills || '';
      const rules = question.pv_rules || '';
      const formats = question.pv_formats || '';
      
      setPvSkills(skills);
      setPvRules(rules);
      setPvFormats(formats);
      
      // 保存原始值
      setOriginalValues({
        pvSkills: skills,
        pvRules: rules,
        pvFormats: formats
      });
    }
  }, [visible, question]);

  // 处理保存
  const handleSave = async () => {
    if (!question) return;
    
    try {
      setLoading(true);
      
      // 只更新发生变化的字段
      const updateData: Partial<{
        pv_skills: string;
        pv_rules: string;
        pv_formats: string;
      }> = {};
      
      if (pvSkills !== originalValues.pvSkills) {
        updateData.pv_skills = pvSkills;
      }
      
      if (pvRules !== originalValues.pvRules) {
        updateData.pv_rules = pvRules;
      }
      
      if (pvFormats !== originalValues.pvFormats) {
        updateData.pv_formats = pvFormats;
      }
      
      // 如果没有字段发生变化，则不调用接口
      if (Object.keys(updateData).length === 0) {
        message.info('没有数据变化');
        onClose();
        return;
      }
      
      // 调用API更新提示词
      await updateQuestion(question.id, updateData);
      
      message.success('保存成功');
      onSuccess();
      onClose();
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败');
    } finally {
      setLoading(false);
    }
  };

  // 清空状态
  useEffect(() => {
    if (!visible) {
      setPvSkills('');
      setPvRules('');
      setPvFormats('');
      setActiveTab('skills');
      setOriginalValues({ pvSkills: '', pvRules: '', pvFormats: '' });
    }
  }, [visible]);

  const tabItems = [
    {
      key: 'skills',
      label: '点评能力',
      children: (
        <TextArea
          placeholder="请输入点评能力提示词"
          value={pvSkills}
          onChange={(e) => setPvSkills(e.target.value)}
          rows={20}
          style={{ height: 'calc(100vh - 250px)', overflow: 'auto' }}
        />
      ),
    },
    {
      key: 'rules',
      label: '点评细则',
      children: (
        <TextArea
          placeholder="请输入点评细则提示词"
          value={pvRules}
          onChange={(e) => setPvRules(e.target.value)}
          rows={20}
          style={{ height: 'calc(100vh - 250px)', overflow: 'auto' }}
        />
      ),
    },
    {
      key: 'formats',
      label: '点评格式',
      children: (
        <TextArea
          placeholder="请输入点评格式提示词"
          value={pvFormats}
          onChange={(e) => setPvFormats(e.target.value)}
          rows={20}
          style={{ height: 'calc(100vh - 250px)', overflow: 'auto' }}
        />
      ),
    },
  ];

  return (
    <Drawer
      title={`提示词编辑 - ${question?.title || ''}`}
      placement="right"
      width="70%"
      onClose={onClose}
      open={visible}
      maskClosable={true}
      extra={
        <Button type="primary" onClick={handleSave} disabled={loading || externalLoading}>
          保存
        </Button>
      }
    >
      <Spin spinning={loading || externalLoading} tip={externalLoading ? "正在加载问题详情..." : "正在保存..."}>
        {externalLoading && !question ? (
          <div style={{ height: '400px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <div>正在加载问题详情...</div>
          </div>
        ) : (
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            items={tabItems}
            style={{ height: '100%' }}
          />
        )}
      </Spin>
    </Drawer>
  );
};

export default PromptDrawer; 