.profile-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0;
}

.profile-header {
  margin-bottom: 32px;
}

.profile-header .ant-typography {
  margin-bottom: 8px;
}



.profile-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.profile-card {
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.profile-avatar-section {
  display: flex;
  align-items: center;
  gap: 24px;
  padding: 8px 0;
}

.profile-avatar {
  background-color: rgb(19 52 59) !important;
  border: 4px solid #fff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.profile-basic-info {
  flex: 1;
}

.profile-name {
  margin-bottom: 4px !important;
  color: #000 !important;
  font-weight: 600;
}

.profile-username {
  color: #666;
  font-size: 16px;
}

.profile-details-card {
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.profile-section {
  padding: 8px 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.section-header .ant-typography {
  margin-bottom: 0 !important;
  color: #000 !important;
  font-weight: 600;
}

.edit-button {
  color: #666 !important;
  padding: 4px 8px;
  height: auto;
  font-size: 14px;
  transition: all 0.3s;
}

.edit-button:hover {
  color: #000 !important;
  background-color: #f5f5f5;
}

/* 统一的按钮样式 - 取消、不保存、编辑按钮 */
.custom-button {
  border: 1px solid rgb(33, 128, 141) !important;
  color: rgb(33, 128, 141) !important;
  background: white !important;
  transition: all 0.3s ease !important;
}

.custom-button:hover {
  border: 1px solid rgb(33, 128, 141) !important;
  color: rgb(33, 128, 141) !important;
  background: rgba(33, 128, 141, 0.04) !important;
  box-shadow: none !important;
}

.custom-button:focus {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  color: rgb(33, 128, 141) !important;
  background: white !important;
}

.custom-button:active {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  color: rgb(33, 128, 141) !important;
  background: white !important;
}

.info-grid {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f5f5f5;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #666;
  font-size: 14px;
  font-weight: 500;
  min-width: 120px;
}

.info-icon {
  font-size: 16px;
  color: #999;
  width: 16px;
  text-align: center;
}

.info-value {
  color: #000;
  font-size: 14px;
  font-weight: 500;
  text-align: right;
}

/* 密码修改模态框样式 */
.password-modal .ant-modal-header {
  padding: 12px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.password-modal .ant-modal-body {
  padding: 16px 24px;
}

.password-modal .ant-modal-title {
  font-size: 16px;
  line-height: 1.4;
  font-weight: 600;
  color: #000;
}

.password-modal .ant-form-item-label > label {
  color: #000;
  font-weight: 500;
}

.password-modal .ant-input-password {
  border-radius: 6px;
}

.confirm-password-button {
  background-color: rgb(33, 128, 141) !important;
  border-color: rgb(33, 128, 141) !important;
  color: white !important;
}

.confirm-password-button:hover {
  background-color: rgba(33, 128, 141, 0.8) !important;
  border-color: rgba(33, 128, 141, 0.8) !important;
  color: white !important;
}

.confirm-password-button:focus {
  background-color: rgb(33, 128, 141) !important;
  border-color: rgb(33, 128, 141) !important;
  color: white !important;
  box-shadow: 0 0 0 2px rgba(33, 128, 141, 0.2) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .profile-container {
    padding: 0 16px;
  }
  
  .profile-avatar-section {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }
  
  .profile-avatar {
    align-self: center;
  }
  
  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .info-label {
    min-width: auto;
  }
  
  .info-value {
    text-align: left;
    width: 100%;
  }
}

/* 加载状态优化 */
.profile-card .ant-spin-container {
  min-height: 200px;
}

/* 卡片悬停效果 */
.profile-card:hover,
.profile-details-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

/* 表单验证错误样式优化 */
.password-modal .ant-form-item-has-error .ant-input-password {
  border-color: #ff4d4f;
}

.password-modal .ant-form-item-has-error .ant-form-item-explain-error {
  color: #ff4d4f;
}