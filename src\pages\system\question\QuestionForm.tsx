import React, { useState, useEffect } from 'react';
import { Modal, Form, Input } from 'antd';
import { 
  createQuestion, 
  updateQuestion,
  type QuestionResponse,
  type QuestionCreate,
  type QuestionUpdate 
} from '../../../services/system/question';
import { showError, showSuccess } from '../../../utils/errorHandler';

interface QuestionFormProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  question: QuestionResponse | null;
  tenantId: number;
}

const QuestionForm: React.FC<QuestionFormProps> = ({ visible, onCancel, onSuccess, question, tenantId }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (visible) {
      if (question) {
        form.setFieldsValue({
          title: question.title,
          notes: question.notes,
        });
      } else {
        form.resetFields();
      }
    }
  }, [visible, question, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      if (question) {
        const updateData: QuestionUpdate = {
          title: values.title,
          notes: values.notes,
        };
        
        await updateQuestion(question.id, updateData);
        showSuccess('更新问题成功');
      } else {
        const createData: QuestionCreate = {
          tenant_id: tenantId,
          title: values.title,
          notes: values.notes,
        };
        await createQuestion(createData);
        showSuccess('创建问题成功');
      }

      form.resetFields();
      onSuccess();
    } catch (error: unknown) {
      showError(error, '操作失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title={question ? '编辑问题' : '添加问题'}
      open={visible}
      onCancel={handleCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
      destroyOnHidden
      width={520}
    >
      <Form
        form={form}
        layout="vertical"
      >
        <Form.Item
          name="title"
          label="标题"
          rules={[{ required: true, message: '请输入标题' }]}
        >
          <Input placeholder="请输入标题" />
        </Form.Item>

        <Form.Item
          name="notes"
          label="备注"
        >
          <Input.TextArea 
            placeholder="请输入备注" 
            rows={4}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default QuestionForm; 