import React, { useState, useEffect } from 'react';
import { Space, Tag, Typography, theme, Button, Select, message, Row, Col, Card, Skeleton } from 'antd';
import { EditOutlined, AppstoreOutlined, BookOutlined, SaveOutlined, CloseOutlined } from '@ant-design/icons';
import { 
  getQuestionTags,
  batchUpdateQuestionModules,
  batchUpdateQuestionSubjects
} from '../../../services/system/question';
import { getFrameworks, type FrameworkResponse } from '../../../services/system/framework';
import { getModules, type ModuleResponse } from '../../../services/system/module';
import { getSubjects, type SubjectResponse } from '../../../services/system/subject';
import { showError } from '../../../utils/errorHandler';

const { Text } = Typography;

interface QuestionTagsTabProps {
  tenantId: number | undefined;
  questionId: string | undefined;
  selectedModuleIds: number[];
  selectedSubjectIds: number[];
  setSelectedModuleIds: (ids: number[]) => void;
  setSelectedSubjectIds: (ids: number[]) => void;
}

const QuestionTagsTab: React.FC<QuestionTagsTabProps> = ({
  tenantId,
  questionId,
  selectedModuleIds,
  selectedSubjectIds,
  setSelectedModuleIds,
  setSelectedSubjectIds
}) => {
  const { token } = theme.useToken();
  
  // 状态管理
  const [moduleEditMode, setModuleEditMode] = useState(false);
  const [moduleEditLoading, setModuleEditLoading] = useState(false);
  const [subjectEditMode, setSubjectEditMode] = useState(false);
  const [subjectEditLoading, setSubjectEditLoading] = useState(false);
  const [frameworkModulesLoading, setFrameworkModulesLoading] = useState(false);
  const [tagsDataLoading, setTagsDataLoading] = useState(false);
  const [frameworks, setFrameworks] = useState<FrameworkResponse[]>([]);
  const [frameworkModules, setFrameworkModules] = useState<ModuleResponse[]>([]);
  const [selectedFrameworkId, setSelectedFrameworkId] = useState<number | undefined>();
  const [tempSelectedModuleIds, setTempSelectedModuleIds] = useState<number[]>([]);
  const [tempSelectedSubjectIds, setTempSelectedSubjectIds] = useState<number[]>([]);
  
  // 本地数据状态
  const [allModules, setAllModules] = useState<ModuleResponse[]>([]);
  const [allSubjects, setAllSubjects] = useState<SubjectResponse[]>([]);
  
  // 问题关联的模块和主题数据（包含名称）
  const [questionModulesData, setQuestionModulesData] = useState<{
    id: number;
    mid: number;
    module_name: string;
  }[]>([]);
  const [questionSubjectsData, setQuestionSubjectsData] = useState<{
    id: number;
    sid: number;
    subject_name: string;
  }[]>([]);

  // 获取框架列表
  const fetchFrameworks = async () => {
    if (!tenantId) return;
    try {
      const response = await getFrameworks(tenantId, { active: 1 });
      setFrameworks(response.items);
    } catch (error) {
      showError(error, '获取框架列表失败');
    }
  };

  // 获取框架下的模块
  const fetchFrameworkModules = async (frameworkId: number) => {
    if (!tenantId) return;
    setFrameworkModulesLoading(true);
    try {
      const response = await getModules({ 
        tenant_id: tenantId, 
        framework_id: frameworkId,
        active: 1 
      });
      setFrameworkModules(response.items);
    } catch (error) {
      showError(error, '获取模块列表失败');
      setFrameworkModules([]);
    } finally {
      setFrameworkModulesLoading(false);
    }
  };

  // 获取所有模块
  const fetchAllModules = async () => {
    if (!tenantId) return;
    try {
      const response = await getModules({
        tenant_id: tenantId,
        active: 1,
        limit: 100
      });
      setAllModules(response.items);
    } catch (error) {
      showError(error, '获取模块列表失败');
    }
  };

  // 获取所有主题
  const fetchAllSubjects = async () => {
    if (!tenantId) return;
    try {
      const response = await getSubjects(tenantId, {
        limit: 100
      });
      setAllSubjects(response.items);
    } catch (error) {
      showError(error, '获取主题列表失败');
    }
  };

  // 获取问题标签关系
  const fetchQuestionTags = async () => {
    if (!tenantId || !questionId) return;
    setTagsDataLoading(true);
    try {
      const response = await getQuestionTags({
        tenant_id: tenantId,
        question_id: parseInt(questionId)
      });
      
      // 存储完整的模块和主题数据（使用类型断言，因为接口返回的数据包含 module_name 和 subject_name）
      setQuestionModulesData((response.modules as unknown as typeof questionModulesData) || []);
      setQuestionSubjectsData((response.subjects as unknown as typeof questionSubjectsData) || []);
      
      // 提取模块ID和主题ID
      const moduleIds = response.modules.map(m => m.mid);
      const subjectIds = response.subjects.map(s => s.sid);
      
      setSelectedModuleIds(moduleIds);
      setSelectedSubjectIds(subjectIds);
    } catch (error) {
      showError(error, '获取问题标签失败');
    } finally {
      setTagsDataLoading(false);
    }
  };

  // 保存模块关系
  const saveModuleRelations = async () => {
    if (!tenantId || !questionId) return;
    
    setModuleEditLoading(true);
    try {
      await batchUpdateQuestionModules({
        tenant_id: tenantId,
        question_id: parseInt(questionId),
        module_ids: tempSelectedModuleIds
      });
      
      setSelectedModuleIds(tempSelectedModuleIds);
      // 重新获取问题标签数据以更新显示
      await fetchQuestionTags();
      setModuleEditMode(false);
      message.success('理论模块保存成功');
    } catch (error) {
      showError(error, '保存模块关系失败');
    } finally {
      setModuleEditLoading(false);
    }
  };

  // 保存主题关系
  const saveSubjectRelations = async () => {
    if (!tenantId || !questionId) return;
    
    setSubjectEditLoading(true);
    try {
      await batchUpdateQuestionSubjects({
        tenant_id: tenantId,
        question_id: parseInt(questionId),
        subject_ids: tempSelectedSubjectIds
      });
      
      setSelectedSubjectIds(tempSelectedSubjectIds);
      // 重新获取问题标签数据以更新显示
      await fetchQuestionTags();
      setSubjectEditMode(false);
      message.success('主题保存成功');
    } catch (error) {
      showError(error, '保存主题关系失败');
    } finally {
      setSubjectEditLoading(false);
    }
  };

  // 取消主题编辑
  const cancelSubjectEdit = () => {
    setTempSelectedSubjectIds([...selectedSubjectIds]);
    setSubjectEditMode(false);
  };

  // 取消模块编辑
  const cancelModuleEdit = () => {
    setTempSelectedModuleIds([...selectedModuleIds]);
    setSelectedFrameworkId(undefined);
    setFrameworkModules([]);
    setModuleEditMode(false);
  };

  // 每次切换到标签tab时自动请求数据
  useEffect(() => {
    if (tenantId && questionId) {
      // 使用新的API获取问题关联的模块和主题数据
      fetchQuestionTags();
    }
  }, [tenantId, questionId]);

  // 监听ESC键退出编辑状态
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        if (subjectEditMode) {
          cancelSubjectEdit();
        } else if (moduleEditMode) {
          cancelModuleEdit();
        }
      }
    };

    // 添加键盘事件监听器
    document.addEventListener('keydown', handleKeyDown);

    // 清理函数，移除事件监听器
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [subjectEditMode, moduleEditMode]); // 依赖编辑状态，确保能正确取消

  return (
    <div style={{ 
      height: '100%',
      overflow: 'auto'
    }}>
      {tenantId && questionId ? (
        <div style={{ maxWidth: '1200px' }}>
          <Row gutter={24}>
            {/* 主题区块 */}
            <Col span={12}>
              <Card 
                title={
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <BookOutlined style={{ color: token.colorTextSecondary }} />
                      <Text strong style={{ fontSize: '16px', color: token.colorText }}>
                        主题
                      </Text>
                    </div>
                    {subjectEditMode ? (
                      <Space size="small">
                        <Button 
                          type="primary" 
                          icon={<SaveOutlined />}
                          size="small"
                          loading={subjectEditLoading}
                          onClick={saveSubjectRelations}
                        />
                        <Button 
                          icon={<CloseOutlined />}
                          size="small"
                          onClick={cancelSubjectEdit}
                          disabled={subjectEditLoading}
                        />
                      </Space>
                    ) : (
                      <Button 
                        type="default" 
                        icon={<EditOutlined />}
                        size="small"
                        onClick={async () => {
                          if (!tenantId) return;
                          setSubjectEditLoading(true);
                          try {
                            setTempSelectedSubjectIds([...selectedSubjectIds]);
                            // 先加载数据，再进入编辑模式
                            await fetchAllSubjects();
                            setSubjectEditMode(true);
                          } catch (error) {
                            showError(error, '加载数据失败');
                          } finally {
                            setSubjectEditLoading(false);
                          }
                        }}
                      />
                    )}
                  </div>
                }
                variant="borderless"
                style={{ height: '100%', minHeight: '200px' }}
                styles={{ body: { minHeight: '120px' } }}
              >
                {subjectEditMode ? (
                  // 编辑模式：显示多选控件
                  <Select
                    mode="multiple"
                    style={{ width: '100%' }}
                    placeholder="请选择相关主题"
                    value={tempSelectedSubjectIds}
                    onChange={setTempSelectedSubjectIds}
                    showSearch
                    filterOption={(input, option) =>
                      (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                    }
                    options={allSubjects.map(subject => ({
                      label: subject.name,
                      value: subject.id
                    }))}
                    loading={subjectEditLoading}
                  />
                ) : tagsDataLoading ? (
                  // 加载状态：显示骨架屏
                  <Skeleton active paragraph={{ rows: 2, width: ['60%', '40%'] }} />
                ) : (
                  // 显示模式：显示已选择的主题标签
                  questionSubjectsData.length > 0 ? (
                    <Space size={[8, 8]} wrap>
                      {questionSubjectsData.map(subject => (
                        <Tag 
                          key={subject.id}
                          style={{ 
                            margin: '2px',
                            borderRadius: '4px',
                            fontSize: '14px',
                            padding: '4px 12px',
                            backgroundColor: 'transparent',
                            color: '#1890ff',
                            border: '1px solid #1890ff'
                          }}
                        >
                          {subject.subject_name}
                        </Tag>
                      ))}
                    </Space>
                  ) : (
                    <Text type="secondary" style={{ 
                      fontSize: '13px',
                      color: token.colorTextTertiary
                    }}>
                      暂未关联任何主题
                    </Text>
                  )
                )}
              </Card>
            </Col>

            {/* 理论模块区块 */}
            <Col span={12}>
              <Card 
                title={
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <AppstoreOutlined style={{ color: token.colorTextSecondary }} />
                      <Text strong style={{ fontSize: '16px', color: token.colorText }}>
                        理论模块
                      </Text>
                    </div>
                    {moduleEditMode ? (
                      <Space size="small">
                        <Button 
                          type="primary" 
                          icon={<SaveOutlined />}
                          size="small"
                          loading={moduleEditLoading}
                          onClick={saveModuleRelations}
                        />
                        <Button 
                          icon={<CloseOutlined />}
                          size="small"
                          onClick={cancelModuleEdit}
                          disabled={moduleEditLoading}
                        />
                      </Space>
                    ) : (
                      <Button 
                        type="default" 
                        icon={<EditOutlined />}
                        size="small"
                        onClick={async () => {
                          if (!tenantId) return;
                          setModuleEditLoading(true);
                          try {
                            setTempSelectedModuleIds([...selectedModuleIds]);
                            // 先加载数据，再进入编辑模式
                            await Promise.all([
                              fetchFrameworks(),
                              fetchAllModules()
                            ]);
                            setModuleEditMode(true);
                          } catch (error) {
                            showError(error, '加载数据失败');
                          } finally {
                            setModuleEditLoading(false);
                          }
                        }}
                      />
                    )}
                  </div>
                }
                variant="borderless"
                style={{ height: '100%', minHeight: '200px' }}
                styles={{ body: { minHeight: '120px' } }}
              >
                {moduleEditMode ? (
                  // 编辑模式：显示框架和模块选择控件
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                    {/* 理论框架选择 */}
                    <div>
                      <Text strong style={{ display: 'block', marginBottom: '8px' }}>理论框架</Text>
                      <Select
                        style={{ width: '100%' }}
                        placeholder="请选择理论框架"
                        value={selectedFrameworkId}
                        onChange={(frameworkId) => {
                          setSelectedFrameworkId(frameworkId);
                          // 清空之前的模块数据，避免显示上一个框架的模块
                          setFrameworkModules([]);
                          setFrameworkModulesLoading(false);
                          if (frameworkId) {
                            fetchFrameworkModules(frameworkId);
                          }
                        }}
                        allowClear
                        showSearch
                        filterOption={(input, option) =>
                          (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                        }
                        options={frameworks.map(framework => ({
                          label: framework.name,
                          value: framework.id
                        }))}
                        loading={moduleEditLoading}
                      />
                    </div>
                    
                    {/* 理论模块选择 */}
                    <div>
                      <Text strong style={{ display: 'block', marginBottom: '8px' }}>理论模块</Text>
                      <Select
                        mode="multiple"
                        style={{ width: '100%' }}
                        placeholder={frameworkModulesLoading ? "加载中..." : selectedFrameworkId ? "请选择理论模块" : "请先选择理论框架"}
                        disabled={!selectedFrameworkId}
                        loading={frameworkModulesLoading}
                        showSearch
                        filterOption={(input, option) =>
                          (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                        }
                        value={tempSelectedModuleIds.filter(id => 
                          frameworkModules.some(module => module.id === id)
                        )}
                        onChange={(moduleIds) => {
                          // 移除当前框架下的已选模块，然后加上新选的
                          const otherFrameworkModules = tempSelectedModuleIds.filter(id => 
                            !frameworkModules.some(module => module.id === id)
                          );
                          setTempSelectedModuleIds([...otherFrameworkModules, ...moduleIds]);
                        }}
                        options={frameworkModules.map(module => ({
                          label: module.name,
                          value: module.id
                        }))}
                      />
                    </div>
                    
                    {/* 已选择的模块标签展示 */}
                    {tempSelectedModuleIds.length > 0 && (
                      <div>
                        <Text strong style={{ display: 'block', marginBottom: '8px' }}>已选择模块</Text>
                        <Space size={[8, 8]} wrap>
                          {tempSelectedModuleIds.map(moduleId => {
                            const module = allModules.find(m => m.id === moduleId);
                            return module ? (
                              <Tag 
                                key={moduleId}
                                closable
                                onClose={() => {
                                  setTempSelectedModuleIds(tempSelectedModuleIds.filter(id => id !== moduleId));
                                }}
                                style={{ 
                                  margin: '2px',
                                  borderRadius: '4px',
                                  fontSize: '14px',
                                  padding: '4px 12px',
                                  backgroundColor: 'transparent',
                                  color: '#1890ff',
                                  border: '1px solid #1890ff'
                                }}
                              >
                                {module.name}
                              </Tag>
                            ) : null;
                          })}
                        </Space>
                      </div>
                    )}
                  </div>
                ) : tagsDataLoading ? (
                  // 加载状态：显示骨架屏
                  <Skeleton active paragraph={{ rows: 2, width: ['60%', '40%'] }} />
                ) : (
                  // 显示模式：显示已选择的理论模块标签
                  questionModulesData.length > 0 ? (
                    <Space size={[8, 8]} wrap>
                      {questionModulesData.map(module => (
                        <Tag 
                          key={module.id}
                          style={{ 
                            margin: '2px',
                            borderRadius: '4px',
                            fontSize: '14px',
                            padding: '4px 12px',
                            backgroundColor: 'transparent',
                            color: '#1890ff',
                            border: '1px solid #1890ff'
                          }}
                        >
                          {module.module_name}
                        </Tag>
                      ))}
                    </Space>
                  ) : (
                    <Text type="secondary" style={{ 
                      fontSize: '13px',
                      color: token.colorTextTertiary
                    }}>
                      暂未关联任何理论模块
                    </Text>
                  )
                )}
              </Card>
            </Col>
          </Row>
        </div>
      ) : (
        <div style={{ 
          textAlign: 'center', 
          color: token.colorTextSecondary,
          padding: '60px 0'
        }}>
          <Text type="secondary" style={{ fontSize: '16px' }}>
            请先选择租户和题目
          </Text>
        </div>
      )}

    </div>
  );
};

export default QuestionTagsTab; 