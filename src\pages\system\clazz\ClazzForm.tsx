import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, DatePicker, message } from 'antd';
import dayjs from 'dayjs';
import { 
  createClazz, 
  updateClazz, 
  type ClazzResponse, 
  type ClazzCreate, 
  type ClazzUpdate 
} from '../../../services/system/clazz';

const { TextArea } = Input;
const { RangePicker } = DatePicker;

interface ClazzFormProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  clazz: ClazzResponse | null;
  tenantId: number;
}

interface FormData {
  name: string;
  description?: string;
  notes?: string;
  btime?: string;
  etime?: string;
  tenant_id: number;
}

const ClazzForm: React.FC<ClazzFormProps> = ({ visible, onCancel, onSuccess, clazz, tenantId }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (visible) {
      if (clazz) {
        // 编辑模式，填充表单数据
        const timeRange = [];
        if (clazz.btime && clazz.etime) {
          timeRange.push(dayjs(clazz.btime));
          timeRange.push(dayjs(clazz.etime));
        }

        form.setFieldsValue({
          name: clazz.name,
          description: clazz.description,
          notes: clazz.notes,
          timeRange: timeRange.length === 2 ? timeRange : undefined,
        });
      } else {
        // 新增模式，清空表单
        form.resetFields();
      }
    }
  }, [visible, clazz, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      // 构建提交数据
      const formData: FormData = {
        tenant_id: tenantId,
        name: values.name,
        description: values.description,
        notes: values.notes,
      };

      // 处理时间范围
      if (values.timeRange && values.timeRange.length === 2) {
        formData.btime = values.timeRange[0].toISOString();
        formData.etime = values.timeRange[1].toISOString();
      }

      console.log('发送的班级数据:', formData);

      if (clazz) {
        // 编辑模式
        const updateData: ClazzUpdate = {
          name: formData.name,
          description: formData.description,
          btime: formData.btime,
          etime: formData.etime,
          notes: formData.notes,
        };
        await updateClazz(clazz.id, updateData, tenantId);
        message.success('班级更新成功！');
      } else {
        // 创建模式 - 确保时间字段必填
        if (!formData.btime || !formData.etime) {
          message.error('开始时间和结束时间为必填项');
          return;
        }
        const createData: ClazzCreate = {
          tenant_id: formData.tenant_id,
          name: formData.name,
          btime: formData.btime,
          etime: formData.etime,
          description: formData.description,
          notes: formData.notes,
        };
        await createClazz(createData);
        message.success('班级创建成功！');
      }

      onSuccess();
    } catch (error) {
      console.error('提交失败:', error);
      message.error('操作失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title={clazz ? '编辑班级' : '新增班级'}
      open={visible}
      onOk={handleSubmit}
      onCancel={handleCancel}
      confirmLoading={loading}
      width={600}
      destroyOnHidden
    >
      <Form
        form={form}
        layout="vertical"
        preserve={false}
      >
        <Form.Item
          name="name"
          label="名称"
          rules={[
            { required: true, message: '请输入班级名称' },
            { max: 100, message: '班级名称不能超过100个字符' },
          ]}
        >
          <Input placeholder="请输入班级名称" />
        </Form.Item>

        <Form.Item
          name="timeRange"
          label="开班时间"
          rules={[
            { required: true, message: '请选择班级开始时间和结束时间' },
            { 
              validator: (_, value) => {
                if (value && value.length === 2) {
                  if (value[0].isAfter(value[1])) {
                    return Promise.reject(new Error('开始时间不能晚于结束时间'));
                  }
                }
                return Promise.resolve();
              }
            }
          ]}
        >
          <RangePicker
            placeholder={['开始时间', '结束时间']}
            style={{ width: '100%' }}
            format="YYYY-MM-DD"
          />
        </Form.Item>

        <Form.Item
          name="description"
          label="描述"
          rules={[
            { max: 500, message: '描述不能超过500个字符' },
          ]}
        >
          <TextArea 
            placeholder="请输入班级描述"
            rows={3}
            showCount
            maxLength={500}
          />
        </Form.Item>

        <Form.Item
          name="notes"
          label="备注"
          rules={[
            { max: 500, message: '备注不能超过500个字符' },
          ]}
        >
          <TextArea 
            placeholder="请输入备注信息"
            rows={3}
            showCount
            maxLength={500}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ClazzForm; 