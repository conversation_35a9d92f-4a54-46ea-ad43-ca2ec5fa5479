import { useRef, useEffect, useState, forwardRef } from 'react';
import { Scrollbar } from 'react-scrollbars-custom';
import type { ScrollbarProps } from 'react-scrollbars-custom';

interface AutoHideScrollbarProps extends ScrollbarProps {
  autoHideTimeout?: number; // 自动隐藏的延迟时间（毫秒）
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const AutoHideScrollbar = forwardRef<any, AutoHideScrollbarProps>(({
  autoHideTimeout = 1000, // 默认1秒后隐藏
  children,
  ...props
}, ref) => {
  const [isScrolling, setIsScrolling] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 处理滚动事件
  const handleScrollStart = () => {
    setIsScrolling(true);
    
    // 清除之前的定时器
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  };

  // 处理滚动停止事件
  const handleScrollStop = () => {
    // 设置定时器，延迟隐藏滚动条
    timeoutRef.current = setTimeout(() => {
      if (!isHovered) {
        setIsScrolling(false);
      }
    }, autoHideTimeout);
  };

  // 处理鼠标进入事件
  const handleMouseEnter = () => {
    setIsHovered(true);
    setIsScrolling(true);
    
    // 清除之前的定时器
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  };

  // 处理鼠标离开事件
  const handleMouseLeave = () => {
    setIsHovered(false);
    
    // 设置定时器，延迟隐藏滚动条
    timeoutRef.current = setTimeout(() => {
      setIsScrolling(false);
    }, autoHideTimeout);
  };

  // 组件卸载时清除定时器
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return (
    <Scrollbar
      ref={ref}
      {...props}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onScrollStart={handleScrollStart}
      onScrollStop={handleScrollStop}
      trackYProps={{
        style: {
          width: 6,
          right: 2,
          bottom: 2,
          top: 2,
          borderRadius: 3,
          opacity: isScrolling ? 1 : 0,
          transition: 'opacity 0.2s ease'
        }
      }}
      thumbYProps={{
        style: {
          background: '#c1c1c1',
          borderRadius: 3,
          width: 6,
          opacity: isScrolling ? 1 : 0,
          transition: 'opacity 0.2s ease'
        }
      }}
      trackXProps={{
        style: {
          height: 6,
          left: 2,
          right: 2,
          bottom: 2,
          borderRadius: 3,
          opacity: isScrolling ? 1 : 0,
          transition: 'opacity 0.2s ease'
        }
      }}
      thumbXProps={{
        style: {
          background: '#c1c1c1',
          borderRadius: 3,
          height: 6,
          opacity: isScrolling ? 1 : 0,
          transition: 'opacity 0.2s ease'
        }
      }}
    >
      {children}
    </Scrollbar>
  );
});

AutoHideScrollbar.displayName = 'AutoHideScrollbar';

export default AutoHideScrollbar;