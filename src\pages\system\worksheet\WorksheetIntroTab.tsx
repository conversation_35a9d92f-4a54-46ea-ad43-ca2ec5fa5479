import { useState, useEffect, useImperativeHandle, forwardRef, useCallback } from 'react';
import { Form, message, Button, Space, Tooltip, theme, Modal } from 'antd';
import { SaveOutlined, CloseOutlined } from '@ant-design/icons';
import type { FormInstance } from 'antd';
import { updateWorksheet } from '../../../services/system/worksheet';
import { showError } from '../../../utils/errorHandler';

import { Input } from 'antd';
const { TextArea } = Input;

interface WorksheetIntroTabProps {
  form: FormInstance;
  worksheetId?: number;
  initialIntro?: string;
  onSaveSuccess?: (updatedIntro: string) => void;
}

export interface WorksheetIntroTabRef {
  hasUnsavedChanges: () => boolean;
  saveContent: () => Promise<void>;
  resetToInitial: () => void;
}

const WorksheetIntroTab = forwardRef<WorksheetIntroTabRef, WorksheetIntroTabProps>(
  ({ form, worksheetId, initialIntro = '', onSaveSuccess }, ref) => {
    const { token } = theme.useToken();
    const [currentContent, setCurrentContent] = useState<string>(initialIntro);
    const [initialContent, setInitialContent] = useState<string>(initialIntro);
    const [editingIntro, setEditingIntro] = useState(false);
    const [showCancelModal, setShowCancelModal] = useState(false);

    // 同步内容到表单（仅在必要时）
    const syncToForm = useCallback((content: string) => {
      form.setFieldValue('intro', content);
    }, [form]);

    // 当初始内容变化时更新状态
    useEffect(() => {
      setCurrentContent(initialIntro);
      setInitialContent(initialIntro);
      // 同步更新表单值，但只在初始化时
      syncToForm(initialIntro);
    }, [initialIntro, syncToForm]);

    // 检查是否有未保存的更改
    const hasUnsavedChanges = () => {
      return currentContent !== initialContent;
    };

    // 处理内容变化
    const handleContentChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
      setCurrentContent(e.target.value);
    }, []);

    // 处理聚焦事件
    const handleFocus = useCallback(() => {
      setEditingIntro(true);
    }, []);

    // 保存简介内容
    const handleSaveIntro = useCallback(async () => {
      if (!worksheetId) {
        message.error('作业单ID不存在，无法保存');
        return;
      }

      try {
        await updateWorksheet(worksheetId, { intro: currentContent });
        // 保存成功后同步所有状态
        setInitialContent(currentContent);  // 更新初始内容
        syncToForm(currentContent);         // 同步到表单
        setEditingIntro(false);
        message.success('简介已保存');
        if (onSaveSuccess) {
          onSaveSuccess(currentContent);
        }
      } catch (error) {
        showError(error, '保存简介失败');
        throw error; // 重新抛出错误以便调用者知道保存失败
      }
    }, [worksheetId, currentContent, syncToForm, onSaveSuccess]);

    // 取消编辑 - 检查是否有未保存更改
    const handleCancelEdit = useCallback(() => {
      if (currentContent !== initialContent) {
        // 有未保存更改，显示确认对话框
        setShowCancelModal(true);
      } else {
        // 没有更改，直接取消
        setEditingIntro(false);
      }
    }, [currentContent, initialContent]);

    // 确认取消编辑（不保存）
    const handleConfirmCancel = useCallback(() => {
      setCurrentContent(initialContent);
      syncToForm(initialContent);
      setEditingIntro(false);
      setShowCancelModal(false);
    }, [initialContent, syncToForm]);

    // 保存并取消编辑
    const handleSaveAndCancel = useCallback(async () => {
      try {
        await handleSaveIntro();
        setEditingIntro(false);
        setShowCancelModal(false);
      } catch {
        // 保存失败，不关闭对话框
      }
    }, [handleSaveIntro]);

    // 重置到初始内容
    const resetToInitial = useCallback(() => {
      setCurrentContent(initialContent);
      syncToForm(initialContent);
      setEditingIntro(false);
    }, [initialContent, syncToForm]);

    // 公开的保存方法
    const saveContent = useCallback(async () => {
      await handleSaveIntro();
    }, [handleSaveIntro]);

    // 暴露方法给父组件
    useImperativeHandle(ref, () => ({
      hasUnsavedChanges,
      saveContent,
      resetToInitial
    }));

    // 处理ESC键
    const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLTextAreaElement>) => {
      if (e.key === 'Escape') {
        handleCancelEdit();
      }
    }, [handleCancelEdit]);

    return (
      <div style={{ 
        height: '100%', 
        display: 'flex', 
        flexDirection: 'column',
        padding: '0px',
        minHeight: 0
      }}>
        {/* 富文本编辑器样式的容器 */}
        <div style={{ 
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          border: `1px solid ${token.colorBorder}`,
          borderRadius: token.borderRadius,
          overflow: 'hidden',
          backgroundColor: '#fff',
          boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02)',
          minHeight: 0
        }}>
          {/* 顶部标题栏 - 完全模仿富文本编辑器样式 */}
          <div style={{ 
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            padding: '8px 12px',
            borderBottom: `1px solid ${token.colorBorder}`,
            backgroundColor: token.colorFillAlter,
            flexShrink: 0
          }}>
            <div style={{ 
              fontSize: '14px', 
              color: token.colorTextSecondary
            }}>
              简介
            </div>
            <Space>
              {/* 只有在内容发生变化时才显示保存和取消按钮 */}
              {editingIntro && currentContent !== initialContent && (
                <>
                  <Tooltip title="保存简介">
                    <Button
                      type="primary"
                      size="small"
                      icon={<SaveOutlined />}
                      onClick={handleSaveIntro}
                    />
                  </Tooltip>
                  <Tooltip title="取消编辑">
                    <Button
                      type="text"
                      size="small"
                      icon={<CloseOutlined />}
                      onClick={handleCancelEdit}
                    />
                  </Tooltip>
                </>
              )}
            </Space>
          </div>
          
          {/* 编辑区域 */}
          <div style={{ 
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden',
            minHeight: 0,
            width: '100%'
          }}>
            <Form
              form={form}
              layout="vertical"
              style={{
                width: '100%',
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                minHeight: 0,
                flex: 1
              }}
            >
              <Form.Item
                name="intro"
                style={{
                  height: '100%',
                  width: '100%',
                  flex: 1,
                  marginBottom: 0,
                  display: 'flex',
                  flexDirection: 'column',
                  minHeight: 0
                }}
              >
                <TextArea
                  value={currentContent}
                  onChange={handleContentChange}
                  onFocus={handleFocus}
                  placeholder="请输入作业单简介内容..."
                  style={{ 
                    flex: 1,
                    height: '100%',
                    border: 'none',
                    borderRadius: '0',
                    resize: 'none',
                    fontSize: '14px',
                    lineHeight: '1.6',
                    padding: '16px',
                    backgroundColor: '#fff',
                    boxShadow: 'none',
                    outline: 'none'
                  }}
                  onKeyDown={handleKeyDown}
                />
              </Form.Item>
            </Form>
          </div>
        </div>

        {/* 取消编辑确认对话框 */}
        <Modal
          title="未保存的更改"
          open={showCancelModal}
          onCancel={() => setShowCancelModal(false)}
          footer={[
            <Button key="continue" onClick={() => setShowCancelModal(false)}>
              继续编辑
            </Button>,
            <Button key="discard" onClick={handleConfirmCancel}>
              不保存
            </Button>,
            <Button key="save" type="primary" onClick={handleSaveAndCancel}>
              保存
            </Button>,
          ]}
          closable={false}
          maskClosable={false}
        >
          <p>您有未保存的简介内容更改，是否要保存？</p>
        </Modal>
      </div>
    );
  }
);

WorksheetIntroTab.displayName = 'WorksheetIntroTab';

export default WorksheetIntroTab; 