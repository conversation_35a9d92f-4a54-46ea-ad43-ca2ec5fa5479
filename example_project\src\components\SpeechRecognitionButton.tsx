import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Tooltip, Badge, message } from 'antd';
import { AudioOutlined, LoadingOutlined } from '@ant-design/icons';
import { speechRecognitionService, type CountdownInfo } from '../services/speechService';

interface SpeechRecognitionButtonProps {
  onTranscript: (text: string) => void;
  className?: string;
}

const SpeechRecognitionButton: React.FC<SpeechRecognitionButtonProps> = ({ 
  onTranscript, 
  className = '' 
}) => {
  const [isSupported, setIsSupported] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [isPreparing, setIsPreparing] = useState(false);
  const [tempResult, setTempResult] = useState('');
  const [countdownInfo, setCountdownInfo] = useState<CountdownInfo | null>(null);

  useEffect(() => {
    setIsSupported(speechRecognitionService.isRecognitionSupported());
  }, []);

  const handleStartListening = () => {
    if (isListening || isPreparing) {
      // 如果正在监听或准备中，点击停止
      speechRecognitionService.stopListening();
      setIsListening(false);
      setIsPreparing(false);
      setTempResult('');
      setCountdownInfo(null);
      return;
    }

    speechRecognitionService.startListening(
      (result) => {
        if (result.isFinal) {
          // 最终结果
          onTranscript(result.transcript);
          setTempResult('');
        } else {
          // 临时结果 - 立即显示
          setTempResult(result.transcript);
        }
      },
      (error) => {
        message.error(error);
        setIsListening(false);
        setIsPreparing(false);
        setTempResult('');
        setCountdownInfo(null);
      },
      () => {
        // 识别结束
        setIsListening(false);
        setIsPreparing(false);
        setTempResult('');
        setCountdownInfo(null);
      },
      () => {
        // 准备中状态
        setIsPreparing(true);
        setIsListening(false);
        setTempResult('');
        setCountdownInfo(null);
      },
      () => {
        // 开始监听状态
        setIsPreparing(false);
        setIsListening(true);
      },
      {
        language: 'zh-CN',
        continuous: true,
        interimResults: true,
        noSpeechTimeout: 5000 // 5秒无语音超时
      },
      (countdown) => {
        // 倒计时回调
        setCountdownInfo(countdown);
      }
    );
  };

  // 获取状态文本
  const getStatusText = () => {
    if (isPreparing) {
      return '准备中';
    }
    if (isListening) {
      if (countdownInfo) {
        return (
          <span>
            识别中：没有检测到声音，将在
            <strong style={{ fontWeight: 'bold', color: '#ff4d4f' }}>
              {countdownInfo.remainingSeconds}
            </strong>
            秒后结束！
          </span>
        );
      }
      if (tempResult) {
        return `识别中：${tempResult}`;
      }
      return '识别中';
    }
    return '';
  };

  // 获取tooltip文本
  const getTooltipText = () => {
    if (isListening || isPreparing) {
      return '结束语音输入';
    }
    return '语音输入';
  };

  // 动态生成按钮类名
  const getButtonClass = () => {
    let buttonClass = `scene-function-button speech-recognition-button ${className}`;
    
    if (isPreparing) {
      buttonClass += ' preparing';
    } else if (isListening) {
      buttonClass += ' listening';
    }
    
    return buttonClass;
  };

  if (!isSupported) {
    return (
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        <Tooltip title="浏览器不支持语音识别">
          <Button 
            className={`scene-function-button ${className}`}
            disabled
            icon={<AudioOutlined style={{ color: '#cccccc' }} />}
          />
        </Tooltip>
      </div>
    );
  }

  const statusText = getStatusText();

  return (
    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
      <Tooltip title={getTooltipText()}>
        <Badge 
          dot={!!tempResult && tempResult.trim().length > 0} 
          color="#52c41a"
        >
          <Button
            className={getButtonClass()}
            onClick={handleStartListening}
            icon={
              isPreparing ? (
                <LoadingOutlined />
              ) : (
                <AudioOutlined />
              )
            }
          />
        </Badge>
      </Tooltip>
      {statusText && (
        <div style={{ 
          fontSize: '12px', 
          color: '#666',
          whiteSpace: 'nowrap',
          maxWidth: countdownInfo ? '300px' : '120px', // 倒计时时增加宽度
          overflow: 'hidden',
          textOverflow: 'ellipsis'
        }}>
          {statusText}
        </div>
      )}
    </div>
  );
};

export default SpeechRecognitionButton;