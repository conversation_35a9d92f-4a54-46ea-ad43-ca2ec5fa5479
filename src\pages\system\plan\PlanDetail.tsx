import React, { useState, useEffect, useCallback, useRef } from 'react';
import { 
  Form,
  Input, 
  Button, 
  Space, 
  Breadcrumb, 
  Spin, 
  Row,
  Col,
  Typography,
  theme,
  message,
  Tooltip,
  Card,
  Modal,
  Tag,
  Select,
  Empty
} from 'antd';
import { 
  HomeOutlined, 
  EditOutlined,
  SaveOutlined,
  CloseOutlined,
  ReloadOutlined,
  DragOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
  RollbackOutlined,
  BookOutlined,
  DatabaseOutlined,
  InfoCircleOutlined,
  LinkOutlined,
  DisconnectOutlined,
  HolderOutlined,
  CommentOutlined
} from '@ant-design/icons';
import { useParams, useNavigate, Link } from 'react-router-dom';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
  useSortable,
} from '@dnd-kit/sortable';
import {
  CSS,
} from '@dnd-kit/utilities';

import { 
  getPlan, 
  updatePlan,
  getPlanExercises,
  createPlanExercise,
  updatePlanExercise,
  deletePlanExercise,
  batchUpdatePlanExerciseOrder,
  type PlanResponse,
  type PlanExerciseResponse,
  type PlanExerciseCreate,
  type PlanExerciseUpdate,
  type PlanExerciseBatchOrderRequest
} from '../../../services/system/plan';
import { getExercises, type ExerciseResponse } from '../../../services/system/exercise';
import { showError } from '../../../utils/errorHandler';
import { getGlobalTenantInfo } from '../../../components/GlobalTenantSelector';
import '../../../styles/detail-page.css';

const { Title, Text } = Typography;
const { TextArea, Search } = Input;
const { Option } = Select;
const { confirm } = Modal;

// 可拖拽的练习项组件
interface SortablePlanExerciseItemProps {
  planExercise: PlanExerciseResponse;
  index: number;
  isSortMode?: boolean;
  isEditMode?: boolean;
  onDelete: (planExerciseId: number) => void;
  onToggleDepend: (planExercise: PlanExerciseResponse) => void;
  getExerciseTypeTag: (type: number) => JSX.Element;
}

const SortablePlanExerciseItem: React.FC<SortablePlanExerciseItemProps> = ({
  planExercise,
  index,
  isSortMode = false,
  isEditMode = false,
  onDelete,
  onToggleDepend,
  getExerciseTypeTag
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: `exercise-${planExercise.id}` });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition: transition || 'all 0.2s',
    opacity: isDragging ? 0.5 : 1,
    backgroundColor: 'transparent',
    border: '1px solid #f0f0f0',
    borderRadius: '6px',
    padding: '12px',
    marginBottom: '8px',
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      onMouseEnter={(e) => {
        e.currentTarget.style.backgroundColor = '#f5f5f5';
        e.currentTarget.style.borderColor = '#d9d9d9';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.backgroundColor = 'transparent';
        e.currentTarget.style.borderColor = '#f0f0f0';
      }}
    >
      <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
        {isSortMode && (
          <div 
            {...listeners}
            style={{ 
              cursor: 'grab',
              color: '#999',
              fontSize: '14px'
            }}
          >
            <HolderOutlined />
          </div>
        )}

        {/* 序号 */}
        <div style={{ 
          backgroundColor: '#666',
          color: 'white',
          width: '20px',
          height: '20px',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '12px',
          fontWeight: 'bold',
          flexShrink: 0
        }}>
          {index + 1}
        </div>

        {/* 图片 */}
        <div style={{ 
          width: '48px', 
          height: '48px', 
          borderRadius: '4px',
          overflow: 'hidden',
          backgroundColor: '#f5f5f5',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexShrink: 0
        }}>
          {planExercise.pic ? (
            <img 
              src={planExercise.pic} 
              alt={planExercise.title}
              style={{ 
                width: '100%', 
                height: '100%', 
                objectFit: 'cover' 
              }}
              onError={(e) => {
                e.currentTarget.style.display = 'none';
                e.currentTarget.parentElement!.innerHTML = '<div style="width: 100%; height: 100%; background: #f0f0f0; display: flex; align-items: center; justify-content: center; color: #ccc; font-size: 12px;">无图</div>';
              }}
            />
          ) : (
            <div style={{ color: '#ccc', fontSize: '12px' }}>无图</div>
          )}
        </div>

        {/* 标题和类型 */}
        <div style={{ 
          flex: 1, 
          minWidth: 0,
          height: '48px',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between'
        }}>
          {/* 标题行：标题 + info图标 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
            <Text strong style={{ 
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              fontSize: '14px',
              lineHeight: '20px'
            }}>
              {planExercise.title}
            </Text>
            {/* 信息图标（悬停显示简介） */}
            {planExercise.intro && (
              <Tooltip title={planExercise.intro} placement="top">
                <InfoCircleOutlined 
                  style={{ 
                    color: '#999', 
                    fontSize: '12px',
                    flexShrink: 0
                  }} 
                />
              </Tooltip>
            )}
          </div>
          {/* 类型标签行：底部对齐 */}
          <div style={{ alignSelf: 'flex-start' }}>
            {getExerciseTypeTag(planExercise.type)}
          </div>
        </div>

        {/* 依赖图标（靠右） */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
          <Tooltip title={
            isEditMode 
              ? `点击${planExercise.depend === 1 ? '取消' : '设置'}依赖` 
              : (planExercise.depend === 1 ? '依赖前一个练习' : '不依赖')
          }>
            <div
              style={{ 
                cursor: isEditMode ? 'pointer' : 'default',
                padding: '4px',
                borderRadius: '4px',
                transition: 'background-color 0.2s',
              }}
              onMouseEnter={(e) => {
                if (isEditMode) {
                  e.currentTarget.style.backgroundColor = '#f5f5f5';
                }
              }}
              onMouseLeave={(e) => {
                if (isEditMode) {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }
              }}
              onClick={() => {
                if (isEditMode) {
                  onToggleDepend(planExercise);
                }
              }}
            >
              {planExercise.depend === 1 ? (
                <LinkOutlined style={{ color: '#1677ff', fontSize: '14px' }} />
              ) : (
                <DisconnectOutlined style={{ color: '#ff4d4f', fontSize: '14px' }} />
              )}
            </div>
          </Tooltip>
          
          {/* 编辑状态下的删除按钮 */}
          {isEditMode && (
            <Tooltip title="删除">
              <Button
                type="text"
                icon={<DeleteOutlined />}
                size="small"
                danger
                onClick={() => onDelete(planExercise.id)}
                style={{ marginLeft: '4px' }}
              />
            </Tooltip>
          )}
        </div>
      </div>
    </div>
  );
};

const PlanDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [plan, setPlan] = useState<PlanResponse | null>(null);
  const [initialLoading, setInitialLoading] = useState(true);
  const { token } = theme.useToken();
  
  // 拖拽排序传感器
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );
  
  // 内联编辑状态
  const [editingName, setEditingName] = useState(false);
  const [editingDescription, setEditingDescription] = useState(false);
  const [editingNotes, setEditingNotes] = useState(false);
  const [tempName, setTempName] = useState('');
  const [tempDescription, setTempDescription] = useState('');
  const [tempNotes, setTempNotes] = useState('');

  // 计划练习相关状态
  const [planExercises, setPlanExercises] = useState<PlanExerciseResponse[]>([]);
  const [exercisesLoading, setExercisesLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingPlanExercise] = useState<PlanExerciseResponse | null>(null);
  const [availableExercises, setAvailableExercises] = useState<ExerciseResponse[]>([]);
  const [selectedTenantId, setSelectedTenantId] = useState<number | undefined>(undefined);
  
  // 排序模式相关状态
  const [isSortMode, setIsSortMode] = useState(false);
  
  // 编辑模式相关状态
  const [isEditMode, setIsEditMode] = useState(false);
  
  // 练习库搜索相关状态
  const [libraryLoading, setLibraryLoading] = useState(false);
  const [searchType, setSearchType] = useState<number | undefined>(undefined); // 默认全部
  const [searchTitle, setSearchTitle] = useState('');
  const [searchTitleValue, setSearchTitleValue] = useState('');
  const [searchNotes, setSearchNotes] = useState('');
  const [searchNotesValue, setSearchNotesValue] = useState('');

  
  // 跟踪已选中的练习ID列表（本计划中的练习）
  const [selectedExerciseIds, setSelectedExerciseIds] = useState<Set<number>>(new Set());

  // 创建对搜索框的引用
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const searchTitleInputRef = useRef<any>(null);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const searchNotesInputRef = useRef<any>(null);

  // 获取计划详情
  const fetchPlan = useCallback(async () => {
    if (!id) return;
    
    try {
      setInitialLoading(true);
      const response = await getPlan(parseInt(id));
      setPlan(response);
      setTempName(response.name);
      setTempDescription(response.description || '');
      setTempNotes(response.notes || '');
    } catch (error) {
      showError(error, '获取计划详情失败');
      navigate('/system/plan');
    } finally {
      setInitialLoading(false);
    }
  }, [id, navigate]);

  // 保存名称
  const handleSaveName = async () => {
    if (!plan || !tempName.trim()) return;
    
    try {
      await updatePlan(plan.id, { name: tempName.trim() });
      setPlan({ ...plan, name: tempName.trim() });
      setEditingName(false);
      message.success('名称更新成功');
    } catch (error) {
      showError(error, '更新名称失败');
    }
  };

  // 保存描述
  const handleSaveDescription = async () => {
    if (!plan) return;
    
    try {
      await updatePlan(plan.id, { description: tempDescription });
      setPlan({ ...plan, description: tempDescription });
      setEditingDescription(false);
      message.success('描述更新成功');
    } catch (error) {
      showError(error, '更新描述失败');
    }
  };

  // 保存备注
  const handleSaveNotes = async () => {
    if (!plan) return;
    
    try {
      await updatePlan(plan.id, { notes: tempNotes });
      setPlan({ ...plan, notes: tempNotes });
      setEditingNotes(false);
      message.success('备注更新成功');
    } catch (error) {
      showError(error, '更新备注失败');
    }
  };

  // 开始编辑各字段
  const startEditName = () => {
    setTempName(plan?.name || '');
    setEditingName(true);
  };

  const startEditDescription = () => {
    setTempDescription(plan?.description || '');
    setEditingDescription(true);
  };

  const startEditNotes = () => {
    setTempNotes(plan?.notes || '');
    setEditingNotes(true);
  };

  // 取消编辑
  const cancelEditName = () => {
    setTempName(plan?.name || '');
    setEditingName(false);
  };

  const cancelEditDescription = () => {
    setTempDescription(plan?.description || '');
    setEditingDescription(false);
  };

  const cancelEditNotes = () => {
    setTempNotes(plan?.notes || '');
    setEditingNotes(false);
  };

  useEffect(() => {
    fetchPlan();
  }, [fetchPlan]);

  // 获取租户信息
  const loadGlobalTenant = useCallback(() => {
    const tenantInfo = getGlobalTenantInfo();
    if (tenantInfo) {
      setSelectedTenantId(tenantInfo.id);
      return tenantInfo;
    } else {
      setSelectedTenantId(undefined);
      return null;
    }
  }, []);

  // 监听全局租户变化
  useEffect(() => {
    loadGlobalTenant();

    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'globalTenant') {
        loadGlobalTenant();
      }
    };

    const handleGlobalTenantChange = (e: Event) => {
      const customEvent = e as CustomEvent;
      const { id } = customEvent.detail;
      if (id) {
        setSelectedTenantId(id);
      } else {
        setSelectedTenantId(undefined);
      }
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('globalTenantChanged', handleGlobalTenantChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('globalTenantChanged', handleGlobalTenantChange);
    };
  }, [loadGlobalTenant]);

  // 获取计划练习列表
  const fetchPlanExercises = useCallback(async () => {
    if (!id || !selectedTenantId) return;
    
    try {
      setExercisesLoading(true);
      const response = await getPlanExercises(parseInt(id), selectedTenantId);
      setPlanExercises(response.items);
      // 更新已选中的练习ID列表
      const selectedIds = new Set(response.items.map(item => item.eid));
      setSelectedExerciseIds(selectedIds);
    } catch (error) {
      showError(error, '获取计划练习失败');
    } finally {
      setExercisesLoading(false);
    }
  }, [id, selectedTenantId]);



  // 获取练习库
  const fetchExerciseLibrary = useCallback(async () => {
    if (!selectedTenantId) {
      setAvailableExercises([]);
      return;
    }

    setLibraryLoading(true);
    try {
      const exercises = await getExercises({
        tenant_id: selectedTenantId,
        active: 1,
        published: 1,
        type: searchType, // 可选类型筛选，undefined表示全部
        limit: 100,
        skip: 0
      });
      
      // 手动过滤标题和备注
      let filteredExercises = exercises;
      if (searchTitle) {
        filteredExercises = filteredExercises.filter(exercise => 
          exercise.title.toLowerCase().includes(searchTitle.toLowerCase())
        );
      }
      if (searchNotes) {
        filteredExercises = filteredExercises.filter(exercise => 
          exercise.notes?.toLowerCase().includes(searchNotes.toLowerCase())
        );
      }
      
      setAvailableExercises(filteredExercises);
    } catch (error) {
      showError(error, '获取练习库失败');
    } finally {
      setLibraryLoading(false);
    }
  }, [selectedTenantId, searchType, searchTitle, searchNotes]);

  // 切换排序模式
  const toggleSortMode = () => {
    if (!isSortMode && !planExercises.length) {
      message.warning('没有可排序的练习');
      return;
    }
    setIsSortMode(!isSortMode);
    // 退出排序模式时，也要退出编辑模式
    if (isSortMode) {
      setIsEditMode(false);
    }
  };

  // 切换编辑模式
  const toggleEditMode = () => {
    setIsEditMode(!isEditMode);
    // 进入编辑模式时，退出排序模式
    if (!isEditMode) {
      setIsSortMode(false);
    }
  };



  // 处理拖拽结束
  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    if (!active.id || !over?.id || !id || !selectedTenantId) return;

    // 解析拖拽ID，提取真实ID
    const parseId = (id: string | number) => {
      const idStr = id.toString();
      if (idStr.startsWith('exercise-')) {
        return parseInt(idStr.replace('exercise-', ''));
      }
      return 0;
    };

    const activeId = parseId(active.id);
    const overId = parseId(over.id);

    // 检查是否是练习拖拽
    if (isSortMode && activeId !== overId && activeId > 0 && overId > 0) {
      const oldIndex = planExercises.findIndex((exercise) => exercise.id === activeId);
      const newIndex = planExercises.findIndex((exercise) => exercise.id === overId);

      if (oldIndex === -1 || newIndex === -1) return;

      const newPlanExercises = arrayMove(planExercises, oldIndex, newIndex);
      setPlanExercises(newPlanExercises);
      
      // 立即调用API更新排序
      try {
        const orderData: PlanExerciseBatchOrderRequest = {
          tenant_id: selectedTenantId,
          plan_id: parseInt(id),
          plan_exercises: newPlanExercises.map((exercise, index) => ({
            id: exercise.id,
            priority: index + 1
          }))
        };
        
        await batchUpdatePlanExerciseOrder(orderData);
        message.success('练习排序更新成功');
      } catch (error) {
        // 如果API调用失败，恢复原始顺序
        setPlanExercises(planExercises);
        showError(error, '练习排序更新失败');
      }
    }
  };



  // 编辑练习（已移除编辑按钮，保留函数以防后续需要）
  // const handleEditExercise = (planExercise: PlanExerciseResponse) => {
  //   setEditingPlanExercise(planExercise);
  //   setIsModalVisible(true);
  //   fetchAvailableExercises();
  // };

  // 删除练习
  const handleDeleteExercise = (planExerciseId: number) => {
    confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: '确定要删除这个练习吗？删除后不可恢复。',
      okText: '确认',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await deletePlanExercise(planExerciseId);
          message.success('删除成功');
          fetchPlanExercises();
        } catch (error) {
          showError(error, '删除练习失败');
        }
      },
    });
  };

  // 切换依赖状态
  const handleToggleDepend = async (planExercise: PlanExerciseResponse) => {
    if (!id) return;
    
    try {
      const newDepend = planExercise.depend === 1 ? 0 : 1;
      const updateData: PlanExerciseUpdate = {
        depend: newDepend
      };
      await updatePlanExercise(parseInt(id), planExercise.id, updateData);
      message.success(`已${newDepend === 1 ? '设置' : '取消'}依赖`);
      fetchPlanExercises();
    } catch (error) {
      showError(error, '更新依赖状态失败');
    }
  };

  // 刷新练习列表
  const handleRefreshExercises = () => {
    fetchPlanExercises();
  };

  // 处理标题搜索
  const handleTitleSearch = (value: string) => {
    if (!isEditMode) return;
    setSearchTitle(value);
    setSearchTitleValue(value);
  };

  // 处理标题搜索输入框值变化
  const handleTitleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!isEditMode) return;
    setSearchTitleValue(e.target.value);
  };

  // 处理备注搜索
  const handleNotesSearch = (value: string) => {
    if (!isEditMode) return;
    setSearchNotes(value);
    setSearchNotesValue(value);
  };

  // 处理备注搜索输入框值变化
  const handleNotesInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!isEditMode) return;
    setSearchNotesValue(e.target.value);
  };

  // 重置练习库搜索
  const handleResetLibrarySearch = () => {
    if (!isEditMode) return;
    setSearchType(undefined);
    setSearchTitle('');
    setSearchTitleValue('');
    setSearchNotes('');
    setSearchNotesValue('');
  };

  // 处理练习库点击 - 添加或删除计划-练习关系
  const handleLibraryExerciseClick = async (exercise: ExerciseResponse) => {
    if (!isEditMode || !id) return;
    
    if (!selectedTenantId) {
      message.error('请先在顶部导航栏选择租户');
      return;
    }

    const isSelected = selectedExerciseIds.has(exercise.id);
    
    try {
      if (isSelected) {
        // 删除计划-练习关系
        const planExercise = planExercises.find(pe => pe.eid === exercise.id);
        if (planExercise) {
          await deletePlanExercise(planExercise.id);
          message.success('已从计划中移除练习');
          
          // 更新选中状态
          const newSelectedIds = new Set(selectedExerciseIds);
          newSelectedIds.delete(exercise.id);
          setSelectedExerciseIds(newSelectedIds);
          
          // 重新获取本计划练习
          await fetchPlanExercises();
        }
      } else {
        // 创建计划-练习关系
        const createData: PlanExerciseCreate = {
          tenant_id: selectedTenantId,
          pid: parseInt(id),
          eid: exercise.id,
          priority: planExercises.length + 1
        };
        
        await createPlanExercise(parseInt(id), createData);
        message.success('已添加练习到计划');
        
        // 更新选中状态
        const newSelectedIds = new Set(selectedExerciseIds);
        newSelectedIds.add(exercise.id);
        setSelectedExerciseIds(newSelectedIds);
        
        // 重新获取本计划练习
        await fetchPlanExercises();
      }
    } catch (error) {
      showError(error, isSelected ? '移除练习失败' : '添加练习失败');
    }
  };



  // 提交表单
  const handleSubmitExercise = async (values: { exerciseId: number; depend?: number }) => {
    if (!id || !selectedTenantId) return;
    
    try {
      if (editingPlanExercise) {
        // 更新练习
        const updateData: PlanExerciseUpdate = {
          depend: values.depend
        };
        await updatePlanExercise(parseInt(id), editingPlanExercise.id, updateData);
        message.success('更新成功');
      } else {
        // 创建新练习
        const createData: PlanExerciseCreate = {
          tenant_id: selectedTenantId,
          pid: parseInt(id),
          eid: values.exerciseId,
          depend: values.depend,
          priority: planExercises.length + 1
        };
        await createPlanExercise(parseInt(id), createData);
        message.success('添加成功');
      }
      
      setIsModalVisible(false);
      fetchPlanExercises();
    } catch (error) {
      showError(error, editingPlanExercise ? '更新练习失败' : '添加练习失败');
    }
  };

  // 获取练习类型标签
  const getExerciseTypeTag = (type: number) => {
    const typeMap: { [key: number]: { name: string; color: string } } = {
      1: { name: '作业单', color: 'blue' },
      2: { name: '模拟场景', color: 'pink' },
    };
    const typeInfo = typeMap[type] || { name: '未知类型', color: 'default' };
    return <Tag color={typeInfo.color}>{typeInfo.name}</Tag>;
  };

  useEffect(() => {
    if (selectedTenantId && id) {
      fetchPlanExercises();
    }
  }, [fetchPlanExercises]);

  // 监听ESC键退出编辑状态
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isEditMode) {
        setIsEditMode(false);
        message.info('已退出编辑模式');
      }
    };

    // 添加键盘事件监听器
    document.addEventListener('keydown', handleKeyDown);

    // 清理函数
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isEditMode]);

  // 监听练习库搜索参数变化
  useEffect(() => {
    if (isEditMode) {
      fetchExerciseLibrary();
    }
  }, [fetchExerciseLibrary, isEditMode, searchType, searchTitle, searchNotes]);

  if (initialLoading) {
    return (
      <div style={{ textAlign: 'center', padding: '100px 0' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!plan) {
    return (
      <div style={{ textAlign: 'center', padding: '100px 0' }}>
        <Text type="secondary">练习计划不存在</Text>
      </div>
    );
  }

  return (
    <div className="detail-page-container">
      {/* 面包屑导航 */}
      <Breadcrumb 
        style={{ marginBottom: 8 }}
        items={[
          {
            title: (
              <Link to="/system/home">
                <HomeOutlined /> 主页
              </Link>
            ),
          },
          {
            title: '租户空间',
          },
          {
            title: (
              <Link to="/system/plan">
                练习计划
              </Link>
            ),
          },
          {
            title: '计划详情',
          },
        ]}
      />

      <div style={{ backgroundColor: token.colorBgContainer, flex: 1, display: 'flex', flexDirection: 'column' }}>
        {/* 头部区域 */}
        <div className="detail-page-header" style={{ borderBottom: `1px solid ${token.colorBorderSecondary}` }}>
          {/* 标题行 */}
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'flex-end',
            marginBottom: '16px'
          }}>
            {/* 标题 */}
            <div style={{ flex: 1, display: 'flex', alignItems: 'flex-end', gap: '8px' }}>
              {editingName ? (
                <div style={{ display: 'flex', alignItems: 'flex-end', gap: '8px' }}>
                  <Input
                    value={tempName}
                    onChange={(e) => setTempName(e.target.value)}
                    size="large"
                    style={{ fontSize: '20px', fontWeight: 600, minWidth: '300px' }}
                    onPressEnter={handleSaveName}
                    onKeyDown={(e) => {
                      if (e.key === 'Escape') {
                        cancelEditName();
                      }
                    }}
                    autoFocus
                  />
                  <Space>
                    <Button
                      type="primary"
                      size="small"
                      icon={<SaveOutlined />}
                      onClick={handleSaveName}
                    />
                    <Button
                      size="small"
                      icon={<CloseOutlined />}
                      onClick={cancelEditName}
                    />
                  </Space>
                </div>
              ) : (
                <div style={{ display: 'flex', alignItems: 'flex-end', gap: '8px' }}>
                  <Title 
                    level={3} 
                    style={{ 
                      margin: 0,
                      cursor: 'pointer'
                    }}
                    onClick={startEditName}
                  >
                    {plan?.name || '加载中...'}
                  </Title>
                  <Button
                    type="text"
                    icon={<EditOutlined />}
                    size="small"
                    onClick={startEditName}
                    style={{ color: token.colorTextSecondary }}
                  />
                </div>
              )}
            </div>
          </div>

          {/* 字段信息行 */}
          <Row gutter={[16, 12]}>
            {/* 描述 */}
            <Col span={14}>
              <div className="detail-field-row">
                <Text className="detail-field-label" style={{ color: token.colorTextSecondary }}>
                  描述：
                </Text>
                {editingDescription ? (
                  <div className="detail-edit-container">
                    <TextArea
                      value={tempDescription}
                      onChange={(e) => setTempDescription(e.target.value)}
                      placeholder="添加描述..."
                      className="detail-edit-input"
                      autoSize={{ minRows: 1, maxRows: 4 }}
                      autoFocus
                      onKeyDown={(e) => {
                        if (e.key === 'Escape') {
                          cancelEditDescription();
                        }
                      }}
                    />
                    <div style={{ marginTop: 4 }}>
                      <Space>
                        <Button
                          type="primary"
                          size="small"
                          icon={<SaveOutlined />}
                          onClick={handleSaveDescription}
                        />
                        <Button
                          size="small"
                          icon={<CloseOutlined />}
                          onClick={cancelEditDescription}
                        />
                      </Space>
                    </div>
                  </div>
                ) : (
                  <Tooltip title={plan?.description || '（暂无）'} placement="top">
                    <Text 
                      className={`detail-field-content ${plan?.description ? 'has-value' : 'empty'}`}
                      style={{
                        color: plan?.description ? token.colorText : token.colorTextTertiary,
                      }}
                      onClick={startEditDescription}
                    >
                      {plan?.description || '（暂无）'}
                    </Text>
                  </Tooltip>
                )}
              </div>
            </Col>

            {/* 备注 */}
            <Col span={10}>
              <div className="detail-field-row">
                <Text className="detail-field-label" style={{ color: token.colorTextSecondary }}>
                  备注：
                </Text>
                {editingNotes ? (
                  <div className="detail-edit-container">
                    <TextArea
                      value={tempNotes}
                      onChange={(e) => setTempNotes(e.target.value)}
                      placeholder="添加备注..."
                      className="detail-edit-input"
                      autoSize={{ minRows: 1, maxRows: 4 }}
                      autoFocus
                      onKeyDown={(e) => {
                        if (e.key === 'Escape') {
                          cancelEditNotes();
                        }
                      }}
                    />
                    <div style={{ marginTop: 4 }}>
                      <Space>
                        <Button
                          type="primary"
                          size="small"
                          icon={<SaveOutlined />}
                          onClick={handleSaveNotes}
                        />
                        <Button
                          size="small"
                          icon={<CloseOutlined />}
                          onClick={cancelEditNotes}
                        />
                      </Space>
                    </div>
                  </div>
                ) : (
                  <Tooltip title={plan?.notes || '（暂无）'} placement="top">
                    <Text 
                      className={`detail-field-content ${plan?.notes ? 'has-value' : 'empty'}`}
                      style={{
                        color: plan?.notes ? token.colorText : token.colorTextTertiary,
                      }}
                      onClick={startEditNotes}
                    >
                      {plan?.notes || '（暂无）'}
                    </Text>
                  </Tooltip>
                )}
              </div>
            </Col>
          </Row>
        </div>

        {/* 练习题部分 */}
        <Row gutter={16} style={{ margin: '0px', marginTop: '24px' }}>
          {/* 左侧分栏：本计划练习 */}
          <Col span={10} style={{ height: '500px' }}>
            <Card
              title={
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <span>
                    <BookOutlined style={{ marginRight: 8 }} />
                    {isSortMode ? '练习题排序' : isEditMode ? '练习题编辑' : '本计划练习'}
                  </span>
                  <div style={{ display: 'flex', gap: '4px' }}>
                    <Tooltip title="刷新列表">
                      <Button
                        type="primary"
                        icon={<ReloadOutlined />}
                        onClick={handleRefreshExercises}
                        loading={exercisesLoading}
                        disabled={isSortMode || isEditMode}
                        size="small"
                      />
                    </Tooltip>
                    <Tooltip title={isEditMode ? "退出编辑" : "编辑"}>
                      <Button
                        type={isEditMode ? "primary" : "text"}
                        icon={isEditMode ? <RollbackOutlined /> : <EditOutlined />}
                        onClick={toggleEditMode}
                        disabled={isSortMode}
                        size="small"
                      />
                    </Tooltip>
                    <Tooltip title={isSortMode ? "退出排序" : "调整排序"}>
                      <Button
                        type={isSortMode ? "primary" : "text"}
                        icon={<DragOutlined />}
                        onClick={toggleSortMode}
                        disabled={isEditMode}
                        size="small"
                      />
                    </Tooltip>

                  </div>
                </div>
              }
              style={{ height: '100%' }}
              styles={{ 
                body: {
                  height: 'calc(100% - 57px)',
                  overflow: 'auto',
                  padding: '12px'
                }
              }}
            >
              <DndContext 
                sensors={sensors} 
                collisionDetection={closestCenter} 
                onDragEnd={handleDragEnd}
              >
                <Spin spinning={exercisesLoading}>
                  {planExercises.length === 0 ? (
                    <Empty
                      description="暂无练习数据"
                      style={{ marginTop: '60px' }}
                    />
                  ) : (
                    <SortableContext 
                      items={planExercises.map(exercise => `exercise-${exercise.id}`)} 
                      strategy={verticalListSortingStrategy}
                    >
                      <div>
                        {planExercises.map((planExercise, index) => (
                          <SortablePlanExerciseItem
                            key={planExercise.id}
                            planExercise={planExercise}
                            index={index}
                            isSortMode={isSortMode}
                            isEditMode={isEditMode}
                            onDelete={handleDeleteExercise}
                            onToggleDepend={handleToggleDepend}
                            getExerciseTypeTag={getExerciseTypeTag}
                          />
                        ))}
                      </div>
                    </SortableContext>
                  )}
                </Spin>
              </DndContext>
            </Card>
          </Col>

          {/* 右侧分栏：练习库 */}
          <Col span={14} style={{ height: '500px' }}>
            <Card
              title={
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <span>
                    <DatabaseOutlined style={{ marginRight: 8 }} />
                    练习库
                    {!isEditMode && (
                      <span style={{ fontSize: '12px', color: '#999', marginLeft: '8px' }}>
                        （请先点击"编辑"按钮）
                      </span>
                    )}
                    {isEditMode && (
                      <span style={{ fontSize: '12px', color: '#999', marginLeft: '8px' }}>
                        （请输入条件缩小搜索范围）
                      </span>
                    )}
                  </span>
                  <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                    <Select
                      placeholder="类型"
                      value={searchType}
                      onChange={setSearchType}
                      style={{ width: 100 }}
                      size="small"
                      disabled={!isEditMode}
                      allowClear
                    >
                      <Option value={1}>作业单</Option>
                      <Option value={2}>场景</Option>
                    </Select>
                    <Search
                      placeholder="搜索标题"
                      allowClear
                      onSearch={handleTitleSearch}
                      style={{ width: 120 }}
                      ref={searchTitleInputRef}
                      value={searchTitleValue}
                      onChange={handleTitleInputChange}
                      size="small"
                      disabled={!isEditMode}
                    />
                    <Search
                      placeholder="搜索备注"
                      allowClear
                      onSearch={handleNotesSearch}
                      style={{ width: 120 }}
                      ref={searchNotesInputRef}
                      value={searchNotesValue}
                      onChange={handleNotesInputChange}
                      size="small"
                      disabled={!isEditMode}
                    />
                    <Tooltip title="重置搜索">
                      <Button
                        type="primary"
                        icon={<ReloadOutlined />}
                        onClick={handleResetLibrarySearch}
                        size="small"
                        disabled={!isEditMode}
                      />
                    </Tooltip>
                  </div>
                </div>
              }
              style={{ 
                height: '100%',
                opacity: isEditMode ? 1 : 0.6
              }}
              styles={{ 
                body: {
                  height: 'calc(100% - 57px)',
                  overflow: 'auto',
                  padding: '12px'
                }
              }}
            >
              <Spin spinning={libraryLoading}>
                {!isEditMode ? (
                  <Empty
                    description={
                      <div>
                        <div>练习库暂不可用</div>
                        <div style={{ fontSize: '12px', color: '#999', marginTop: '8px' }}>
                          请先点击左上角的"编辑"按钮进入编辑模式
                        </div>
                      </div>
                    }
                    style={{ marginTop: '60px' }}
                  />
                ) : availableExercises.length === 0 ? (
                  <Empty
                    description="暂无符合条件的练习"
                    style={{ marginTop: '60px' }}
                  />
                ) : (
                  <div>
                    {availableExercises.map((exercise) => {
                      const isSelected = selectedExerciseIds.has(exercise.id);
                      return (
                        <div
                          key={exercise.id}
                          style={{
                            border: isSelected ? '1px solid #1890ff' : '1px solid #f0f0f0',
                            borderRadius: '6px',
                            padding: '12px',
                            marginBottom: '8px',
                            cursor: isEditMode ? 'pointer' : 'not-allowed',
                            transition: 'all 0.2s',
                            backgroundColor: isSelected ? '#e6f7ff' : 'transparent',
                            opacity: isEditMode ? 1 : 0.6
                          }}
                          onMouseEnter={(e) => {
                            if (isEditMode && !isSelected) {
                              e.currentTarget.style.backgroundColor = '#f5f5f5';
                              e.currentTarget.style.borderColor = '#d9d9d9';
                            }
                          }}
                          onMouseLeave={(e) => {
                            if (isEditMode && !isSelected) {
                              e.currentTarget.style.backgroundColor = 'transparent';
                              e.currentTarget.style.borderColor = '#f0f0f0';
                            }
                          }}
                          onClick={() => {
                            if (isEditMode) {
                              handleLibraryExerciseClick(exercise);
                            }
                          }}
                        >
                          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                            {/* 图片 */}
                            <div style={{ 
                              width: '48px', 
                              height: '48px', 
                              borderRadius: '4px',
                              overflow: 'hidden',
                              backgroundColor: '#f5f5f5',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              flexShrink: 0
                            }}>
                              {exercise.pic ? (
                                <img 
                                  src={exercise.pic} 
                                  alt={exercise.title}
                                  style={{ 
                                    width: '100%', 
                                    height: '100%', 
                                    objectFit: 'cover' 
                                  }}
                                  onError={(e) => {
                                    e.currentTarget.style.display = 'none';
                                    e.currentTarget.parentElement!.innerHTML = '<div style="width: 100%; height: 100%; background: #f0f0f0; display: flex; align-items: center; justify-content: center; color: #ccc; font-size: 12px;">无图</div>';
                                  }}
                                />
                              ) : (
                                <div style={{ color: '#ccc', fontSize: '12px' }}>无图</div>
                              )}
                            </div>

                            {/* 标题和类型 */}
                            <div style={{ 
                              flex: 1, 
                              minWidth: 0,
                              height: '48px',
                              display: 'flex',
                              flexDirection: 'column',
                              justifyContent: 'space-between'
                            }}>
                              {/* 标题行：标题 + info图标 + 备注图标 */}
                              <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                                <Text strong style={{ 
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis',
                                  whiteSpace: 'nowrap',
                                  fontSize: '14px',
                                  lineHeight: '20px'
                                }}>
                                  {exercise.title}
                                </Text>
                                {/* 信息图标（悬停显示简介） */}
                                {exercise.intro && (
                                  <Tooltip title={exercise.intro} placement="top">
                                    <InfoCircleOutlined 
                                      style={{ 
                                        color: '#999', 
                                        fontSize: '12px',
                                        flexShrink: 0
                                      }} 
                                    />
                                  </Tooltip>
                                )}
                                {/* 备注图标（悬停显示备注） */}
                                {exercise.notes && (
                                  <Tooltip title={exercise.notes} placement="top">
                                    <CommentOutlined 
                                      style={{ 
                                        color: '#999', 
                                        fontSize: '12px',
                                        flexShrink: 0
                                      }} 
                                    />
                                  </Tooltip>
                                )}
                              </div>
                              {/* 类型标签行：底部对齐 */}
                              <div style={{ alignSelf: 'flex-start' }}>
                                {getExerciseTypeTag(exercise.type)}
                              </div>
                            </div>

                            {/* 选中状态图标（靠右） */}
                            {isSelected && (
                              <div style={{ display: 'flex', alignItems: 'center' }}>
                                <Tag color="success" style={{ margin: 0, fontSize: '12px' }}>
                                  已选
                                </Tag>
                              </div>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </Spin>
            </Card>
          </Col>
        </Row>
      </div>

      {/* 添加/编辑练习模态框 */}
      <Modal
        title={editingPlanExercise ? '编辑练习' : '添加练习'}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          layout="vertical"
          onFinish={handleSubmitExercise}
          initialValues={{
            exerciseId: editingPlanExercise?.eid,
            depend: editingPlanExercise?.depend,
          }}
        >
          {!editingPlanExercise && (
            <Form.Item
              label="选择练习"
              name="exerciseId"
              rules={[{ required: true, message: '请选择练习' }]}
            >
              <Select
                placeholder="请选择练习"
                showSearch
                optionFilterProp="children"
              >
                {availableExercises.map(exercise => (
                  <Option key={exercise.id} value={exercise.id}>
                    {exercise.title}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          )}
          
          <Form.Item
            label="依赖"
            name="depend"
          >
            <Select
              placeholder="请选择依赖关系"
              allowClear
            >
              <Option value={0}>不依赖</Option>
              <Option value={1}>依赖前一个练习</Option>
            </Select>
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setIsModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingPlanExercise ? '更新' : '添加'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default PlanDetail; 