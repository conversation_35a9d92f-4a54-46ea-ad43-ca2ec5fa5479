import React, { useState, useEffect, useCallback } from 'react';
import { 
  Button, 
  Space, 
  Modal,
  Table,
  Tooltip,
  Card,
  Input,
  Empty,
  message,
  Form
} from 'antd';
import { 
  PlusOutlined,
  DeleteOutlined,
  DragOutlined,
  CloseOutlined,
  ReloadOutlined,
  ExclamationCircleOutlined,
  EditOutlined,
  SaveOutlined
} from '@ant-design/icons';
import type { TableProps } from 'antd/es/table';
import { 
  getSceneGuides,
  createSceneGuide,
  updateSceneGuide,
  deleteSceneGuide,
  batchUpdateSceneGuideOrder,
  type SceneGuideResponse,
  type SceneGuideCreate,
  type SceneGuideUpdate,
  type SceneGuideBatchOrderRequest
} from '../../../services/system/scene';
import { showError, showSuccess } from '../../../utils/errorHandler';
import { getGlobalTenantInfo } from '../../../components/GlobalTenantSelector';
import SortableGuideTable from './SortableGuideTable';


const { Search } = Input;
const { confirm } = Modal;

// 场景指南表单组件
interface SceneGuideFormProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  guide: SceneGuideResponse | null;
  tenantId: number;
  sceneId: number;
}

const SceneGuideForm: React.FC<SceneGuideFormProps> = ({
  visible,
  onCancel,
  onSuccess,
  guide,
  tenantId,
  sceneId
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (visible) {
      if (guide) {
        form.setFieldsValue({
          title: guide.title,
          details: guide.details,
        });
      } else {
        form.resetFields();
      }
    }
  }, [visible, guide, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      if (guide) {
        // 更新
        await updateSceneGuide(guide.id, values as SceneGuideUpdate, tenantId);
        showSuccess('指南更新成功');
      } else {
        // 创建
        const createData: SceneGuideCreate = {
          sid: sceneId,
          tenant_id: tenantId,
          ...values
        };
        await createSceneGuide(createData);
        showSuccess('指南创建成功');
      }

      onSuccess();
    } catch (error) {
      showError(error, guide ? '更新指南失败' : '创建指南失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={guide ? '编辑指南' : '添加指南'}
      open={visible}
      onCancel={onCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
      width={600}
    >
      <Form form={form} layout="vertical">
        <Form.Item
          name="title"
          label="标题"
          rules={[{ required: true, message: '请输入标题' }]}
        >
          <Input placeholder="请输入指南标题" />
        </Form.Item>
        
        <Form.Item
          name="details"
          label="详情"
          rules={[{ required: true, message: '请输入详情' }]}
        >
          <Input.TextArea
            placeholder="请输入指南详情"
            rows={6}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

// 场景指南表格组件
const SceneGuidesTable: React.FC<{
  guides: SceneGuideResponse[];
  tenantId: number;
  sceneId: number;
  onRefresh: () => void;
  onGuidesChange: (guides: SceneGuideResponse[]) => void;
}> = ({ guides, tenantId, sceneId, onRefresh, onGuidesChange }) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isSortMode, setIsSortMode] = useState(false);
  const [originalGuides, setOriginalGuides] = useState<SceneGuideResponse[]>([]);
  const [currentGuide, setCurrentGuide] = useState<SceneGuideResponse | null>(null);
  const [searchTitle, setSearchTitle] = useState('');
  const [searchTitleValue, setSearchTitleValue] = useState('');
  const [filteredGuides, setFilteredGuides] = useState<SceneGuideResponse[]>([]);
  
  // 分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  
  // 排序状态
  const [sortOrders, setSortOrders] = useState<{
    [key: string]: 'asc' | 'desc' | undefined
  }>({
    priority: 'asc'
  });

  // 过滤和排序数据
  const filterAndSortGuides = useCallback(() => {
    let filtered = [...guides];
    
    // 手动实现标题搜索过滤
    if (searchTitle) {
      filtered = filtered.filter(guide => 
        guide.title.toLowerCase().includes(searchTitle.toLowerCase())
      );
    }
    
    // 手动实现排序
    if (sortOrders.priority) {
      filtered.sort((a, b) => {
        if (sortOrders.priority === 'asc') {
          return a.priority - b.priority;
        } else {
          return b.priority - a.priority;
        }
      });
    }
    
    setFilteredGuides(filtered);
    setPagination(prev => ({
      ...prev,
      total: filtered.length
    }));
  }, [guides, searchTitle, sortOrders]);

  useEffect(() => {
    filterAndSortGuides();
  }, [filterAndSortGuides]);

  // 处理标题搜索
  const handleTitleSearch = (value: string) => {
    setSearchTitle(value);
    setSearchTitleValue(value);
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  // 处理标题搜索输入框值变化，但不触发搜索
  const handleTitleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTitleValue(e.target.value);
  };

  const handleAdd = () => {
    setCurrentGuide(null);
    setModalVisible(true);
  };

  const handleEdit = (record: SceneGuideResponse) => {
    setCurrentGuide(record);
    setModalVisible(true);
  };

  const handleDelete = (id: number) => {
    confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: '确定要删除这个场景指南吗？删除后无法恢复。',
      okText: '确认',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await deleteSceneGuide(id, tenantId);
          showSuccess('删除成功');
          onRefresh();
        } catch (error) {
          showError(error, '删除失败');
        }
      }
    });
  };

  // 进入排序模式
  const enterSortMode = () => {
    if (guides.length === 0) {
      showError(null, '没有可排序的数据');
      return;
    }
    
    setOriginalGuides([...guides]);
    setIsSortMode(true);
  };

  // 退出排序模式
  const exitSortMode = () => {
    setIsSortMode(false);
    setOriginalGuides([]);
  };

  // 保存排序
  const handleSortSave = async () => {
    if (loading) return;
    
    try {
      setLoading(true);
      
      const hasChanges = guides.some((guide, index) => {
        const originalIndex = originalGuides.findIndex(orig => orig.id === guide.id);
        return originalIndex !== index;
      });
      
      if (!hasChanges) {
        showSuccess('排序未发生变化');
        exitSortMode();
        return;
      }
      
      const guidesOrderData: SceneGuideBatchOrderRequest = {
        tenant_id: tenantId,
        guides: guides.map((guide, index) => ({
          id: guide.id,
          priority: index + 1
        }))
      };
      
      await batchUpdateSceneGuideOrder(guidesOrderData);
      showSuccess('排序保存成功');
      
      onRefresh();
      exitSortMode();
    } catch (error) {
      showError(error, '保存排序失败');
    } finally {
      setLoading(false);
    }
  };

  // 取消排序的回调
  const handleSortCancel = () => {
    onGuidesChange([...originalGuides]);
    exitSortMode();
  };

  // 处理重置刷新
  const handleRefresh = async () => {
    setSearchTitle('');
    setSearchTitleValue('');

    setSortOrders({
      priority: 'asc'
    });

    setPagination(prev => ({
      ...prev,
      current: 1,
      pageSize: 10,
    }));
    
    if (isSortMode) {
      exitSortMode();
    }
    
    onRefresh();
  };

  // 处理表格排序变化
  const handleTableChange: TableProps<SceneGuideResponse>['onChange'] = (paginationConfig, _filters, sorter) => {
    if (paginationConfig) {
      setPagination(prev => ({
        ...prev,
        current: paginationConfig.current || 1,
        pageSize: paginationConfig.pageSize || 10,
      }));
    }

    interface SorterInfo {
      field?: string;
      order?: 'ascend' | 'descend';
    }

    const currentSorter = Array.isArray(sorter) ? sorter[0] : sorter;
    
    const newSortOrders: { [key: string]: "asc" | "desc" | undefined } = {
      priority: undefined
    };

    if (currentSorter && (currentSorter as SorterInfo).field) {
      const field = (currentSorter as SorterInfo).field;
      const order = (currentSorter as SorterInfo).order;
      
      if (field === 'priority') {
        if (order === 'ascend') {
          newSortOrders.priority = 'asc';
        } else if (order === 'descend') {
          newSortOrders.priority = 'desc';
        }
      }
    }

    if (Object.values(newSortOrders).every(v => v === undefined)) {
      newSortOrders.priority = 'asc';
    }

    setSortOrders(newSortOrders);
  };

  // 获取当前页数据
  const getCurrentPageData = () => {
    const startIndex = (pagination.current - 1) * pagination.pageSize;
    const endIndex = startIndex + pagination.pageSize;
    return filteredGuides.slice(startIndex, endIndex);
  };

  // 表单提交成功后的回调
  const handleFormSuccess = () => {
    setModalVisible(false);
    onRefresh();
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 100,
    },
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
      width: 300,
    },
    {
      title: '详情',
      dataIndex: 'details',
      key: 'details',
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_: unknown, record: SceneGuideResponse) => (
        <Space size="middle">
          <Tooltip title={isSortMode ? "排序模式下不可编辑" : "编辑"}>
            <Button
              type="primary"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
              disabled={isSortMode}
            />
          </Tooltip>
          <Tooltip title={isSortMode ? "排序模式下不可删除" : "删除"}>
            <Button
              type="primary"
              style={{ backgroundColor: '#ff4d4f', borderColor: '#ff4d4f' }}
              size="small"
              icon={<DeleteOutlined />}
              onClick={() => handleDelete(record.id)}
              disabled={isSortMode}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <Card
      title={isSortMode ? "场景指南排序" : "场景指南列表"}
      extra={
        <Space>
          {isSortMode ? (
            <>
              <Tooltip title="保存排序">
                <Button
                  type="primary"
                  icon={<SaveOutlined />}
                  onClick={handleSortSave}
                  loading={loading}
                  disabled={!guides.length}
                />
              </Tooltip>
              <Tooltip title="取消排序">
                <Button
                  type="default"
                  icon={<CloseOutlined />}
                  onClick={handleSortCancel}
                  disabled={loading}
                />
              </Tooltip>
            </>
          ) : (
            <>
              <Search
                placeholder="搜索指南标题"
                allowClear
                onSearch={handleTitleSearch}
                style={{ width: 160 }}
                value={searchTitleValue}
                onChange={handleTitleInputChange}
              />
              <Tooltip title="添加指南">
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAdd}
                />
              </Tooltip>
              <Tooltip title="重置刷新">
                <Button
                  type="primary"
                  icon={<ReloadOutlined />}
                  onClick={handleRefresh}
                />
              </Tooltip>
              <Tooltip title="调整排序">
                <Button 
                  icon={<DragOutlined />} 
                  onClick={enterSortMode}
                  disabled={guides.length === 0}
                />
              </Tooltip>
            </>
          )}
        </Space>
      }
    >
{isSortMode ? (
                        <SortableGuideTable
          guides={guides}
          loading={loading}
          onGuidesChange={onGuidesChange}
          onEdit={handleEdit}
          onDelete={handleDelete}
        />
      ) : (
        <Table
          columns={columns}
          dataSource={getCurrentPageData()}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          locale={{
            emptyText: '暂无指南数据'
          }}
          onChange={handleTableChange}
          sortDirections={['ascend', 'descend', 'ascend']}
          showSorterTooltip={false}
        />
      )}

      <SceneGuideForm
        visible={modalVisible}
        onCancel={() => setModalVisible(false)}
        onSuccess={handleFormSuccess}
        guide={currentGuide}
        tenantId={tenantId}
        sceneId={sceneId}
      />
    </Card>
  );
};

interface SceneGuidesTabProps {
  sceneId?: string;
}

const SceneGuidesTab: React.FC<SceneGuidesTabProps> = ({ sceneId }) => {
  const [guides, setGuides] = useState<SceneGuideResponse[]>([]);

  // 获取当前租户ID
  const getCurrentTenantId = (): number | null => {
    const tenantInfo = getGlobalTenantInfo();
    return tenantInfo?.id || null;
  };

  // 获取指南列表
  const fetchGuides = async () => {
    if (!sceneId) return;
    
    const tenantId = getCurrentTenantId();
    if (!tenantId) {
      message.error('请先选择租户');
      return;
    }

    try {
      const response = await getSceneGuides(parseInt(sceneId), tenantId);
      setGuides(response.items);
    } catch (error) {
      showError(error, '获取指南列表失败');
    }
  };

  // 处理指南变更
  const handleGuidesChange = (newGuides: SceneGuideResponse[]) => {
    setGuides(newGuides);
  };

  // 初始化加载数据
  useEffect(() => {
    fetchGuides();
  }, [sceneId]);

  // 监听租户切换事件
  useEffect(() => {
    const handleTenantChange = () => {
      setGuides([]);
      fetchGuides();
    };

    window.addEventListener('globalTenantChanged', handleTenantChange);
    return () => {
      window.removeEventListener('globalTenantChanged', handleTenantChange);
    };
  }, [sceneId]);

  if (!sceneId) {
    return (
      <div style={{ 
        height: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '40px'
      }}>
        <Empty
          description="场景ID不可用"
          style={{ color: '#999' }}
        />
      </div>
    );
  }

  const currentTenantId = getCurrentTenantId();
  if (!currentTenantId) {
    return (
      <div style={{ 
        height: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '40px'
      }}>
        <Empty
          description="请先在顶部导航栏选择租户"
          style={{ color: '#999' }}
        />
      </div>
    );
  }

  return (
    <div style={{ 
      padding: '0px',
      height: '100%',
      overflow: 'auto'
    }}>
      <SceneGuidesTable
        guides={guides}
        tenantId={currentTenantId}
        sceneId={parseInt(sceneId)}
        onRefresh={fetchGuides}
        onGuidesChange={handleGuidesChange}
      />
    </div>
  );
};

export default SceneGuidesTab; 