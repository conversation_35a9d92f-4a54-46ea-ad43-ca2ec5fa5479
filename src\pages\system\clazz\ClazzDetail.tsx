import React, { useState, useEffect, useCallback } from 'react';
import { 
  Button, 
  Space, 
  Breadcrumb, 
  Spin, 
  Row,
  Col,
  Typography,
  theme,
  message,
  Tooltip,
  Tabs,
  Input
} from 'antd';
import { 
  HomeOutlined, 
  EditOutlined,
  SaveOutlined,
  CloseOutlined,
  BookOutlined,
  TeamOutlined,
} from '@ant-design/icons';
import { useParams, useNavigate, Link } from 'react-router-dom';
import dayjs from 'dayjs';

import { 
  getClazz, 
  updateClazz,
  type ClazzResponse
} from '../../../services/system/clazz';
import { showError } from '../../../utils/errorHandler';
import { getGlobalTenantInfo } from '../../../components/GlobalTenantSelector';
import styles from '../../../components/RichTextEditor.module.css';
import '../../../styles/detail-page.css';
import ClazzExercisesTab from './ClazzExercisesTab';
import ClazzStudentsTab from './ClazzStudentsTab';

const { Title, Text } = Typography;
const { TextArea } = Input;

const ClazzDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [clazz, setClazz] = useState<ClazzResponse | null>(null);
  const [initialLoading, setInitialLoading] = useState(true);
  const [selectedTenantId, setSelectedTenantId] = useState<number | undefined>(undefined);
  const { token } = theme.useToken();
  const [activeTab, setActiveTab] = useState('exercises');
  
  // 内联编辑状态
  const [editingName, setEditingName] = useState(false);
  const [editingDescription, setEditingDescription] = useState(false);
  const [editingNotes, setEditingNotes] = useState(false);
  const [tempName, setTempName] = useState('');
  const [tempDescription, setTempDescription] = useState('');
  const [tempNotes, setTempNotes] = useState('');

  // 获取班级详情
  const fetchClazz = useCallback(async () => {
    if (!id || !selectedTenantId) return;
    
    try {
      setInitialLoading(true);
      const response = await getClazz(parseInt(id), selectedTenantId);
      setClazz(response);
      setTempName(response.name);
      setTempDescription(response.description || '');
      setTempNotes(response.notes || '');
    } catch (error) {
      showError(error, '获取班级详情失败');
      navigate('/system/clazz');
    } finally {
      setInitialLoading(false);
    }
  }, [id, selectedTenantId, navigate]);

  // 获取租户信息
  const loadGlobalTenant = useCallback(() => {
    const tenantInfo = getGlobalTenantInfo();
    if (tenantInfo) {
      setSelectedTenantId(tenantInfo.id);
      return tenantInfo;
    } else {
      setSelectedTenantId(undefined);
      return null;
    }
  }, []);

  // 监听全局租户变化
  useEffect(() => {
    loadGlobalTenant();

    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'globalTenant') {
        loadGlobalTenant();
      }
    };

    const handleGlobalTenantChange = (e: Event) => {
      const customEvent = e as CustomEvent;
      const { id } = customEvent.detail;
      if (id) {
        setSelectedTenantId(id);
      } else {
        setSelectedTenantId(undefined);
      }
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('globalTenantChanged', handleGlobalTenantChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('globalTenantChanged', handleGlobalTenantChange);
    };
  }, [loadGlobalTenant]);

  useEffect(() => {
    if (selectedTenantId) {
      fetchClazz();
    }
  }, [fetchClazz]);

  // 保存名称
  const handleSaveName = async () => {
    if (!clazz || !tempName.trim() || !selectedTenantId) return;
    
    try {
      await updateClazz(clazz.id, { name: tempName.trim() }, selectedTenantId);
      setClazz({ ...clazz, name: tempName.trim() });
      setEditingName(false);
      message.success('名称更新成功');
    } catch (error) {
      showError(error, '更新名称失败');
    }
  };

  // 保存描述
  const handleSaveDescription = async () => {
    if (!clazz || !selectedTenantId) return;
    
    try {
      await updateClazz(clazz.id, { description: tempDescription }, selectedTenantId);
      setClazz({ ...clazz, description: tempDescription });
      setEditingDescription(false);
      message.success('描述更新成功');
    } catch (error) {
      showError(error, '更新描述失败');
    }
  };

  // 保存备注
  const handleSaveNotes = async () => {
    if (!clazz || !selectedTenantId) return;
    
    try {
      await updateClazz(clazz.id, { notes: tempNotes }, selectedTenantId);
      setClazz({ ...clazz, notes: tempNotes });
      setEditingNotes(false);
      message.success('备注更新成功');
    } catch (error) {
      showError(error, '更新备注失败');
    }
  };

  // 开始编辑各字段
  const startEditName = () => {
    setTempName(clazz?.name || '');
    setEditingName(true);
  };

  const startEditDescription = () => {
    setTempDescription(clazz?.description || '');
    setEditingDescription(true);
  };

  const startEditNotes = () => {
    setTempNotes(clazz?.notes || '');
    setEditingNotes(true);
  };

  // 取消编辑
  const cancelEditName = () => {
    setTempName(clazz?.name || '');
    setEditingName(false);
  };

  const cancelEditDescription = () => {
    setTempDescription(clazz?.description || '');
    setEditingDescription(false);
  };

  const cancelEditNotes = () => {
    setTempNotes(clazz?.notes || '');
    setEditingNotes(false);
  };

  // 处理tab切换
  const handleTabChange = (tabKey: string) => {
    setActiveTab(tabKey);
  };

  // Tab配置
  const tabItems = [
    {
      key: 'exercises',
      label: (
        <span style={{ fontSize: '16px' }}>
          <BookOutlined style={{ marginRight: '8px', fontSize: '18px' }} />
          练习配置
        </span>
      ),
      children: clazz ? <ClazzExercisesTab clazzId={clazz.id} /> : null,
    },
    {
      key: 'students',
      label: (
        <span style={{ fontSize: '16px' }}>
          <TeamOutlined style={{ marginRight: '8px', fontSize: '18px' }} />
          参训学员
        </span>
      ),
      children: clazz ? <ClazzStudentsTab clazzId={clazz.id} /> : null,
    },
  ];

  if (initialLoading) {
    return (
      <div style={{ textAlign: 'center', padding: '100px 0' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!clazz) {
    return (
      <div style={{ textAlign: 'center', padding: '100px 0' }}>
        <Text type="secondary">班级不存在</Text>
      </div>
    );
  }

  return (
    <div className="detail-page-container">
      {/* 面包屑导航 */}
      <Breadcrumb 
        style={{ marginBottom: 8 }}
        items={[
          {
            title: (
              <Link to="/system/home">
                <HomeOutlined /> 主页
              </Link>
            ),
          },
          {
            title: '租户空间',
          },
          {
            title: (
              <Link to="/system/clazz">
                班级管理
              </Link>
            ),
          },
          {
            title: '班级详情',
          },
        ]}
      />

      <div style={{ backgroundColor: token.colorBgContainer, flex: 1, display: 'flex', flexDirection: 'column' }}>
        {/* 头部区域 */}
        <div className="detail-page-header" style={{ borderBottom: `1px solid ${token.colorBorderSecondary}` }}>
          {/* 标题行 */}
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'flex-end',
            marginBottom: '16px'
          }}>
            {/* 标题 */}
            <div style={{ flex: 1, display: 'flex', alignItems: 'flex-end', gap: '8px' }}>
              {editingName ? (
                <div style={{ display: 'flex', alignItems: 'flex-end', gap: '8px' }}>
                  <Input
                    value={tempName}
                    onChange={(e) => setTempName(e.target.value)}
                    size="large"
                    style={{ fontSize: '20px', fontWeight: 600, minWidth: '300px' }}
                    onPressEnter={handleSaveName}
                    onKeyDown={(e) => {
                      if (e.key === 'Escape') {
                        cancelEditName();
                      }
                    }}
                    autoFocus
                  />
                  <Space>
                    <Button
                      type="primary"
                      size="small"
                      icon={<SaveOutlined />}
                      onClick={handleSaveName}
                    />
                    <Button
                      size="small"
                      icon={<CloseOutlined />}
                      onClick={cancelEditName}
                    />
                  </Space>
                </div>
              ) : (
                <div style={{ display: 'flex', alignItems: 'flex-end', gap: '8px' }}>
                  <Title 
                    level={3} 
                    style={{ 
                      margin: 0,
                      cursor: 'pointer'
                    }}
                    onClick={startEditName}
                  >
                    {clazz?.name || '加载中...'}
                  </Title>
                  <Button
                    type="text"
                    icon={<EditOutlined />}
                    size="small"
                    onClick={startEditName}
                    style={{ color: token.colorTextSecondary }}
                  />
                  {/* 开班时间 - 放在标题右边 */}
                  {clazz?.btime && clazz?.etime && (
                    <Text 
                      type="secondary" 
                      style={{ 
                        fontSize: '14px', 
                        whiteSpace: 'nowrap',
                        marginLeft: '16px'
                      }}
                    >
                      {`${dayjs(clazz.btime).format('YYYY-MM-DD')} 至 ${dayjs(clazz.etime).format('YYYY-MM-DD')}`}
                    </Text>
                  )}
                </div>
              )}
            </div>
            
            {/* 右侧：创建时间 */}
            <div style={{ 
              display: 'flex', 
              alignItems: 'flex-end',
              gap: '16px'
            }}>
              {/* 创建时间 */}
              <Text 
                type="secondary" 
                style={{ fontSize: '12px', whiteSpace: 'nowrap' }}
              >
                创建于：{clazz?.ctime ? dayjs(clazz.ctime).format('YYYY-MM-DD HH:mm:ss') : '未知'}
              </Text>
            </div>
          </div>

          {/* 字段信息行 */}
          <Row gutter={[16, 12]}>
            {/* 描述 */}
            <Col span={14}>
              <div className="detail-field-row">
                <Text className="detail-field-label" style={{ color: token.colorTextSecondary }}>
                  描述：
                </Text>
                {editingDescription ? (
                  <div className="detail-edit-container">
                    <TextArea
                      value={tempDescription}
                      onChange={(e) => setTempDescription(e.target.value)}
                      placeholder="添加描述..."
                      className="detail-edit-input"
                      autoSize={{ minRows: 1, maxRows: 4 }}
                      autoFocus
                      onKeyDown={(e) => {
                        if (e.key === 'Escape') {
                          cancelEditDescription();
                        }
                      }}
                    />
                    <div style={{ marginTop: 4 }}>
                      <Space>
                        <Button
                          type="primary"
                          size="small"
                          icon={<SaveOutlined />}
                          onClick={handleSaveDescription}
                        />
                        <Button
                          size="small"
                          icon={<CloseOutlined />}
                          onClick={cancelEditDescription}
                        />
                      </Space>
                    </div>
                  </div>
                ) : (
                  <Tooltip title={clazz?.description || '（暂无）'} placement="top">
                    <Text 
                      className={`detail-field-content ${clazz?.description ? 'has-value' : 'empty'}`}
                      style={{
                        color: clazz?.description ? token.colorText : token.colorTextTertiary,
                      }}
                      onClick={startEditDescription}
                    >
                      {clazz?.description || '（暂无）'}
                    </Text>
                  </Tooltip>
                )}
              </div>
            </Col>

            {/* 备注 */}
            <Col span={10}>
              <div className="detail-field-row">
                <Text className="detail-field-label" style={{ color: token.colorTextSecondary }}>
                  备注：
                </Text>
                {editingNotes ? (
                  <div className="detail-edit-container">
                    <TextArea
                      value={tempNotes}
                      onChange={(e) => setTempNotes(e.target.value)}
                      placeholder="添加备注..."
                      className="detail-edit-input"
                      autoSize={{ minRows: 1, maxRows: 4 }}
                      autoFocus
                      onKeyDown={(e) => {
                        if (e.key === 'Escape') {
                          cancelEditNotes();
                        }
                      }}
                    />
                    <div style={{ marginTop: 4 }}>
                      <Space>
                        <Button
                          type="primary"
                          size="small"
                          icon={<SaveOutlined />}
                          onClick={handleSaveNotes}
                        />
                        <Button
                          size="small"
                          icon={<CloseOutlined />}
                          onClick={cancelEditNotes}
                        />
                      </Space>
                    </div>
                  </div>
                ) : (
                  <Tooltip title={clazz?.notes || '（暂无）'} placement="top">
                    <Text 
                      className={`detail-field-content ${clazz?.notes ? 'has-value' : 'empty'}`}
                      style={{
                        color: clazz?.notes ? token.colorText : token.colorTextTertiary,
                      }}
                      onClick={startEditNotes}
                    >
                      {clazz?.notes || '（暂无）'}
                    </Text>
                  </Tooltip>
                )}
              </div>
            </Col>
          </Row>
        </div>

        {/* 主体内容区域 */}
        <div className="detail-page-content">
          <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
            {/* 标签页 */}
            <div style={{ 
              flex: 1,
              display: 'flex', 
              flexDirection: 'column' 
            }}>
              <Tabs
                activeKey={activeTab}
                onChange={handleTabChange}
                destroyOnHidden={true}
                style={{ 
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column'
                }}
                className={styles.fullHeightTabs}
                items={tabItems}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ClazzDetail; 