import React from 'react';
import { Card, Typography, Button, Space } from 'antd';
import { useAuthStore } from '../utils/authStore';
import { MainLayout } from '../layouts';

const { Title, Text } = Typography;

const Dashboard: React.FC = () => {
  const { user } = useAuthStore();

  return (
    <MainLayout>
      <div style={{ maxWidth: 1200, margin: '0 auto' }}>
        <Title level={2}>仪表板</Title>

        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
          gap: '24px',
          marginTop: '24px'
        }}>
          <Card title="用户信息" variant="borderless">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong>用户ID：</Text>
                <Text>{user?.uid}</Text>
              </div>
              <div>
                <Text strong>用户名：</Text>
                <Text>{user?.username}</Text>
              </div>
              <div>
                <Text strong>姓名：</Text>
                <Text>{user?.name}</Text>
              </div>
              <div>
                <Text strong>租户ID：</Text>
                <Text>{user?.tenant_id}</Text>
              </div>
            </Space>
          </Card>

          <Card title="系统状态" variant="borderless">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong>登录状态：</Text>
                <Text style={{ color: '#52c41a' }}>已登录</Text>
              </div>
              <div>
                <Text strong>Token状态：</Text>
                <Text style={{ color: '#52c41a' }}>有效</Text>
              </div>
              <div>
                <Text strong>登录时间：</Text>
                <Text>{new Date().toLocaleString()}</Text>
              </div>
            </Space>
          </Card>

          <Card title="快速操作" variant="borderless">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Button type="primary" block>
                查看个人信息
              </Button>
              <Button block>
                修改密码
              </Button>
              <Button danger block>
                系统设置
              </Button>
            </Space>
          </Card>
        </div>
      </div>
    </MainLayout>
  );
};

export default Dashboard;
