import React, { useEffect, useState } from 'react';
import { Modal, Form, Input, Select, Button, Row, Col, message } from 'antd';
import { createTeacher, updateTeacher, type TeacherResponse, type TeacherCreate, type TeacherUpdate } from '../../../services/system/teacher';

const { TextArea } = Input;

interface TeacherFormProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  teacher: TeacherResponse | null;
  tenantId: number;
}

const TeacherForm: React.FC<TeacherFormProps> = ({
  visible,
  onCancel,
  onSuccess,
  teacher,
  tenantId
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (visible) {
      if (teacher) {
        // 编辑模式，设置表单初始值
        form.setFieldsValue({
          name: teacher.name,
          gender: teacher.gender,
          intro: teacher.intro || '', // 简介字段
          notes: teacher.notes || '', // 备注字段
        });
      } else {
        // 添加模式，重置表单
        form.resetFields();
        // 设置默认值
        form.setFieldsValue({
          gender: 0, // 默认未知
        });
      }
    }
  }, [teacher, visible, form]);

  const handleSubmit = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      
      if (teacher) {
        // 编辑模式
        const updateData: TeacherUpdate = {
          name: values.name,
          gender: values.gender,
          intro: values.intro,
          notes: values.notes,
        };
        await updateTeacher(teacher.id, updateData);
        message.success('老师信息更新成功');
      } else {
        // 添加模式
        const createData: TeacherCreate = {
          tenant_id: tenantId,
          name: values.name,
          avatar: '', // 创建时需要提供avatar字段，可以先设为空字符串
          gender: values.gender,
          intro: values.intro,
          notes: values.notes,
          active: 1,
        };
        await createTeacher(createData);
        message.success('老师创建成功');
      }
      
      onSuccess();
    } catch (error) {
      console.error('保存老师信息失败:', error);
      message.error('保存失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title={teacher ? '编辑老师' : '添加老师'}
      open={visible}
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" loading={loading} onClick={handleSubmit}>
          确定
        </Button>,
      ]}
      destroyOnHidden
    >
      <Form
        form={form}
        layout="vertical"
        style={{ marginTop: 16 }}
        initialValues={{
          gender: 0,
        }}
      >
        {/* 姓名和性别在同一行 */}
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="name"
              label="姓名"
              rules={[
                { required: true, message: '请输入老师姓名' },
                { max: 50, message: '老师姓名不能超过50个字符' }
              ]}
            >
              <Input placeholder="请输入老师姓名" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="gender"
              label="性别"
              rules={[{ required: true, message: '请选择性别' }]}
            >
              <Select placeholder="请选择性别">
                <Select.Option value={0}>未知</Select.Option>
                <Select.Option value={1}>男</Select.Option>
                <Select.Option value={2}>女</Select.Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="intro"
          label="简介"
          rules={[{ max: 500, message: '老师简介不能超过500个字符' }]}
        >
          <TextArea
            rows={4}
            placeholder="请输入老师的简介信息"
            maxLength={500}
            showCount
          />
        </Form.Item>

        <Form.Item
          name="notes"
          label="备注"
        >
          <TextArea
            placeholder="请输入备注信息（可选）"
            rows={3}
            maxLength={500}
            showCount
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default TeacherForm; 