import { get, post, put, del } from '../api';

// 教师响应数据类型
export interface TeacherResponse {
  id: number;
  tenant_id: number;
  name: string;
  gender: number; // 0：未知；1：男；2：女
  avatar: string;
  intro?: string | null; // 简介
  notes?: string | null; // 备注
  ctime: string;
  active: number; // 0：失效；1：有效
}

// 创建教师数据类型
export interface TeacherCreate {
  tenant_id: number;
  name: string;
  avatar: string; // 头像路径，根据API文档是必填字段
  gender?: number; // 默认值为0
  intro?: string;
  notes?: string;
  active?: number; // 默认值为1
}

// 更新教师数据类型
export interface TeacherUpdate {
  name?: string;
  gender?: number;
  avatar?: string;
  intro?: string;
  notes?: string;
  active?: number;
}

// 分页响应类型
export interface TeacherListResponse {
  items: TeacherResponse[];
  total: number;
}

// 查询参数类型
export interface TeacherQuery {
  tenant_id: number;
  skip?: number;
  limit?: number;
  name?: string;
  intro?: string;
  notes?: string;
  start_time?: string;
  end_time?: string;
  sort_by?: 'id' | 'ctime';
  sort_order?: 'asc' | 'desc';
}

// 头像上传URL响应类型
export interface AvatarUploadUrlResponse {
  upload_url: string;
  file_path: string;
  file_url?: string;
  expires?: number;
}

// 头像上传URL请求类型
export interface AvatarUploadUrlRequest {
  tenant_id: number;
  file_name: string;
}

// 获取教师列表
export const getTeachers = async (tenantId: number, params?: Omit<TeacherQuery, 'tenant_id'>): Promise<TeacherListResponse> => {
  const queryParams = {
    tenant_id: tenantId,
    ...params
  };
  return await get<TeacherListResponse>('/sys/teacher', queryParams);
};

// 获取单个教师
export const getTeacher = async (id: number): Promise<TeacherResponse> => {
  return await get<TeacherResponse>(`/sys/teacher/${id}`);
};

// 创建教师
export const createTeacher = async (data: TeacherCreate): Promise<TeacherResponse> => {
  return await post<TeacherResponse>('/sys/teacher', data);
};

// 更新教师
export const updateTeacher = async (id: number, data: TeacherUpdate): Promise<TeacherResponse> => {
  return await put<TeacherResponse>(`/sys/teacher/${id}`, data);
};

// 获取头像上传URL
export const getTeacherAvatarUploadUrl = async (params: AvatarUploadUrlRequest): Promise<AvatarUploadUrlResponse> => {
  return await get<AvatarUploadUrlResponse>('/sys/teacher/avatar/upload-url', params);
};

// 更新教师头像
export const updateTeacherAvatar = async (teacherId: number, avatarPath: string): Promise<TeacherResponse> => {
  if (teacherId === undefined || avatarPath === undefined || avatarPath === null || avatarPath === '') {
    throw new Error('Invalid parameters for updating teacher avatar');
  }

  const requestBody = { avatar: avatarPath };
  return await put<TeacherResponse>(`/sys/teacher/${teacherId}`, requestBody);
};

// 删除教师
export const deleteTeacher = async (id: number): Promise<void> => {
  return await del<void>(`/sys/teacher/${id}`);
};

// 删除头像文件
export const deleteTeacherAvatarFile = async (filePath: string): Promise<void> => {
  if (!filePath) {
    throw new Error('File path is required for deleting avatar file');
  }

  return await del<void>('/sys/teacher/avatar', { 
    data: { file_path: filePath } 
  });
}; 