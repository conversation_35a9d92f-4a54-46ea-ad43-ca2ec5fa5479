import React from 'react';
import { Drawer, Space, Typography, Empty } from 'antd';
import { TagOutlined } from '@ant-design/icons';
import './TargetDrawer.css';

const { Title, Text } = Typography;

export interface ModuleItem {
  name: string;
}

export interface FrameworkItem {
  name: string;
  logo?: string | null;
  module_list?: ModuleItem[];
}

export interface TargetDrawerProps {
  visible: boolean;
  onClose: () => void;
  frameworks: FrameworkItem[];
  title?: string;
  width?: string | number;
}

const TargetDrawer: React.FC<TargetDrawerProps> = ({
  visible,
  onClose,
  frameworks,
  title = '练习目标',
  width = 600
}) => {
  return (
    <Drawer
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <TagOutlined style={{ color: '#21808d', fontSize: '18px' }} />
          <span>{title}</span>
        </div>
      }
      placement="right"
      onClose={onClose}
      open={visible}
      width={width}
      className="target-drawer"
    >
      <div className="drawer-content">
        {frameworks && frameworks.length > 0 ? (
          <Space direction="vertical" style={{ width: '100%' }} size="large">
            {frameworks.map((framework, frameworkIndex) => (
              <div key={frameworkIndex} className="framework-section">
                <div className="framework-header">
                  <div className="framework-title">
                    {framework.logo && (
                      <img 
                        src={framework.logo} 
                        alt={framework.name}
                        className="framework-logo"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                        }}
                      />
                    )}
                    <Title level={4} style={{ margin: 0 }}>
                      {framework.name}
                    </Title>
                  </div>
                </div>
                
                {framework.module_list && framework.module_list.length > 0 && (
                  <div className="modules-container">
                    <div className="modules-grid">
                      {framework.module_list.map((module, moduleIndex) => (
                        <div key={moduleIndex} className="module-item">
                          <Text className="module-name">{module.name}</Text>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </Space>
        ) : (
          <Empty description="暂无练习目标" image={Empty.PRESENTED_IMAGE_SIMPLE} />
        )}
      </div>
    </Drawer>
  );
};

export default TargetDrawer;