import { get, post, put, del } from '../api';

// 人物角色响应数据类型
export interface CharacterResponse {
  id: number;
  tenant_id: number;
  name: string;
  gender: number; // 0：未知；1：男；2：女
  avatar: string;
  profile: string;
  timbre_type: number; // 0：未设置；1：火山引擎
  timbre?: string | null;
  notes?: string | null;
  pv_profile?: string | null; // 人物资料（提示词变量）
  pv_ability?: string | null; // 人物能力（提示词变量）
  pv_restriction?: string | null; // 人物限制（提示词变量）
  published: number; // 0：未发布；1：已发布
  ctime: string;
  active: number; // 0：失效；1：有效
}

// 创建人物角色数据类型
export interface CharacterCreate {
  tenant_id: number;
  name: string;
  profile: string; // 必填字段
  gender?: number;
  avatar?: string;
  timbre_type?: number;
  timbre?: string;
  notes?: string;
  published?: number;
  active?: number;
}

// 更新人物角色数据类型
export interface CharacterUpdate {
  name?: string;
  gender?: number;
  avatar?: string;
  profile?: string;
  timbre_type?: number;
  timbre?: string;
  notes?: string;
  pv_profile?: string;
  pv_ability?: string;
  pv_restriction?: string;
  published?: number;
  active?: number;
}

// 分页响应类型
export interface CharacterListResponse {
  items: CharacterResponse[];
  total: number;
}

// 查询参数类型
export interface CharacterQuery {
  tenant_id: number;
  skip?: number;
  limit?: number;
  active?: number;
  published?: number;
  name?: string;
  profile?: string;
  notes?: string;
  sort_by?: 'id' | 'ctime';
  sort_order?: 'asc' | 'desc';
}

// 头像上传URL响应类型
export interface AvatarUploadUrlResponse {
  upload_url: string;
  file_path: string;
  file_url?: string;
  expires?: number;
}

// 头像上传URL请求类型
export interface AvatarUploadUrlRequest {
  tenant_id: number;
  file_name: string;
}

// 获取人物角色列表
export const getCharacters = async (tenantId: number, params?: Omit<CharacterQuery, 'tenant_id'>): Promise<CharacterListResponse> => {
  const queryParams = {
    tenant_id: tenantId,
    ...params
  };
  return await get<CharacterListResponse>('/sys/character', queryParams);
};

// 获取单个人物角色
export const getCharacter = async (id: number, tenantId: number): Promise<CharacterResponse> => {
  return await get<CharacterResponse>(`/sys/character/${id}`, { tenant_id: tenantId });
};

// 创建人物角色
export const createCharacter = async (data: CharacterCreate): Promise<CharacterResponse> => {
  return await post<CharacterResponse>('/sys/character', data);
};

// 更新人物角色
export const updateCharacter = async (id: number, data: CharacterUpdate, tenantId: number): Promise<CharacterResponse> => {
  return await put<CharacterResponse>(`/sys/character/${id}`, data, { params: { tenant_id: tenantId } });
};

// 获取头像上传URL
export const getCharacterAvatarUploadUrl = async (params: AvatarUploadUrlRequest): Promise<AvatarUploadUrlResponse> => {
  return await get<AvatarUploadUrlResponse>('/sys/character/avatar/upload-url', params);
};

// 更新人物角色头像
export const updateCharacterAvatar = async (characterId: number, avatarPath: string, tenantId: number): Promise<CharacterResponse> => {
  if (characterId === undefined || avatarPath === undefined || avatarPath === null || avatarPath === '') {
    throw new Error('Invalid parameters for updating character avatar');
  }

  const requestBody = { avatar: avatarPath };
  return await put<CharacterResponse>(`/sys/character/${characterId}`, requestBody, { params: { tenant_id: tenantId } });
};

// 删除头像文件
export const deleteCharacterAvatarFile = async (filePath: string): Promise<void> => {
  if (!filePath) {
    throw new Error('File path is required for deleting avatar file');
  }

  return await del<void>('/sys/character/avatar', { 
    data: { file_path: filePath } 
  });
}; 