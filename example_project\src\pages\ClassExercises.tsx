import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Card, Typography, Spin, Empty, Avatar, Tooltip, Tag, Button, App } from 'antd';
import { LockOutlined, BookOutlined, UserOutlined, ClockCircleOutlined, InfoCircleOutlined, RollbackOutlined } from '@ant-design/icons';
import { ReactFlow, Background, Controls, Handle, Position, type Node, type Edge } from '@xyflow/react';
import '@xyflow/react/dist/style.css';
import { MainLayout } from '../layouts';
import { getClassExercises, createExerciseLog, type ClassExerciseInfo } from '../services/classExercises';
import { useParams, useNavigate } from 'react-router-dom';
import '../styles/ClassExercises.css';

const { Title, Text } = Typography;

// 自定义节点组件
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const ExerciseNode = ({ data }: { data: any }) => {
  const { exercise, isEnabled, index, exercises, getTypeTag, getStatusTag, formatTime, navigate, classId, modal } = data;

  // 处理卡片点击事件
  const handleCardClick = async () => {
    if (!isEnabled) return;
    
    // 如果是作业单类型（e_type为1）
    if (exercise.e_type === 1) {
      // 如果el_id为null，表示首次进入练习，弹出确认框
      if (exercise.el_id === null) {
        modal.confirm({
          title: '开始练习',
          content: '您要开始此练习吗？',
          okText: '确认',
          cancelText: '取消',
          onOk: async () => {
            try {
              // 调用POST接口创建练习记录
              await createExerciseLog({
                cid: parseInt(classId),
                eid: exercise.e_id
              });
              // 创建成功后跳转到Worksheet页面
              navigate(`/worksheet/${exercise.w_id}/${classId}`);
            } catch (error) {
              console.error('创建练习记录失败:', error);
              // 这里可以显示错误消息
            }
          }
        });
      } else {
        // 如果已有练习记录，直接跳转
        navigate(`/worksheet/${exercise.w_id}/${classId}`);
      }
    }
    // 如果是角色扮演类型（e_type为2）
    else if (exercise.e_type === 2) {
      // 如果el_id为null，表示首次进入练习，弹出确认框
      if (exercise.el_id === null) {
        modal.confirm({
          title: '开始练习',
          content: '您要开始此练习吗？',
          okText: '确认',
          cancelText: '取消',
          onOk: async () => {
            try {
              // 调用POST接口创建练习记录
              await createExerciseLog({
                cid: parseInt(classId),
                eid: exercise.e_id
              });
              // 创建成功后跳转到Scene页面
              navigate(`/scene/${exercise.s_id}/${classId}`);
            } catch (error) {
              console.error('创建练习记录失败:', error);
              // 这里可以显示错误消息
            }
          }
        });
      } else {
        // 如果已有练习记录，直接跳转
        navigate(`/scene/${exercise.s_id}/${classId}`);
      }
    }
  };

  return (
    <div style={{ position: 'relative' }}>
      {/* 输入句柄 - 除了第一个节点，其他节点都有输入句柄 */}
      {index > 0 && (
        <Handle
          type="target"
          position={Position.Left}
          style={{
            background: '#b1b1b1',
            width: '0px',
            height: '0px',
            border: '2px solid #fff',
          }}
        />
      )}

      {/* 输出句柄 - 除了最后一个节点，其他节点都有输出句柄 */}
      {index < exercises.length - 1 && (
        <Handle
          type="source"
          position={Position.Right}
          style={{
            background: '#b1b1b1',
            width: '0px',
            height: '0px',
            border: '2px solid #fff',
          }}
        />
      )}

      <Card
        hoverable={isEnabled}
        onClick={handleCardClick}
        style={{
          width: '280px',
          opacity: isEnabled ? 1 : 0.6,
          filter: isEnabled ? 'none' : 'grayscale(50%)',
          position: 'relative',
          cursor: isEnabled ? 'pointer' : 'not-allowed',
        }}
        cover={
          exercise.e_pic ? (
            <img
              alt={exercise.e_title}
              src={exercise.e_pic}
              style={{ height: '160px', objectFit: 'cover' }}
            />
          ) : (
            <div
              style={{
                height: '160px',
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <Avatar size={48} icon={<BookOutlined />} style={{ backgroundColor: 'rgba(255,255,255,0.2)' }} />
            </div>
          )
        }
      actions={[
        <div key="teacher" style={{ padding: '0 16px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <Tooltip title={exercise.t_intro || '暂无老师简介'}>
            {exercise.t_avatar ? (
              <Avatar size={24} src={exercise.t_avatar} style={{ marginRight: '8px' }} />
            ) : (
              <Avatar size={24} icon={<UserOutlined />} style={{ marginRight: '8px' }} />
            )}
          </Tooltip>
          <Text type="secondary" style={{ fontSize: '14px' }}>
            {exercise.t_name || '未分配老师'}
          </Text>
        </div>,
        exercise.e_duration && (
          <div key="duration" style={{ padding: '0 16px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <ClockCircleOutlined style={{ marginRight: '4px', color: '#8c8c8c' }} />
            <Text type="secondary" style={{ fontSize: '14px' }}>
              约 {exercise.e_duration} 分钟
            </Text>
          </div>
        )
      ].filter(Boolean)}
    >
      <Card.Meta
        title={
          <div>
            {/* e_type标签放在左上角 */}
            <div style={{ position: 'absolute', top: '4px', left: '8px', zIndex: 5 }}>
              {getTypeTag(exercise.e_type)}
            </div>
            
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '8px', paddingTop: 'px' }}>
              <div style={{ display: 'flex', alignItems: 'center', flex: 1, paddingRight: '8px' }}>
                <Text strong style={{ fontSize: '18px' }}>
                  {exercise.e_title}
                </Text>
              </div>
              {/* info icon 右对齐 */}
              <div style={{ flexShrink: 0 }}>
                {exercise.e_intro && (
                  <Tooltip title={exercise.e_intro}>
                    <InfoCircleOutlined style={{ color: '#8c8c8c', fontSize: '14px' }} />
                  </Tooltip>
                )}
              </div>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              {getStatusTag(exercise.el_status)}
              {exercise.el_status === 1 && exercise.el_utime && (
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  上次练习: {formatTime(exercise.el_utime)}
                </Text>
              )}
              {exercise.el_status === 2 && exercise.el_stime && (
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  提交时间: {formatTime(exercise.el_stime)}
                </Text>
              )}
            </div>
          </div>
        }
      />
      
      {/* 锁定图标 */}
      {!isEnabled && (
        <Tooltip title="请提交前一练习后，方能解锁！">
          <div
            style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              background: 'rgba(0, 0, 0, 0.7)',
              borderRadius: '50%',
              width: '60px',
              height: '60px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 10,
            }}
          >
            <LockOutlined style={{ fontSize: '24px', color: '#fff' }} />
          </div>
        </Tooltip>
      )}
    </Card>
    </div>
  );
};

// 定义节点类型 - 移到组件外部避免重复创建
const nodeTypes = {
  exerciseNode: ExerciseNode,
};

const ClassExercises: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { message, modal } = App.useApp();
  const [exercises, setExercises] = useState<ClassExerciseInfo[]>([]);
  const [className, setClassName] = useState<string>('');
  const [loading, setLoading] = useState(true);

  // 获取练习数据
  const fetchExercises = async () => {
    if (!id) {
      message.error('班级ID不能为空');
      return;
    }

    try {
      setLoading(true);
      const response = await getClassExercises(parseInt(id));
      setExercises(response.exercises);
      setClassName(response.class_name);
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      console.error('获取练习列表失败:', error);
      
      let errorMessage = '获取练习列表失败，请稍后重试';
      if (error.response) {
        const status = error.response.status;
        if (status === 401) {
          errorMessage = '登录已过期，请重新登录';
        } else if (status === 403) {
          errorMessage = '没有权限访问该班级练习';
        } else if (status === 404) {
          errorMessage = '班级不存在或无权访问';
        } else if (status === 500) {
          errorMessage = '服务器内部错误，请稍后重试';
        }
      } else if (error.request) {
        errorMessage = '网络连接失败，请检查网络设置';
      }
      
      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchExercises();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id]);

  // 判断练习是否可用
  const isExerciseEnabled = (exercise: ClassExerciseInfo, index: number) => {
    // 如果当前练习不依赖前一个节点，则永远可用
    if (exercise.depend === 0) return true;
    
    // 如果当前练习依赖前一个节点
    if (exercise.depend === 1) {
      // 如果是第一个节点，则可用
      if (index === 0) return true;
      
      // 否则检查前一个节点的状态是否为已提交
      const prevExercise = exercises[index - 1];
      return prevExercise.el_status === 2;
    }
    
    return true;
  };

  // 获取练习状态标签
  const getStatusTag = useCallback((status: number) => {
    switch (status) {
      case 0:
        return <Tag color="#8c8c8c">待练习</Tag>;
      case 1:
        return <Tag color="#21808d">练习中</Tag>;
      case 2:
        return <Tag color="#13343b">已提交</Tag>;
      default:
        return <Tag color="#8c8c8c">未开始</Tag>;
    }
  }, []);

  // 获取练习类型标签
  const getTypeTag = useCallback((type: number) => {
    switch (type) {
      case 1:
        return <Tag color="blue">作业单</Tag>;
      case 2:
        return <Tag color="purple">角色扮演</Tag>;
      default:
        return <Tag color="default">未知类型</Tag>;
    }
  }, []);

  // 格式化时间显示
  const formatTime = useCallback((timeString: string | null) => {
    if (!timeString) return '';
    const date = new Date(timeString);
    return date.toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  }, []);

  // 生成React Flow的节点和边数据
  const { nodes, edges } = useMemo(() => {
    const nodes: Node[] = [];
    const edges: Edge[] = [];

    exercises.forEach((exercise, index) => {
      const isEnabled = isExerciseEnabled(exercise, index);
      
      // 计算节点位置（水平排列）
      const x = index * 400 + 100;
      const y = 200;

      const nodeId = `exercise-${exercise.e_id}`;
      nodes.push({
        id: nodeId,
        type: 'exerciseNode',
        position: { x, y },
        data: {
          exercise,
          index,
          isEnabled,
          exercises,
          getTypeTag,
          getStatusTag,
          formatTime,
          navigate,
          classId: id,
          modal,
        },
        draggable: false,
      });

      // 创建边（连接线）
      if (index < exercises.length - 1) {
        const nextExercise = exercises[index + 1];
        const sourceId = `exercise-${exercise.e_id}`;
        const targetId = `exercise-${nextExercise.e_id}`;
        
        // The logic from the failed replace block is to base styling on the next node.
        const isNextEnabled = isExerciseEnabled(nextExercise, index + 1);
        
        // 连接线颜色基于目标节点状态
        const strokeColor = isNextEnabled ? '#5f5f5f' : '#b1b1b1';
        // 动画效果：从前一个节点流向后一个节点
        
        const edgeId = `edge-${exercise.e_id}-${nextExercise.e_id}`;
        edges.push({
          id: edgeId,
          source: sourceId,
          target: targetId,
          type: 'smoothstep',
          animated: true,
          style: {
            stroke: strokeColor,
            strokeWidth: 2,
            strokeDasharray: '5,5', // 虚线样式
          },
        });
      }
    });

    return { nodes, edges };
  }, [exercises, id, navigate, modal, getTypeTag, getStatusTag, formatTime]);

  return (
    <MainLayout>
      <div style={{ height: 'calc(100vh - 48px)', display: 'flex', flexDirection: 'column' }}>
        {loading ? (
          <div style={{ 
            display: 'flex', 
            flexDirection: 'column',
            justifyContent: 'center', 
            alignItems: 'center', 
            height: '100%',
            minHeight: '60vh'
          }}>
            <Spin size="large" />
            <div style={{ marginTop: '16px' }}>
              <Text type="secondary">正在加载练习信息...</Text>
            </div>
          </div>
        ) : exercises.length === 0 ? (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="暂无练习信息"
            style={{ padding: '50px' }}
          />
        ) : (
          <div style={{ flex: 1, width: '100%', position: 'relative' }}>
            <ReactFlow
              nodes={nodes}
              edges={edges}
              nodeTypes={nodeTypes}
              fitView
              fitViewOptions={{ padding: 0.2, minZoom: 0.6, maxZoom: 1 }}
              nodesDraggable={false}
              nodesConnectable={false}
              elementsSelectable={true}
              panOnDrag={true}
              zoomOnScroll={true}
              zoomOnPinch={true}
              minZoom={0.6}
              maxZoom={1}
              proOptions={{ hideAttribution: true }}
              style={{ userSelect: 'none' }}
            >
              <Background />
              <Controls />
              {/* 左上角班级名称 */}
              <div style={{
                position: 'absolute',
                left: '8px',
                zIndex: 1000,
                background: 'rgba(255, 255, 255, 0.9)',
                padding: '16px',
                borderRadius: '8px',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                userSelect: 'none'
              }}>
                <Title level={3} style={{ margin: 0, marginBottom: '8px', fontSize: '28px' }}>{className || '练习流程'}</Title>
                <Text type="secondary" style={{ fontSize: '16px' }}>
                  请按以下计划进行练习
                </Text>
              </div>
              {/* 右上角返回按钮 */}
              <div style={{
                position: 'absolute',
                top: '10px',
                right: '10px',
                zIndex: 1000
              }}>
                <Tooltip title="返回" placement='left'>
                  <Button
                    type="text"
                    icon={<RollbackOutlined />}
                    onClick={() => navigate('/training-camp')}
                    style={{
                      width: '48px',
                      height: '48px',
                      borderRadius: '12px',
                      background: 'rgba(255, 255, 255, 0.9)',
                      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}
                  />
                </Tooltip>
              </div>
            </ReactFlow>
          </div>
        )}
      </div>
    </MainLayout>
  );
};

export default ClassExercises;