import { FaMicrophone, FaPaperPlane } from 'react-icons/fa';
import { HiOutlineAtSymbol } from 'react-icons/hi2';
import {
  RollbackOutlined,
  ClockCircleOutlined,
  UserOutlined,
  CompassOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  RobotOutlined,
  RedoOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
} from '@ant-design/icons';

// 功能图标常量
export const SCENE_ICONS = {
  // 功能栏图标
  AT: <HiOutlineAtSymbol />,
  MICROPHONE: <FaMicrophone />,
  SEND: <FaPaperPlane />,
  
  // 导航图标
  BACK: <RollbackOutlined />,
  GUIDE: <CompassOutlined />,
  MENU_FOLD: <MenuFoldOutlined />,
  MENU_UNFOLD: <MenuUnfoldOutlined />,
  
  // 状态图标
  CLOCK: <ClockCircleOutlined />,
  USER: <UserOutlined />,
  ROBOT: <RobotOutlined />,
  REDO: <RedoOutlined />,
  FULLSCREEN: <FullscreenOutlined />,
  FULLSCREEN_EXIT: <FullscreenExitOutlined />,
} as const;

// 图标配置类型
export type SceneIconType = keyof typeof SCENE_ICONS;