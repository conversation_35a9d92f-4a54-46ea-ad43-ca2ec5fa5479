import { useState, useEffect, useImperativeHandle, forwardRef, useCallback } from 'react';
import { Form, message } from 'antd';
import type { FormInstance } from 'antd';
import RichTextEditor from '../../../components/RichTextEditor';
import { updateWorksheet } from '../../../services/system/worksheet';
import { showError } from '../../../utils/errorHandler';

interface WorksheetBackgroundTabProps {
  form: FormInstance;
  worksheetId?: number;
  initialBgtext?: string;
  onSaveSuccess?: (updatedBgtext: string) => void;
}

export interface WorksheetBackgroundTabRef {
  hasUnsavedChanges: () => boolean;
  saveContent: () => Promise<void>;
  resetToInitial: () => void;
}

const WorksheetBackgroundTab = forwardRef<WorksheetBackgroundTabRef, WorksheetBackgroundTabProps>(
  ({ form, worksheetId, initialBgtext = '', onSaveSuccess }, ref) => {
    const [currentContent, setCurrentContent] = useState<string>(initialBgtext);
    const [initialContent, setInitialContent] = useState<string>(initialBgtext);

    // 同步内容到表单（仅在必要时）
    const syncToForm = useCallback((content: string) => {
      form.setFieldValue('bgtext', content);
    }, [form]);

    // 当初始内容变化时更新状态
    useEffect(() => {
      setCurrentContent(initialBgtext);
      setInitialContent(initialBgtext);
      // 同步更新表单值，但只在初始化时
      syncToForm(initialBgtext);
    }, [initialBgtext, syncToForm]);

    // 检查是否有未保存的更改
    const hasUnsavedChanges = () => {
      return currentContent !== initialContent;
    };

    // 处理内容变化
    const handleContentChange = useCallback((content: string) => {
      setCurrentContent(content);
      // 不再直接操作表单值，避免循环引用
    }, []);

    // 保存背景介绍内容
    const handleSaveBgtext = useCallback(async (content: string) => {
      if (!worksheetId) {
        message.error('作业单ID不存在，无法保存');
        return;
      }

      try {
        await updateWorksheet(worksheetId, { bgtext: content });
        // 保存成功后同步所有状态
        setCurrentContent(content);  // 确保当前内容也更新
        setInitialContent(content);  // 更新初始内容
        syncToForm(content);         // 同步到表单
        message.success('背景介绍已保存');
        if (onSaveSuccess) {
          onSaveSuccess(content);
        }
      } catch (error) {
        showError(error, '保存背景介绍失败');
        throw error; // 重新抛出错误以便调用者知道保存失败
      }
    }, [worksheetId, syncToForm, onSaveSuccess]);

    // 重置到初始内容
    const resetToInitial = useCallback(() => {
      setCurrentContent(initialContent);
      syncToForm(initialContent);
    }, [initialContent, syncToForm]);

    // 公开的保存方法
    const saveContent = useCallback(async () => {
      await handleSaveBgtext(currentContent);
    }, [handleSaveBgtext, currentContent]);

    // 暴露方法给父组件
    useImperativeHandle(ref, () => ({
      hasUnsavedChanges,
      saveContent,
      resetToInitial
    }));

  return (
    <div
      className="rich-text-editor-form-wrapper"
      style={{
        width: '100%',
        padding: '0px',
        height: '100%',
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      <Form
        form={form}
        layout="vertical"
        style={{
          width: '100%',
          height: '100%',
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        <Form.Item
          name="bgtext"
          style={{
            height: '100%',
            width: '100%',
            flex: 1,
            marginBottom: 0,
            display: 'flex',
            flexDirection: 'column'
          }}
        >
          <RichTextEditor
            title="背景介绍"
            placeholder="请输入背景介绍内容..."
            saveTooltip="保存背景介绍"
            fullscreenTooltip="全屏编辑背景介绍"
            exitFullscreenTooltip="退出全屏"
            toolbarMode="full"
            value={currentContent}
            onChange={handleContentChange}
            onSave={handleSaveBgtext}
            showSaveButton={true}
            showFullscreenButton={true}
          />
        </Form.Item>
      </Form>
    </div>
  );
});

WorksheetBackgroundTab.displayName = 'WorksheetBackgroundTab';

export default WorksheetBackgroundTab; 