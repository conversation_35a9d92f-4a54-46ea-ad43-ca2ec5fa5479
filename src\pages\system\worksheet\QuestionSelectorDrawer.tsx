import React, { useState, useEffect } from 'react';
import { 
  Drawer, 
  Form, 
  Input, 
  Select, 
  Button, 
  List, 
  Checkbox, 
  Space, 
  Spin, 
  message,
  Typography,
  Empty,
  Tooltip
} from 'antd';
import { ProCard } from '@ant-design/pro-components';
import { SearchOutlined, ClearOutlined, DeleteOutlined, ReloadOutlined, CommentOutlined, HolderOutlined } from '@ant-design/icons';
import { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors, DragEndEvent } from '@dnd-kit/core';
import { arrayMove, SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { 
  getQuestions, 
  type QuestionResponse, 
  type QuestionListParams 
} from '../../../services/system/question';
import { 
  getSubjects, 
  type SubjectResponse 
} from '../../../services/system/subject';
import { 
  updateUnitQuestions, 
  type UnitQuestionsUpdateRequest
} from '../../../services/system/worksheet';
import { getGlobalTenantInfo } from '../../../components/GlobalTenantSelector';
import { showError } from '../../../utils/errorHandler';

const { Text } = Typography;

interface QuestionSelectorDrawerProps {
  visible: boolean;
  worksheetId?: string;
  unitId?: number;
  unitName?: string;
  initialSelectedQuestions?: SelectedQuestion[];
  onClose: () => void;
  onSuccess: () => void;
}

interface SelectedQuestion {
  qid: number;
  notes?: string;
}

// 可排序的已选问题项组件
const SortableSelectedQuestionItem: React.FC<{
  selectedQ: SelectedQuestion;
  question: QuestionResponse | undefined;
  index: number;
  onRemove: (qid: number) => void;
}> = ({ selectedQ, question, index, onRemove }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: selectedQ.qid });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  if (!question) return null;

    return (
    <div ref={setNodeRef} style={style} {...attributes}>
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' }}>
        {/* 可拖拽的列表项主体 */}
        <div
          style={{
            backgroundColor: '#fafafa',
            border: '1px solid #f0f0f0',
            borderRadius: '6px',
            padding: '12px',
            display: 'flex',
            alignItems: 'center',
            flex: 1,
            opacity: isDragging ? 0.5 : 1,
            cursor: isDragging ? 'grabbing' : 'grab',
          }}
          {...listeners}
        >
          <div style={{ 
            marginRight: '12px', 
            color: '#999',
            fontSize: '14px',
            cursor: isDragging ? 'grabbing' : 'grab'
          }}>
            <HolderOutlined />
          </div>
          <div style={{ 
            backgroundColor: '#1890ff',
            color: 'white',
            width: '20px',
            height: '20px',
            borderRadius: '2px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '12px',
            fontWeight: 'bold',
            marginRight: '12px'
          }}>
            {index + 1}
          </div>
          <div style={{ flex: 1 }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <Text>
                {question.title}
              </Text>
              {question.notes && (
                <Tooltip title={`备注：${question.notes}`}>
                  <CommentOutlined style={{ color: '#999', fontSize: '14px' }} />
                </Tooltip>
              )}
            </div>
          </div>
        </div>
        
        {/* 独立的删除按钮，在列表项外部 */}
        <Tooltip title="删除问题">
          <Button 
            type="text" 
            size="small" 
            danger
            icon={<DeleteOutlined />}
            onClick={() => onRemove(selectedQ.qid)}
            style={{ 
              flexShrink: 0,
              opacity: isDragging ? 0.5 : 1
            }}
          />
        </Tooltip>
      </div>
    </div>
  );
};

const QuestionSelectorDrawer: React.FC<QuestionSelectorDrawerProps> = ({
  visible,
  worksheetId,
  unitId,
  unitName,
  initialSelectedQuestions = [],
  onClose,
  onSuccess
}) => {
  const [form] = Form.useForm();
  const [questions, setQuestions] = useState<QuestionResponse[]>([]);
  const [subjects, setSubjects] = useState<SubjectResponse[]>([]);
  const [selectedQuestions, setSelectedQuestions] = useState<SelectedQuestion[]>([]);
  // 已选问题的详情缓存，独立于搜索结果
  const [selectedQuestionsCache, setSelectedQuestionsCache] = useState<Map<number, QuestionResponse>>(new Map());
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [subjectsLoading, setSubjectsLoading] = useState(false);
  const [subjectsLoaded, setSubjectsLoaded] = useState(false);
  const [selectedSubjectId, setSelectedSubjectId] = useState<number | undefined>(undefined);

  // 拖拽传感器
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // 处理拖拽结束
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id && over) {
      setSelectedQuestions((items) => {
        const oldIndex = items.findIndex((item) => item.qid === active.id);
        const newIndex = items.findIndex((item) => item.qid === over.id);

        return arrayMove(items, oldIndex, newIndex);
      });
    }
  };

  // 获取当前租户ID
  const getCurrentTenantId = (): number | null => {
    const tenantInfo = getGlobalTenantInfo();
    return tenantInfo?.id || null;
  };

  // 获取科目列表
  const fetchSubjects = async () => {
    const tenantId = getCurrentTenantId();
    if (!tenantId || subjectsLoaded) return;

    try {
      setSubjectsLoading(true);
      const response = await getSubjects(tenantId, { 
        limit: 100,
        sort_by: 'id',
        sort_order: 'desc',
      });
      setSubjects(response.items);
      setSubjectsLoaded(true);
    } catch (error) {
      showError(error, '获取科目列表失败');
    } finally {
      setSubjectsLoading(false);
    }
  };

  // 获取问题列表
  const fetchQuestions = async (params?: { title?: string; subject_id?: number; notes?: string }) => {
    const tenantId = getCurrentTenantId();
    if (!tenantId) {
      message.error('请先选择租户');
      return;
    }

    setLoading(true);
    try {
      const queryParams: Omit<QuestionListParams, 'tenant_id'> = {
        active: 1, // 只获取激活的问题
        limit: 100, // 限制数量
        sort_by: 'ctime',
        sort_order: 'desc',
        ...params
      };

      const response = await getQuestions(tenantId, queryParams);
      setQuestions(response.items);
      
      // 将搜索结果添加到已选问题缓存中
      setSelectedQuestionsCache(prev => {
        const newCache = new Map(prev);
        response.items.forEach(item => {
          newCache.set(item.id, item);
        });
        return newCache;
      });
    } catch (error) {
      showError(error, '获取问题列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 重置搜索条件（不重置已选问题）
  const handleReset = () => {
    // 重置表单字段
    form.resetFields();
    
    // 重置科目状态
    setSubjects([]);
    setSubjectsLoaded(false);
    setSelectedSubjectId(undefined);
    
    // 重新获取默认问题列表
    fetchQuestions();
  };

  // 从已选问题列表中移除问题（用于左侧删除按钮）
  const handleRemoveSelectedQuestion = (questionId: number) => {
    setSelectedQuestions(prev => prev.filter(q => q.qid !== questionId));
  };

  // 处理问题选择（用于右侧问题库的选择操作）
  const handleQuestionCheck = (questionId: number, checked: boolean) => {
    if (checked) {
      // 添加到已选列表
      setSelectedQuestions(prev => {
        if (!prev.some(q => q.qid === questionId)) {
          return [...prev, { qid: questionId }];
        }
        return prev;
      });
      
      // 将问题详情添加到缓存
      const question = questions.find(q => q.id === questionId);
      if (question) {
        setSelectedQuestionsCache(prev => {
          const newCache = new Map(prev);
          newCache.set(questionId, question);
          return newCache;
        });
      }
    } else {
      // 从已选列表移除
      setSelectedQuestions(prev => prev.filter(q => q.qid !== questionId));
    }
  };

  // 全选/取消全选（只操作当前搜索结果）
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      // 全选：将当前搜索结果中未选中的问题添加到已选列表
      const newQuestions = questions
        .filter(q => !selectedQuestions.some(sq => sq.qid === q.id))
        .map(q => ({ qid: q.id }));
      
      if (newQuestions.length > 0) {
        setSelectedQuestions(prev => [...prev, ...newQuestions]);
        
        // 将新选中的问题详情添加到缓存
        setSelectedQuestionsCache(prev => {
          const newCache = new Map(prev);
          questions
            .filter(q => !selectedQuestions.some(sq => sq.qid === q.id))
            .forEach(question => {
              newCache.set(question.id, question);
            });
          return newCache;
        });
      }
    } else {
      // 取消全选：将当前搜索结果中已选中的问题从已选列表中移除
      const currentQuestionIds = questions.map(q => q.id);
      setSelectedQuestions(prev => prev.filter(q => !currentQuestionIds.includes(q.qid)));
    }
  };

  // 清空所有已选问题
  const handleClearSelected = () => {
    setSelectedQuestions([]);
  };

  // 保存选中的问题
  const handleSave = async () => {
    if (selectedQuestions.length === 0) {
      message.warning('请至少选择一个问题');
      return;
    }

    if (!worksheetId || !unitId) {
      message.error('工作表ID或单元ID不可用');
      return;
    }

    const tenantId = getCurrentTenantId();
    if (!tenantId) {
      message.error('请先选择租户');
      return;
    }

    setSaving(true);
    try {
      const data: UnitQuestionsUpdateRequest = {
        tenant_id: tenantId,
        worksheet_id: parseInt(worksheetId),
        unit_id: unitId,
        questions: selectedQuestions.map((q, index) => ({
          id: q.qid,
          priority: index + 1
        }))
      };

      await updateUnitQuestions(data);
      message.success('问题更新成功');
      onSuccess();
      handleClose();
    } catch (error) {
      showError(error, '更新问题失败');
    } finally {
      setSaving(false);
    }
  };

  // 关闭抽屉
  const handleClose = () => {
    setSelectedQuestions([]);
    form.resetFields();
    setQuestions([]);
    setSubjects([]);
    setSubjectsLoaded(false);
    setSelectedSubjectId(undefined);
    setSelectedQuestionsCache(new Map());
    onClose();
  };

  // 处理科目选择
  const handleSubjectChange = (value: number | undefined) => {
    setSelectedSubjectId(value);
    // 科目选择后立即搜索
    const formValues = form.getFieldsValue();
    const params = {
      title: formValues.title?.trim() || undefined,
      subject_id: value || undefined,
      notes: formValues.notes?.trim() || undefined
    };
    fetchQuestions(params);
  };

  // 初始化数据
  useEffect(() => {
    if (visible) {
      // 设置初始化的已选问题
      setSelectedQuestions(initialSelectedQuestions || []);
      
      // 获取问题列表和初始已选问题的详情
      const initializeData = async () => {
        const tenantId = getCurrentTenantId();
        if (!tenantId) {
          message.error('请先选择租户');
          return;
        }

        setLoading(true);
        try {
          // 1. 获取常规问题列表（用于右侧搜索结果显示）
          const queryParams: Omit<QuestionListParams, 'tenant_id'> = {
            active: 1,
            limit: 100,
            sort_by: 'ctime',
            sort_order: 'desc'
          };

          const response = await getQuestions(tenantId, queryParams);
          setQuestions(response.items);
          
          // 将搜索结果添加到已选问题缓存中
          const newCache = new Map<number, QuestionResponse>();
          response.items.forEach(item => {
            newCache.set(item.id, item);
          });

          // 2. 如果有初始已选问题，需要确保它们的详情都被加载到缓存中
          if (initialSelectedQuestions && initialSelectedQuestions.length > 0) {
            const initialQids = initialSelectedQuestions.map(q => q.qid);
            const missingQids = initialQids.filter(qid => !newCache.has(qid));
            
            if (missingQids.length > 0) {
              // 获取缺失的问题详情（通过更大的limit或者专门的查询）
              const additionalQueryParams: Omit<QuestionListParams, 'tenant_id'> = {
                active: 1,
                limit: 1000, // 更大的limit以获取更多历史问题
                sort_by: 'ctime',
                sort_order: 'desc'
              };
              
              const additionalResponse = await getQuestions(tenantId, additionalQueryParams);
              additionalResponse.items.forEach(item => {
                if (initialQids.includes(item.id)) {
                  newCache.set(item.id, item);
                }
              });
            }
          }
          
          setSelectedQuestionsCache(newCache);
        } catch (error) {
          showError(error, '获取问题列表失败');
        } finally {
          setLoading(false);
        }
      };
      
      initializeData();
    }
  }, [visible, initialSelectedQuestions]);

  // 更新勾选状态的计算逻辑
  const allQuestionIds = questions.map(q => q.id);
  const selectedQuestionIds = selectedQuestions.map(q => q.qid);
  
  const isAllSelected = questions.length > 0 && allQuestionIds.every(id => selectedQuestionIds.includes(id));
  const isIndeterminate = selectedQuestionIds.some(id => allQuestionIds.includes(id)) && !isAllSelected;

  return (
    <Drawer
      title={`"${unitName || ''}" 问题列表编辑`}
      placement="right"
      width="80%"
      onClose={handleClose}
      open={visible}
      extra={
        <Space>
          <Button onClick={handleClose}>
            取消
          </Button>
          <Button 
            type="primary" 
            onClick={handleSave}
            loading={saving}
            disabled={selectedQuestions.length === 0}
          >
            保存 ({selectedQuestions.length})
          </Button>
        </Space>
      }
    >
      <ProCard split="vertical" size="small">
        {/* 左侧：已选问题列表 */}
        <ProCard 
          title={
            <span style={{ fontSize: '16px', fontWeight: 600 }}>
              已选问题
            </span>
          }
          extra={
            selectedQuestions.length > 0 && (
              <Button 
                size="small" 
                danger 
                onClick={handleClearSelected}
                icon={<ClearOutlined />}
              >
                清空
              </Button>
            )
          }
          colSpan="40%" 
          headerBordered
        >
          
          {selectedQuestions.length === 0 ? (
            <Empty
              description="暂无已选问题"
              style={{ margin: '40px 0' }}
            />
          ) : (
            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              onDragEnd={handleDragEnd}
            >
              <SortableContext 
                items={selectedQuestions.map(q => q.qid)} 
                strategy={verticalListSortingStrategy}
              >
                <div>
                  {selectedQuestions.map((selectedQ, index) => {
                    const question = selectedQuestionsCache.get(selectedQ.qid);
                    return (
                      <SortableSelectedQuestionItem
                        key={selectedQ.qid}
                        selectedQ={selectedQ}
                        question={question}
                        index={index}
                        onRemove={handleRemoveSelectedQuestion}
                      />
                    );
                  })}
                </div>
              </SortableContext>
            </DndContext>
          )}
        </ProCard>

        {/* 右侧：问题搜索和列表 */}
        <ProCard 
          title={
            <span style={{ fontSize: '16px', fontWeight: 600 }}>
              问题库搜索
            </span>
          } 
          headerBordered
        >
          {/* 搜索区域 */}
          <div style={{ marginBottom: '16px', padding: '16px', backgroundColor: '#fafafa', borderRadius: '6px' }}>
            <Form form={form} style={{ width: '100%' }}>
              <div style={{ display: 'flex', gap: '12px', marginBottom: '12px' }}>
                <Form.Item 
                  label="科目" 
                  style={{ flex: 1, marginBottom: 0 }}
                >
                  <Select
                    placeholder="选择科目"
                    allowClear
                    showSearch
                    loading={subjectsLoading}
                    value={selectedSubjectId}
                    filterOption={(input, option) => {
                      const label = option?.label || option?.children;
                      return String(label).toLowerCase().includes(input.toLowerCase());
                    }}
                    onFocus={fetchSubjects}
                    onChange={handleSubjectChange}
                    notFoundContent={subjectsLoading ? '搜索中...' : subjects.length === 0 ? '点击获取科目列表' : '暂无数据'}
                  >
                    {subjects.map(subject => (
                      <Select.Option key={subject.id} value={subject.id}>
                        {subject.name}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
                <Form.Item 
                  label="标题" 
                  name="title"
                  style={{ flex: 1, marginBottom: 0 }}
                >
                  <Input
                    placeholder="输入问题标题关键词"
                    allowClear
                    onPressEnter={() => {
                      const values = form.getFieldsValue();
                      const params = {
                        title: values.title?.trim() || undefined,
                        subject_id: selectedSubjectId || undefined,
                        notes: values.notes?.trim() || undefined
                      };
                      fetchQuestions(params);
                    }}
                  />
                </Form.Item>
                <Form.Item 
                  label="备注" 
                  name="notes"
                  style={{ flex: 1, marginBottom: 0 }}
                >
                  <Input
                    placeholder="输入备注关键词"
                    allowClear
                    onPressEnter={() => {
                      const values = form.getFieldsValue();
                      const params = {
                        title: values.title?.trim() || undefined,
                        subject_id: selectedSubjectId || undefined,
                        notes: values.notes?.trim() || undefined
                      };
                      fetchQuestions(params);
                    }}
                  />
                </Form.Item>
              </div>
            </Form>
            <div style={{ marginBottom: '8px', fontSize: '12px', color: '#666' }}>
              提示：请输入条件缩小搜索范围，最多只显示前100个搜索结果！
            </div>
            <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
              <Space>
                <Button 
                  type="primary" 
                  icon={<SearchOutlined />} 
                  onClick={() => {
                    const values = form.getFieldsValue();
                    const params = {
                      title: values.title?.trim() || undefined,
                      subject_id: selectedSubjectId || undefined,
                      notes: values.notes?.trim() || undefined
                    };
                    fetchQuestions(params);
                  }}
                >
                  搜索
                </Button>
                <Button 
                  icon={<ReloadOutlined />} 
                  onClick={handleReset}
                >
                  重置
                </Button>
              </Space>
            </div>
          </div>

          {/* 问题列表区域 */}
          <div style={{ marginBottom: '16px' }}>
            {questions.length > 0 && (
              <div style={{ marginBottom: '12px', display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }}>
                <Checkbox
                  indeterminate={isIndeterminate}
                  checked={isAllSelected}
                  onChange={(e) => handleSelectAll(e.target.checked)}
                >
                  全选 ({questions.length})
                </Checkbox>
              </div>
            )}
            
            <Spin spinning={loading}>
              {questions.length === 0 ? (
                <Empty
                  description="暂无搜索结果"
                  style={{ margin: '40px 0' }}
                />
              ) : (
                <List
                  dataSource={questions}
                  renderItem={(question) => {
                    const isSelected = selectedQuestions.some(q => q.qid === question.id);
                    
                    return (
                      <List.Item
                        key={question.id}
                        style={{
                          padding: '12px',
                          border: '1px solid #f0f0f0',
                          borderRadius: '6px',
                          marginBottom: '8px',
                          backgroundColor: isSelected ? '#e6f4ff' : '#fafafa',
                          cursor: 'pointer'
                        }}
                        onClick={() => {
                          handleQuestionCheck(question.id, !isSelected);
                        }}
                      >
                        <List.Item.Meta
                          avatar={
                            <Checkbox
                              checked={isSelected}
                              onChange={(e) => {
                                e.stopPropagation();
                                handleQuestionCheck(question.id, e.target.checked);
                              }}
                            />
                          }
                          title={
                            <Text>
                              {question.title}
                              {isSelected && <Text type="secondary" style={{ marginLeft: '8px', fontSize: '12px' }}>(已选)</Text>}
                            </Text>
                          }
                          description={
                            <div>
                              {question.notes ? (
                                <Text style={{ fontSize: '12px' }}>
                                  备注：{question.notes}
                                </Text>
                              ) : (
                                <Text type="secondary" style={{ fontSize: '12px' }}>
                                  备注：无
                                </Text>
                              )}
                            </div>
                          }
                        />
                      </List.Item>
                    );
                  }}
                />
              )}
            </Spin>
          </div>
        </ProCard>
      </ProCard>
    </Drawer>
  );
};

export default QuestionSelectorDrawer; 