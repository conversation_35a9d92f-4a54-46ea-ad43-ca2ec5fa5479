import React from 'react';

// 图标相关类型定义
export interface IconConfig {
  icon: React.ReactNode;
  tooltip: string;
  disabled?: boolean;
  onClick?: () => void;
}

// 功能栏配置类型
export interface FunctionBarConfig {
  mention: IconConfig;
  voice: IconConfig;
  send: IconConfig;
}

// 场景状态枚举
export enum SceneStatus {
  ACTIVE = 1,
  COMPLETED = 2,
  PENDING = 0,
}

// 功能按钮状态
export interface FunctionButtonState {
  disabled: boolean;
  loading?: boolean;
  active?: boolean;
}

export default {};