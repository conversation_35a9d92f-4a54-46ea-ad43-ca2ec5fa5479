import { get, post } from './api';

// 创建练习记录请求接口
export interface ExerciseLogCreateRequest {
  cid: number; // 班级ID
  eid: number; // 练习ID
}

// 创建练习记录响应接口
export interface ExerciseLogCreateResponse {
  id: number; // 练习情况ID
  btime: string; // 开始练习时间
}

// 班级练习信息接口
export interface ClassExerciseInfo {
  e_id: number;
  e_title: string;
  e_type: number;
  e_pic?: string | null;
  e_intro?: string | null;
  e_duration?: number | null;
  t_id?: number | null;
  t_name?: string | null;
  t_avatar?: string | null;
  t_intro?: string | null;
  depend: number; // 练习依赖（0：不依赖；1：依赖）
  w_id?: number | null; // 作业单ID（如果存在）
  s_id?: number | null; // 场景ID（如果存在）
  el_id?: number | null; // 练习情况ID
  el_status: number; // 练习状态（0：未开始；1：练习中；2：已提交）
  el_btime?: string | null;
  el_stime?: string | null;
  el_utime?: string | null;
}

// 班级练习列表响应接口
export interface ClassExerciseListResponse {
  class_name: string;
  exercises: ClassExerciseInfo[];
}

/**
 * 获取指定班级的所有练习信息
 * @param classId 班级ID
 * @returns Promise<ClassExerciseInfo[]> 练习列表
 */
export const getClassExercises = async (classId: number): Promise<ClassExerciseListResponse> => {
  try {
    const response = await get<ClassExerciseListResponse>(`/class/${classId}/exercises`);
    return response;
  } catch (error) {
    console.error('获取班级练习列表失败:', error);
    throw error;
  }
};

/**
 * 创建练习记录
 * @param request 创建练习记录请求
 * @returns Promise<ExerciseLogCreateResponse> 创建结果
 */
export const createExerciseLog = async (request: ExerciseLogCreateRequest): Promise<ExerciseLogCreateResponse> => {
  try {
    const response = await post<ExerciseLogCreateResponse>('/exercise/log', request);
    return response;
  } catch (error) {
    console.error('创建练习记录失败:', error);
    throw error;
  }
};