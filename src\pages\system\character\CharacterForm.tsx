import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Select, Row, Col } from 'antd';
import { createCharacter, updateCharacter, type CharacterCreate, type CharacterUpdate, type CharacterResponse } from '../../../services/system/character';
import { showError, showSuccess } from '../../../utils/errorHandler';

const { TextArea } = Input;

interface CharacterFormProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  character: CharacterResponse | null;
  tenantId: number;
}

const CharacterForm: React.FC<CharacterFormProps> = ({
  visible,
  onCancel,
  onSuccess,
  character,
  tenantId
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const isEditing = !!character;

  useEffect(() => {
    if (visible) {
      if (character) {
        // 编辑模式 - 加载现有数据
        form.setFieldsValue({
          name: character.name,
          gender: character.gender,
          profile: character.profile,
          timbre_type: character.timbre_type,
          timbre: character.timbre || '',
          notes: character.notes || ''
        });
      } else {
        // 新建模式 - 设置默认值
        form.resetFields();
        form.setFieldsValue({
          gender: 0,
          timbre_type: 0,
        });
      }
    }
  }, [visible, character, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      if (isEditing && character) {
        // 编辑模式 - 更新字段
        const updateData: CharacterUpdate = {
          name: values.name,
          gender: values.gender,
          profile: values.profile,
          timbre_type: values.timbre_type,
          timbre: values.timbre || undefined,
          notes: values.notes || undefined
        };
        await updateCharacter(character.id, updateData, tenantId);
        showSuccess('更新人物角色成功');
      } else {
        // 新建模式 - 创建时使用默认状态
        const createData: CharacterCreate = {
          tenant_id: tenantId,
          name: values.name,
          gender: values.gender,
          profile: values.profile,
          timbre_type: values.timbre_type,
          timbre: values.timbre || undefined,
          notes: values.notes || undefined,
          // 新建时默认为未发布、启用状态
          published: 0,
          active: 1
        };
        await createCharacter(createData);
        showSuccess('创建人物角色成功');
      }

      form.resetFields();
      onSuccess();
    } catch (error: unknown) {
      showError(error, '操作失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title={isEditing ? '编辑人物角色' : '新建人物角色'}
      open={visible}
      onCancel={handleCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
      maskClosable={true}
      destroyOnHidden
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          gender: 0,
          timbre_type: 0,
        }}
      >
        {/* 姓名和性别在同一行 */}
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="name"
              label="姓名"
              rules={[{ required: true, message: '请输入姓名' }]}
            >
              <Input placeholder="请输入人物角色姓名" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="gender"
              label="性别"
              rules={[{ required: true, message: '请选择性别' }]}
            >
              <Select>
                <Select.Option value={0}>未知</Select.Option>
                <Select.Option value={1}>男</Select.Option>
                <Select.Option value={2}>女</Select.Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="profile"
          label="人物资料"
          rules={[{ required: true, message: '请输入人物资料' }]}
        >
          <TextArea
            rows={4}
            placeholder="请输入人物角色的详细资料描述"
          />
        </Form.Item>

        {/* 音色类型和音色在同一行 */}
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="timbre_type"
              label="音色类型"
            >
              <Select>
                <Select.Option value={0}>未设置</Select.Option>
                <Select.Option value={1}>火山引擎</Select.Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="timbre"
              label="音色设置"
            >
              <Input placeholder="请输入音色配置（可选）" />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="notes"
          label="备注"
        >
          <TextArea
            rows={3}
            placeholder="请输入备注信息（可选）"
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default CharacterForm; 