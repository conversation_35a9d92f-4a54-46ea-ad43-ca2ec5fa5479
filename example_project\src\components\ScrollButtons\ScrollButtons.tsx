import React, { useState, useEffect } from 'react';
import { VerticalAlignTopOutlined, VerticalAlignBottomOutlined } from '@ant-design/icons';
import { Tooltip } from 'antd';
import './ScrollButtons.css';

interface ScrollButtonsProps {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  scrollContainerRef: React.RefObject<HTMLElement | any>;
  containerRef?: React.RefObject<HTMLElement | null>; // 可选的容器引用，用于限制显示区域
}

const ScrollButtons: React.FC<ScrollButtonsProps> = ({ scrollContainerRef, containerRef }) => {
  const [visible, setVisible] = useState(false);
  const [isInContainer, setIsInContainer] = useState(false);

  // 监听鼠标进入和离开容器
  useEffect(() => {
    const container = containerRef?.current;
    if (!container) return;

    const handleMouseEnter = () => {
      setIsInContainer(true);
    };

    const handleMouseLeave = () => {
      setIsInContainer(false);
      setVisible(false); // 离开容器时立即隐藏
    };

    container.addEventListener('mouseenter', handleMouseEnter);
    container.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      container.removeEventListener('mouseenter', handleMouseEnter);
      container.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [containerRef]);

  // 监听鼠标位置，只在容器内时检查边缘位置
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isInContainer) {
        setVisible(false);
        return;
      }

      const container = containerRef?.current;
      if (!container) return;

      // 获取容器的边界信息
      const containerRect = container.getBoundingClientRect();
      const containerWidth = containerRect.width;
      const threshold = containerRect.right - containerWidth * 0.05; // 容器右侧5%区域

      // 只有在容器内且靠近右边缘时才显示
      if (e.clientX > threshold && e.clientX <= containerRect.right) {
        setVisible(true);
      } else {
        setVisible(false);
      }
    };

    if (isInContainer) {
      window.addEventListener('mousemove', handleMouseMove);
    }

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
    };
  }, [isInContainer, containerRef]);

  // 滚动到顶部
  const scrollToTop = () => {
    if (!scrollContainerRef.current) return;

    try {
      // 获取正确的滚动元素
      // react-scrollbars-custom 的滚动元素是 scrollerElement
      const scroller = scrollContainerRef.current.scrollerElement;

      if (!scroller) return;

      // 使用平滑滚动动画
      const duration = 500; // 滚动时间（毫秒）
      const start = scroller.scrollTop;
      const startTime = performance.now();

      const animateScroll = (currentTime: number) => {
        const elapsedTime = currentTime - startTime;
        const progress = Math.min(elapsedTime / duration, 1);
        const easeInOutQuad = progress < 0.5
          ? 2 * progress * progress
          : 1 - Math.pow(-2 * progress + 2, 2) / 2; // 缓动函数

        scroller.scrollTop = start * (1 - easeInOutQuad);

        if (progress < 1) {
          requestAnimationFrame(animateScroll);
        }
      };

      requestAnimationFrame(animateScroll);
    } catch (error) {
      console.error('Failed to scroll to top:', error);
    }
  };

  // 滚动到底部
  const scrollToBottom = () => {
    if (!scrollContainerRef.current) return;

    try {
      // 获取正确的滚动元素
      // react-scrollbars-custom 的滚动元素是 scrollerElement
      const scroller = scrollContainerRef.current.scrollerElement;

      if (!scroller) return;

      // 计算最大滚动高度
      const scrollHeight = scroller.scrollHeight;
      const clientHeight = scroller.clientHeight;
      const maxScrollTop = scrollHeight - clientHeight;

      // 使用平滑滚动动画
      const duration = 500; // 滚动时间（毫秒）
      const start = scroller.scrollTop;
      const change = maxScrollTop - start;
      const startTime = performance.now();

      const animateScroll = (currentTime: number) => {
        const elapsedTime = currentTime - startTime;
        const progress = Math.min(elapsedTime / duration, 1);
        const easeInOutQuad = progress < 0.5
          ? 2 * progress * progress
          : 1 - Math.pow(-2 * progress + 2, 2) / 2; // 缓动函数

        scroller.scrollTop = start + change * easeInOutQuad;

        if (progress < 1) {
          requestAnimationFrame(animateScroll);
        }
      };

      requestAnimationFrame(animateScroll);
    } catch (error) {
      console.error('Failed to scroll to bottom:', error);
    }
  };

  return (
    <div
      style={{
        display: visible ? 'flex' : 'none',
        flexDirection: 'column',
        position: 'absolute',
        right: '10px',
        top: '50%',
        transform: 'translateY(-50%)',
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        borderRadius: '4px',
        padding: '3px 2px',
        boxShadow: '0 2px 6px rgba(0, 0, 0, 0.15)',
        zIndex: 1000,
      }}
    >
      <Tooltip title="到顶部" placement="left">
        <div
          onClick={scrollToTop}
          className="scroll-button"
        >
          <VerticalAlignTopOutlined style={{ fontSize: '14px', color: '#595959' }} />
        </div>
      </Tooltip>
      <Tooltip title="到底部" placement="left">
        <div
          onClick={scrollToBottom}
          className="scroll-button"
        >
          <VerticalAlignBottomOutlined style={{ fontSize: '14px', color: '#595959' }} />
        </div>
      </Tooltip>
    </div>
  );
};

export default ScrollButtons;