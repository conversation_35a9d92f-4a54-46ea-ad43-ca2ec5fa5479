import React, { useState, useEffect } from 'react';
import { Drawer, Form, Input, Button, message } from 'antd';
import { 
  getWorksheetUnit,
  createWorksheetUnit,
  updateWorksheetUnit,
  type WorksheetUnitResponse,
  type UnitCreate,
  type UnitUpdate
} from '../../../services/system/worksheet';
import { getGlobalTenantInfo } from '../../../components/GlobalTenantSelector';
import { showError } from '../../../utils/errorHandler';
import RichTextEditor from '../../../components/RichTextEditor';

interface UnitFormDrawerProps {
  visible: boolean;
  mode: 'create' | 'edit';
  worksheetId?: string;
  unit?: WorksheetUnitResponse;
  onClose: () => void;
  onSuccess: () => void;
}

const UnitFormDrawer: React.FC<UnitFormDrawerProps> = ({
  visible,
  mode,
  worksheetId,
  unit,
  onClose,
  onSuccess
}) => {
  const [form] = Form.useForm();
  const [bgtext, setBgtext] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [loadingDetail, setLoadingDetail] = useState(false);

  // 获取当前租户ID
  const getCurrentTenantId = (): number | null => {
    const tenantInfo = getGlobalTenantInfo();
    return tenantInfo?.id || null;
  };

  // 获取单元详情（编辑模式）
  const fetchUnitDetail = async (unitId: number) => {
    if (!worksheetId) return;
    
    setLoadingDetail(true);
    try {
      const unitDetail = await getWorksheetUnit(parseInt(worksheetId), unitId);
      form.setFieldsValue({
        name: unitDetail.name
      });
      setBgtext(unitDetail.bgtext || '');
    } catch (error) {
      showError(error, '获取单元详情失败');
    } finally {
      setLoadingDetail(false);
    }
  };

  // 创建单元
  const handleCreateUnit = async () => {
    try {
      const values = await form.validateFields();
      const tenantId = getCurrentTenantId();
      if (!tenantId || !worksheetId) {
        message.error('缺少必要参数');
        return;
      }

      setLoading(true);
      
      const createData: UnitCreate = {
        tenant_id: tenantId,
        wid: parseInt(worksheetId),
        name: values.name,
        bgtext: bgtext || undefined
      };

      await createWorksheetUnit(createData);
      message.success('单元创建成功');
      handleClose();
      onSuccess();
    } catch (error) {
      if (error && typeof error === 'object' && 'errorFields' in error) {
        // 表单验证错误，不需要显示错误消息
        return;
      }
      showError(error, '创建单元失败');
    } finally {
      setLoading(false);
    }
  };

  // 更新单元
  const handleUpdateUnit = async () => {
    try {
      if (!unit || !worksheetId) return;
      
      const values = await form.validateFields();
      setLoading(true);
      
      const updateData: UnitUpdate = {
        name: values.name,
        bgtext: bgtext || undefined
      };

      await updateWorksheetUnit(parseInt(worksheetId), unit.id, updateData);
      message.success('单元更新成功');
      handleClose();
      onSuccess();
    } catch (error) {
      if (error && typeof error === 'object' && 'errorFields' in error) {
        return;
      }
      showError(error, '更新单元失败');
    } finally {
      setLoading(false);
    }
  };

  // 关闭抽屉
  const handleClose = () => {
    form.resetFields();
    setBgtext('');
    onClose();
  };

  // 提交表单
  const handleSubmit = () => {
    if (mode === 'create') {
      handleCreateUnit();
    } else {
      handleUpdateUnit();
    }
  };

  // 当抽屉打开时，根据模式初始化数据
  useEffect(() => {
    if (visible) {
      if (mode === 'create') {
        form.resetFields();
        setBgtext('');
      } else if (mode === 'edit' && unit) {
        fetchUnitDetail(unit.id);
      }
    }
  }, [visible, mode, unit]);

  return (
    <Drawer
      title={mode === 'edit' ? "编辑单元" : "添加单元"}
      width="70%"
      open={visible}
      onClose={handleClose}
      extra={
        <div style={{ display: 'flex', gap: '8px' }}>
          <Button onClick={handleClose}>
            取消
          </Button>
          <Button 
            type="primary" 
            onClick={handleSubmit}
            loading={loading}
          >
            {mode === 'edit' ? "更新" : "创建"}
          </Button>
        </div>
      }
    >
      {loadingDetail ? (
        <div style={{ textAlign: 'center', padding: '40px' }}>
          加载中...
        </div>
      ) : (
        <Form
          form={form}
          layout="vertical"
          requiredMark={false}
        >
          <Form.Item
            label="名称"
            name="name"
            rules={[
              { required: true, message: '请输入单元名称' },
              { max: 100, message: '名称长度不能超过100个字符' }
            ]}
          >
            <Input placeholder="请输入单元名称" />
          </Form.Item>

          <Form.Item
            label="背景"
            style={{ marginBottom: 0 }}
          >
            <div style={{ border: '1px solid #d9d9d9', borderRadius: '6px', minHeight: '300px' }}>
              <RichTextEditor
                value={bgtext}
                onChange={setBgtext}
                placeholder="请输入单元背景信息..."
                title="背景内容"
                showSaveButton={false}
                showFullscreenButton={true}
                toolbarMode="standard"
                style={{ border: 'none', minHeight: '300px' }}
              />
            </div>
          </Form.Item>
        </Form>
      )}
    </Drawer>
  );
};

export default UnitFormDrawer; 