import { message } from 'antd';

// 错误类型定义
interface ApiError {
  response?: {
    data?: {
      detail?: string;
      details?: string;  // 添加details字段支持
      message?: string;
      error?: string;
      code?: number;     // 添加code字段支持
    };
    status?: number;
    statusText?: string;
  };
  message?: string;
  code?: string;
}

// 通用错误处理函数
export const handleApiError = (error: unknown, defaultMessage = '操作失败'): string => {
  // 如果是axios错误对象，优先检查响应数据
  const apiError = error as ApiError;
  
  // 优先检查API响应中的错误信息
  if (apiError.response?.data) {
    const { detail, details, message: responseMessage, error: errorMessage } = apiError.response.data;
    
    // 优先使用message，然后是details，再是detail，最后是error
    if (responseMessage) return responseMessage;
    if (details) return details;
    if (detail) return detail;
    if (errorMessage) return errorMessage;
  }
  
  // 网络错误
  if (apiError.code === 'NETWORK_ERROR' || apiError.message?.includes('Network Error')) {
    return '网络连接失败，请检查网络连接后重试';
  }
  
  // 超时错误（需要在通用Error检查之前）
  if (apiError.code === 'TIMEOUT' || apiError.code === 'ECONNABORTED' || apiError.message?.includes('timeout')) {
    return '网络请求超时，请检查网络状况后重试';
  }

  // 请求被取消
  if (apiError.message?.includes('aborted') && !apiError.message?.includes('timeout')) {
    return '请求已取消';
  }

  // CORS 错误
  if (apiError.message?.includes('CORS') || apiError.message?.includes('Cross-Origin')) {
    return '跨域请求失败，请联系管理员';
  }

  // 无网络连接
  if (apiError.message?.includes('ERR_INTERNET_DISCONNECTED') || apiError.message?.includes('No Internet')) {
    return '网络连接已断开，请检查网络设置';
  }

  // DNS 解析错误
  if (apiError.code === 'ENOTFOUND' || apiError.message?.includes('getaddrinfo')) {
    return '无法连接到服务器，请检查网络设置';
  }

  // 连接被拒绝
  if (apiError.code === 'ECONNREFUSED' || apiError.message?.includes('Connection refused')) {
    return '服务器连接被拒绝，请稍后重试';
  }

  // 如果没有从响应数据中获取到错误信息，则根据HTTP状态码提供友好提示
  if (apiError.response?.status) {
    switch (apiError.response.status) {
      case 400:
        return '请求参数错误，请检查输入内容';
      case 401:
        return '登录已过期，请重新登录';
      case 403:
        return '您没有权限执行此操作';
      case 404:
        return '请求的资源不存在';
      case 409:
        return '数据冲突，该内容可能已存在';
      case 422:
        return '输入数据格式错误，请检查后重试';
      case 429:
        return '操作过于频繁，请稍后再试';
      case 500:
        return '服务器内部错误，请稍后重试';
      case 502:
        return '服务器连接异常，请稍后重试';
      case 503:
        return '服务暂时维护中，请稍后再试';
      case 504:
        return '请求超时，请稍后重试';
      default:
        return `操作失败，请稍后重试 (错误代码: ${apiError.response.status})`;
    }
  }
  
  // 如果是标准的Error对象但没有响应数据（放在最后处理）
  if (error instanceof Error) {
    return error.message || defaultMessage;
  }
  
  return defaultMessage;
};

// 显示错误消息的便捷函数
export const showError = (error: unknown, defaultMessage = '操作失败'): void => {
  const errorMessage = handleApiError(error, defaultMessage);
  message.error(errorMessage);
};

// 显示成功消息的便捷函数
export const showSuccess = (msg: string): void => {
  message.success(msg);
};

// 显示警告消息的便捷函数
export const showWarning = (msg: string): void => {
  message.warning(msg);
};

// 显示信息消息的便捷函数
export const showInfo = (msg: string): void => {
  message.info(msg);
};