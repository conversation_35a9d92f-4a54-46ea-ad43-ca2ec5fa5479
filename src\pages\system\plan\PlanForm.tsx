import React, { useState, useEffect } from 'react';
import { Modal, Form, Input } from 'antd';
import { 
  PlanResponse, 
  PlanCreate, 
  PlanUpdate, 
  createPlan, 
  updatePlan 
} from '../../../services/system/plan';
import { showError, showSuccess } from '../../../utils/errorHandler';

const { TextArea } = Input;

interface PlanFormProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  plan: PlanResponse | null;
  tenantId: number;
}

const PlanForm: React.FC<PlanFormProps> = ({ visible, onCancel, onSuccess, plan, tenantId }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (visible) {
      if (plan) {
        // 编辑模式，填充表单数据
        form.setFieldsValue({
          name: plan.name,
          description: plan.description || '',
          notes: plan.notes || '',
        });
      } else {
        // 新建模式，重置表单
        form.resetFields();
      }
    }
  }, [visible, plan, form]);

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      if (plan) {
        // 编辑模式
        const updateData: PlanUpdate = values;
        await updatePlan(plan.id, updateData);
        showSuccess('更新练习计划成功');
      } else {
        // 新建模式
        const createData: PlanCreate = {
          tenant_id: tenantId,
          active: 1, // 默认启用
          ...values,
        };
        await createPlan(createData);
        showSuccess('创建练习计划成功');
      }

      onSuccess();
    } catch (error) {
      if (error instanceof Error && 'errorFields' in error) {
        // 表单验证错误，不显示错误消息
        return;
      }
      showError(error, plan ? '更新练习计划失败' : '创建练习计划失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理取消
  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title={plan ? '编辑练习计划' : '新建练习计划'}
      open={visible}
      onOk={handleSubmit}
      onCancel={handleCancel}
      confirmLoading={loading}
      width={600}
      destroyOnHidden
    >
      <Form
        form={form}
        layout="vertical"
        preserve={false}
      >
        <Form.Item
          name="name"
          label="名称"
          rules={[
            { required: true, message: '请输入名称' },
            { max: 100, message: '名称不能超过100个字符' }
          ]}
        >
          <Input placeholder="请输入名称" />
        </Form.Item>

        <Form.Item
          name="description"
          label="描述"
          rules={[
            { max: 500, message: '描述不能超过500个字符' }
          ]}
        >
          <TextArea 
            placeholder="请输入描述" 
            rows={4}
            showCount
            maxLength={500}
          />
        </Form.Item>

        <Form.Item
          name="notes"
          label="备注"
          rules={[
            { max: 500, message: '备注不能超过500个字符' }
          ]}
        >
          <TextArea 
            placeholder="请输入备注信息" 
            rows={3}
            showCount
            maxLength={500}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default PlanForm; 