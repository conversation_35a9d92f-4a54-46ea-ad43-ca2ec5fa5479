import React, { useState, useEffect } from 'react';
import { Drawer, Form, Input, Button, message, Select } from 'antd';
import { 
  createSceneLine,
  updateSceneLine,
  getSceneCharacters,
  type SceneLineResponse,
  type SceneLineCreate,
  type SceneLineUpdate,
  type SceneCharacterResponse
} from '../../../services/system/scene';
import { getGlobalTenantInfo } from '../../../components/GlobalTenantSelector';
import { showError } from '../../../utils/errorHandler';

const { TextArea } = Input;
const { Option } = Select;

interface LineFormDrawerProps {
  visible: boolean;
  mode: 'create' | 'edit';
  sceneId?: string;
  cueId?: number;
  line?: SceneLineResponse;
  onClose: () => void;
  onSuccess: () => void;
}

const LineFormDrawer: React.FC<LineFormDrawerProps> = ({
  visible,
  mode,
  sceneId,
  cueId,
  line,
  onClose,
  onSuccess
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [characters, setCharacters] = useState<SceneCharacterResponse[]>([]);
  const [charactersLoading, setCharactersLoading] = useState(false);

  // 获取当前租户ID
  const getCurrentTenantId = (): number | null => {
    const tenantInfo = getGlobalTenantInfo();
    return tenantInfo?.id || null;
  };

  // 获取场景角色列表
  const fetchCharacters = async () => {
    if (!sceneId) return;
    
    const tenantId = getCurrentTenantId();
    if (!tenantId) {
      message.error('请先选择租户');
      return;
    }

    setCharactersLoading(true);
    try {
      const response = await getSceneCharacters(parseInt(sceneId), tenantId);
      setCharacters(response);
    } catch (error) {
      console.warn('获取场景角色失败:', error);
      setCharacters([]);
    } finally {
      setCharactersLoading(false);
    }
  };

  // 创建台词
  const handleCreateLine = async () => {
    try {
      const values = await form.validateFields();
      const tenantId = getCurrentTenantId();
      if (!tenantId || !cueId) {
        message.error('缺少必要参数');
        return;
      }

      // 验证必选字段
      if (!values.character_name) {
        message.error('请选择人物');
        return;
      }

      // 根据角色名找到对应的cid
      const selectedCharacter = characters.find(char => char.name === values.character_name);
      if (!selectedCharacter) {
        message.error('找不到对应的角色信息');
        return;
      }

      setLoading(true);
      
      const createData: SceneLineCreate = {
        tenant_id: tenantId,
        cueid: cueId,
        cid: selectedCharacter.cid,
        pv_topic: values.pv_topic,
        pv_ability: values.pv_ability || '',
        pv_restriction: values.pv_restriction || ''
      };

      await createSceneLine(createData);
      message.success('台词创建成功');
      handleClose();
      onSuccess();
    } catch (error) {
      if (error && typeof error === 'object' && 'errorFields' in error) {
        // 表单验证错误，不需要显示错误消息
        return;
      }
      showError(error, '创建台词失败');
    } finally {
      setLoading(false);
    }
  };

  // 更新台词
  const handleUpdateLine = async () => {
    try {
      if (!line) return;
      
      const values = await form.validateFields();
      const tenantId = getCurrentTenantId();
      if (!tenantId) {
        message.error('缺少必要参数');
        return;
      }

      // 验证必选字段
      if (!values.character_name) {
        message.error('请选择人物');
        return;
      }

      // 根据角色名找到对应的cid
      const selectedCharacter = characters.find(char => char.name === values.character_name);
      if (!selectedCharacter) {
        message.error('找不到对应的角色信息');
        return;
      }

      setLoading(true);
      
      const updateData: SceneLineUpdate = {
        cid: selectedCharacter.cid,
        pv_topic: values.pv_topic,
        pv_ability: values.pv_ability || '',
        pv_restriction: values.pv_restriction || ''
      };

      await updateSceneLine(line.id, updateData, tenantId);
      message.success('台词更新成功');
      handleClose();
      onSuccess();
    } catch (error) {
      if (error && typeof error === 'object' && 'errorFields' in error) {
        return;
      }
      showError(error, '更新台词失败');
    } finally {
      setLoading(false);
    }
  };

  // 关闭抽屉
  const handleClose = () => {
    form.resetFields();
    onClose();
  };

  // 提交表单
  const handleSubmit = () => {
    if (mode === 'create') {
      handleCreateLine();
    } else {
      handleUpdateLine();
    }
  };

  // 当抽屉打开时，根据模式初始化数据
  useEffect(() => {
    if (visible) {
      fetchCharacters();
      
      if (mode === 'create') {
        form.resetFields();
      }
    }
  }, [visible, mode]);

  // 当 characters 数据加载完成且处于编辑模式时，设置表单值
  useEffect(() => {
    if (visible && mode === 'edit' && line && characters.length > 0) {
      // 根据 line.cid 找到对应的角色名
      const editCharacter = characters.find(char => char.cid === line.cid);
      const editCharacterName = editCharacter?.name || '';
      
      form.setFieldsValue({
        character_name: editCharacterName,
        pv_topic: line.pv_topic || '',
        pv_ability: line.pv_ability || '',
        pv_restriction: line.pv_restriction || ''
      });
    }
  }, [visible, mode, line, characters, form]);

  return (
    <Drawer
      title={mode === 'edit' ? "编辑台词" : "添加台词"}
      width="60%"
      open={visible}
      onClose={handleClose}
      extra={
        <div style={{ display: 'flex', gap: '8px' }}>
          <Button onClick={handleClose}>
            取消
          </Button>
          <Button 
            type="primary" 
            onClick={handleSubmit}
            loading={loading}
          >
            {mode === 'edit' ? "更新" : "创建"}
          </Button>
        </div>
      }
    >
      <Form
        form={form}
        layout="vertical"
        requiredMark={false}
      >
        <div style={{ marginBottom: '16px' }}>
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: '8px',
            marginBottom: '12px'
          }}>
            
            <Form.Item
              name="character_name"
              style={{ margin: 0, minWidth: '200px' }}
              rules={[
                { required: true, message: '请选择机器人角色' }
              ]}
            >
              <Select
                placeholder="选择机器人角色"
                style={{ minWidth: '200px' }}
                loading={charactersLoading}
              >
                {characters
                  .filter(character => character.played === 0)
                  .map(character => (
                    <Option key={character.name} value={character.name}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                        {character.avatar && (
                          <img 
                            src={character.avatar} 
                            alt={character.name}
                            style={{ 
                              width: '28px', 
                              height: '28px', 
                              borderRadius: '50%',
                              objectFit: 'cover'
                            }}
                          />
                        )}
                        <span style={{ color: '#1890ff', fontWeight: 'bold', fontSize: '15px' }}>
                          {character.name}
                        </span>
                      </div>
                    </Option>
                  ))}
              </Select>
            </Form.Item>
          </div>
        </div>

        <Form.Item
          label="发言主题"
          name="pv_topic"
          rules={[
            { required: true, message: '请输入发言主题' },
            { max: 200, message: '发言主题长度不能超过200个字符' }
          ]}
        >
          <TextArea 
            placeholder="请输入发言主题" 
            rows={3}
            showCount
            maxLength={200}
          />
        </Form.Item>

        <Form.Item
          label="人物能力"
          name="pv_ability"
          rules={[
            { max: 500, message: '人物能力长度不能超过500个字符' }
          ]}
        >
          <TextArea 
            placeholder="请输入人物能力描述（可选）" 
            rows={4}
            showCount
            maxLength={500}
          />
        </Form.Item>

        <Form.Item
          label="人物限制"
          name="pv_restriction"
          rules={[
            { max: 500, message: '人物限制长度不能超过500个字符' }
          ]}
        >
          <TextArea 
            placeholder="请输入人物限制条件（可选）" 
            rows={4}
            showCount
            maxLength={500}
          />
        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default LineFormDrawer; 