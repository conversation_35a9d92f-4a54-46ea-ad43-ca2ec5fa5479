import React, { useState, useEffect, useCallback } from 'react';
import { 
  Button, 
  Space, 
  Modal,
  Table,
  Tooltip,
  Typography,
  theme,
  Card,
  Input
} from 'antd';
import { 
  PlusOutlined,
  DeleteOutlined,
  DragOutlined,
  CloseOutlined,
  ReloadOutlined,
  ExclamationCircleOutlined,
  EditOutlined,
  SaveOutlined
} from '@ant-design/icons';
import type { TableProps } from 'antd/es/table';
import { 
  deleteQuestionGuide,
  batchUpdateQuestionGuideOrder,
  type QuestionGuideResponse
} from '../../../services/system/question';
import { showError, showSuccess } from '../../../utils/errorHandler';
import QuestionGuideForm from './QuestionGuideForm';
import SortableGuideTable from './SortableGuideTable';

const { Text } = Typography;
const { Search } = Input;
const { confirm } = Modal;

// 问题指南表格组件
const QuestionGuidesTable: React.FC<{
  guides: QuestionGuideResponse[];
  tenantId: number;
  questionId: number;
  onRefresh: () => void;
  onGuidesChange: (guides: QuestionGuideResponse[]) => void;
}> = ({ guides, tenantId, questionId, onRefresh, onGuidesChange }) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isSortMode, setIsSortMode] = useState(false);
  const [originalGuides, setOriginalGuides] = useState<QuestionGuideResponse[]>([]);
  const [currentGuide, setCurrentGuide] = useState<QuestionGuideResponse | null>(null);
  const [searchTitle, setSearchTitle] = useState('');
  const [searchTitleValue, setSearchTitleValue] = useState(''); // 用于存储标题输入框的当前值
  const [filteredGuides, setFilteredGuides] = useState<QuestionGuideResponse[]>([]);
  
  // 分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  
  // 排序状态
  const [sortOrders, setSortOrders] = useState<{
    [key: string]: 'asc' | 'desc' | undefined
  }>({
    priority: 'asc'
  });

  // 过滤和排序数据
  const filterAndSortGuides = useCallback(() => {
    let filtered = [...guides];
    
    // 手动实现标题搜索过滤
    if (searchTitle) {
      filtered = filtered.filter(guide => 
        guide.title.toLowerCase().includes(searchTitle.toLowerCase())
      );
    }
    
    // 手动实现排序
    if (sortOrders.priority) {
      filtered.sort((a, b) => {
        if (sortOrders.priority === 'asc') {
          return a.priority - b.priority;
        } else {
          return b.priority - a.priority;
        }
      });
    }
    
    setFilteredGuides(filtered);
    setPagination(prev => ({
      ...prev,
      total: filtered.length
    }));
  }, [guides, searchTitle, sortOrders]);

  useEffect(() => {
    filterAndSortGuides();
  }, [filterAndSortGuides]);

  // 处理标题搜索
  const handleTitleSearch = (value: string) => {
    setSearchTitle(value);
    setSearchTitleValue(value);
    setPagination(prev => ({ ...prev, current: 1 })); // 重置到第一页
  };

  // 处理标题搜索输入框值变化，但不触发搜索
  const handleTitleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTitleValue(e.target.value);
  };

  const handleAdd = () => {
    setCurrentGuide(null);
    setModalVisible(true);
  };

  const handleEdit = (record: QuestionGuideResponse) => {
    setCurrentGuide(record);
    setModalVisible(true);
  };

  const handleDelete = (id: number) => {
    confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: '确定要删除这个问题指南吗？删除后无法恢复。',
      okText: '确认',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await deleteQuestionGuide(id, tenantId);
          showSuccess('删除成功');
          onRefresh();
        } catch (error) {
          showError(error, '删除失败');
        }
      }
    });
  };

  // 进入排序模式
  const enterSortMode = () => {
    if (guides.length === 0) {
      showError(null, '没有可排序的数据');
      return;
    }
    
    // 保存原始数据用于取消时恢复
    setOriginalGuides([...guides]);
    setIsSortMode(true);
  };

  // 退出排序模式
  const exitSortMode = () => {
    setIsSortMode(false);
    setOriginalGuides([]);
  };

  // 保存排序
  const handleSortSave = async () => {
    if (loading) return; // 防止重复提交
    
    try {
      setLoading(true);
      
      // 检查是否有实际变化
      const hasChanges = guides.some((guide, index) => {
        const originalIndex = originalGuides.findIndex(orig => orig.id === guide.id);
        return originalIndex !== index;
      });
      
      if (!hasChanges) {
        showSuccess('排序未发生变化');
        exitSortMode();
        return;
      }
      
      // 准备批量更新数据
      const guidesOrderData = guides.map((guide, index) => ({
        id: guide.id,
        priority: index + 1 // 优先级从小到大，第一个项目priority=1，排在最前面
      }));
      
      // 调用批量更新接口
      await batchUpdateQuestionGuideOrder({
        tenant_id: tenantId,
        guides: guidesOrderData
      });
      
      showSuccess('排序保存成功');
      
      // 重新获取数据
      onRefresh();
      exitSortMode();
    } catch (error) {
      showError(error, '保存排序失败');
    } finally {
      setLoading(false);
    }
  };

  // 取消排序的回调
  const handleSortCancel = () => {
    onGuidesChange([...originalGuides]);
    exitSortMode();
  };

  // 处理重置刷新
  const handleRefresh = async () => {
    // 清空搜索框
    setSearchTitle('');
    setSearchTitleValue('');

    // 重置排序为优先级升序
    setSortOrders({
      priority: 'asc'
    });

    // 重置分页状态
    setPagination(prev => ({
      ...prev,
      current: 1,
      pageSize: 10,
    }));
    
    // 退出排序模式
    if (isSortMode) {
      exitSortMode();
    }
    
    // 刷新数据
    onRefresh();
  };

  // 处理表格排序变化
  const handleTableChange: TableProps<QuestionGuideResponse>['onChange'] = (paginationConfig, _filters, sorter) => {
    if (paginationConfig) {
      setPagination(prev => ({
        ...prev,
        current: paginationConfig.current || 1,
        pageSize: paginationConfig.pageSize || 10,
      }));
    }

    interface SorterInfo {
      field?: string;
      order?: 'ascend' | 'descend';
    }

    const currentSorter = Array.isArray(sorter) ? sorter[0] : sorter;
    
    const newSortOrders: { [key: string]: "asc" | "desc" | undefined } = {
      priority: undefined
    };

    if (currentSorter && (currentSorter as SorterInfo).field) {
      const field = (currentSorter as SorterInfo).field;
      const order = (currentSorter as SorterInfo).order;
      
      if (field === 'priority') {
        if (order === 'ascend') {
          newSortOrders.priority = 'asc';
        } else if (order === 'descend') {
          newSortOrders.priority = 'desc';
        }
      }
    }

    if (Object.values(newSortOrders).every(v => v === undefined)) {
      newSortOrders.priority = 'asc';
    }

    setSortOrders(newSortOrders);
  };

  // 获取当前页数据
  const getCurrentPageData = () => {
    const startIndex = (pagination.current - 1) * pagination.pageSize;
    const endIndex = startIndex + pagination.pageSize;
    return filteredGuides.slice(startIndex, endIndex);
  };

  // 表单提交成功后的回调
  const handleFormSuccess = () => {
    setModalVisible(false);
    onRefresh();
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 100,
    },
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
      width: 300,
    },
    {
      title: '详情',
      dataIndex: 'details',
      key: 'details',
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_: unknown, record: QuestionGuideResponse) => (
        <Space size="middle">
          <Tooltip title={isSortMode ? "排序模式下不可编辑" : "编辑"}>
            <Button
              type="primary"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
              disabled={isSortMode}
            />
          </Tooltip>
          <Tooltip title={isSortMode ? "排序模式下不可删除" : "删除"}>
            <Button
              type="primary"
              style={{ backgroundColor: '#ff4d4f', borderColor: '#ff4d4f' }}
              size="small"
              icon={<DeleteOutlined />}
              onClick={() => handleDelete(record.id)}
              disabled={isSortMode}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <Card
      title={isSortMode ? "问题指南排序" : "问题指南列表"}
      extra={
        <Space>
          {/* 排序模式下只显示保存和取消按钮 */}
          {isSortMode ? (
            <>
              <Tooltip title="保存排序">
                <Button
                  type="primary"
                  icon={<SaveOutlined />}
                  onClick={handleSortSave}
                  loading={loading}
                  disabled={!guides.length}
                />
              </Tooltip>
              <Tooltip title="取消排序">
                <Button
                  type="default"
                  icon={<CloseOutlined />}
                  onClick={handleSortCancel}
                  disabled={loading}
                />
              </Tooltip>
            </>
          ) : (
            <>
              <Search
                placeholder="搜索指南标题"
                allowClear
                onSearch={handleTitleSearch}
                style={{ width: 160 }}
                value={searchTitleValue}
                onChange={handleTitleInputChange}
              />
              <Tooltip title="添加指南">
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAdd}
                />
              </Tooltip>
              <Tooltip title="重置刷新">
                <Button
                  type="primary"
                  icon={<ReloadOutlined />}
                  onClick={handleRefresh}
                />
              </Tooltip>
              <Tooltip title="调整排序">
                <Button 
                  icon={<DragOutlined />} 
                  onClick={enterSortMode}
                  disabled={guides.length === 0}
                />
              </Tooltip>
            </>
          )}
        </Space>
      }
    >
      {isSortMode ? (
        <SortableGuideTable
          guides={guides}
          loading={loading}
          onGuidesChange={onGuidesChange}
          onEdit={handleEdit}
          onDelete={handleDelete}
        />
      ) : (
        <Table
          columns={columns}
          dataSource={getCurrentPageData()}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          locale={{
            emptyText: '暂无指南数据'
          }}
          onChange={handleTableChange}
          sortDirections={['ascend', 'descend', 'ascend']}
          showSorterTooltip={false}
        />
      )}

      <QuestionGuideForm
        visible={modalVisible}
        onCancel={() => setModalVisible(false)}
        onSuccess={handleFormSuccess}
        guide={currentGuide}
        tenantId={tenantId}
        questionId={questionId}
      />
    </Card>
  );
};

interface QuestionGuidesTabProps {
  guides: QuestionGuideResponse[];
  tenantId: number | undefined;
  questionId: string | undefined;
  onRefresh: () => void;
  onGuidesChange: (guides: QuestionGuideResponse[]) => void;
}

const QuestionGuidesTab: React.FC<QuestionGuidesTabProps> = ({ 
  guides, 
  tenantId, 
  questionId, 
  onRefresh, 
  onGuidesChange 
}) => {
  const { token } = theme.useToken();

  return (
    <div style={{ 
      padding: '0px',
      height: '100%',
      overflow: 'auto'
    }}>
      {tenantId && questionId ? (
        <QuestionGuidesTable
          guides={guides}
          tenantId={tenantId}
          questionId={parseInt(questionId)}
          onRefresh={onRefresh}
          onGuidesChange={onGuidesChange}
        />
      ) : (
        <div style={{ 
          textAlign: 'center', 
          color: token.colorTextSecondary,
          padding: '40px 0'
        }}>
          <Text type="secondary">请先选择租户</Text>
        </div>
      )}
    </div>
  );
};

export default QuestionGuidesTab; 