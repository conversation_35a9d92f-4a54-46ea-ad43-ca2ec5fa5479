import React, { useState, useEffect } from 'react';
import { Modal, Form, Input } from 'antd';
import { 
  createSubject, 
  updateSubject,
  type SubjectResponse,
  type SubjectCreate,
  type SubjectUpdate
} from '../../../services/system/subject';
import { showError, showSuccess } from '../../../utils/errorHandler';

interface SubjectFormProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  subject: SubjectResponse | null;
  tenantId: number;
}

const SubjectForm: React.FC<SubjectFormProps> = ({ visible, onCancel, onSuccess, subject, tenantId }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (visible) {
      if (subject) {
        form.setFieldsValue({
          name: subject.name,
        });
      } else {
        form.resetFields();
      }
    }
  }, [visible, subject, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      if (subject) {
        const updateData: SubjectUpdate = {
          name: values.name,
        };
        
        await updateSubject(subject.id, updateData);
        showSuccess('更新主题成功');
      } else {
        const createData: SubjectCreate = {
          tenant_id: tenantId,
          name: values.name,
        };
        await createSubject(createData);
        showSuccess('创建主题成功');
      }

      form.resetFields();
      onSuccess();
    } catch (error: unknown) {
      showError(error, '操作失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title={subject ? '编辑主题' : '添加主题'}
      open={visible}
      onCancel={handleCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
      destroyOnHidden
      width={520}
    >
      <Form
        form={form}
        layout="vertical"
      >
        <Form.Item
          name="name"
          label="主题名称"
          rules={[
            { required: true, message: '请输入主题名称' },
            { max: 100, message: '主题名称不能超过100个字符' }
          ]}
        >
          <Input placeholder="请输入主题名称" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default SubjectForm; 