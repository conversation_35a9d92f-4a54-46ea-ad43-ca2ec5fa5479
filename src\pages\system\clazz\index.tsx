import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Card, Table, Button, Space, Input, Tooltip, Modal, Avatar, Breadcrumb, DatePicker } from 'antd';
import { PlusOutlined, EditOutlined, StopOutlined, ReloadOutlined, ExclamationCircleOutlined, DeleteFilled, RollbackOutlined, UndoOutlined, HomeOutlined, UserOutlined, EyeOutlined } from '@ant-design/icons';
import type { TableProps } from 'antd/es/table';
import type { InputRef } from 'antd';
import dayjs from 'dayjs';
import { 
  getClazzes, 
  updateClazz,
  type ClazzResponse
} from '../../../services/system/clazz';
import { showError, showSuccess } from '../../../utils/errorHandler';
import { getGlobalTenantInfo, type GlobalTenantInfo } from '../../../components/GlobalTenantSelector';
import ClazzForm from './ClazzForm';
import PicUploader from './PicUploader';
import { Link, useNavigate } from 'react-router-dom';

const { Search } = Input;
const { confirm } = Modal;
const { RangePicker } = DatePicker;

const ClazzManagement: React.FC = () => {
  const [clazzes, setClazzes] = useState<ClazzResponse[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [picUploaderVisible, setPicUploaderVisible] = useState(false);
  const [currentClazz, setCurrentClazz] = useState<ClazzResponse | null>(null);
  const [selectedTenantId, setSelectedTenantId] = useState<number | undefined>(undefined);
  const [searchName, setSearchName] = useState('');
  const [searchNameValue, setSearchNameValue] = useState('');
  const [searchNotes, setSearchNotes] = useState('');
  const [searchNotesValue, setSearchNotesValue] = useState('');
  const [timeRange, setTimeRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);
  const [isRecycleBin, setIsRecycleBin] = useState(false);
  const [tableKey, setTableKey] = useState<number>(0);
  const navigate = useNavigate();

  // 分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 创建对搜索框的引用
  const searchNameInputRef = useRef<InputRef>(null);
  const searchNotesInputRef = useRef<InputRef>(null);

  // 从 GlobalTenantSelector 读取全局租户信息
  const loadGlobalTenant = useCallback(() => {
    const tenantInfo: GlobalTenantInfo | null = getGlobalTenantInfo();
    if (tenantInfo) {
      setSelectedTenantId(tenantInfo.id);
      return tenantInfo;
    } else {
      setSelectedTenantId(undefined);
      return null;
    }
  }, []);

  // 监听全局租户变化
  useEffect(() => {
    // 初始加载
    loadGlobalTenant();

    // 监听 localStorage 变化（跨标签页）
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'globalTenant') {
        loadGlobalTenant();
      }
    };

    // 监听自定义事件（同一页面内的更新）
    const handleGlobalTenantChange = (e: Event) => {
      const customEvent = e as CustomEvent;
      const { id } = customEvent.detail;
      if (id) {
        setSelectedTenantId(id);
      } else {
        setSelectedTenantId(undefined);
      }
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('globalTenantChanged', handleGlobalTenantChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('globalTenantChanged', handleGlobalTenantChange);
    };
  }, [loadGlobalTenant]);

  // 获取班级列表
  const fetchClazzes = useCallback(async () => {
    if (!selectedTenantId) {
      setClazzes([]);
      return;
    }

    try {
      setLoading(true);
      
      const response = await getClazzes({
        tenant_id: selectedTenantId,
        skip: (pagination.current - 1) * pagination.pageSize,
        limit: pagination.pageSize,
        active: isRecycleBin ? 0 : 1,
        sort_by: 'id',
        sort_order: 'desc',
        ...(searchName && { name: searchName }),
        ...(searchNotes && { notes: searchNotes }),
        start_time: timeRange?.[0]?.toISOString(),
        end_time: timeRange?.[1]?.toISOString(),
      });
      
      setClazzes(response.items);
      setPagination(prev => ({
        ...prev,
        total: response.total
      }));
    } catch (error) {
      showError(error, '获取班级列表失败');
    } finally {
      setLoading(false);
    }
  }, [selectedTenantId, searchName, searchNotes, timeRange, isRecycleBin, pagination.current, pagination.pageSize]);

  useEffect(() => {
    if (selectedTenantId) {
      fetchClazzes();
    }
  }, [fetchClazzes]);

  // 处理名称搜索
  const handleNameSearch = (value: string) => {
    setSearchName(value);
    setSearchNameValue(value);
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  const handleNameInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchNameValue(e.target.value);
  };

  // 处理备注搜索
  const handleNotesSearch = (value: string) => {
    setSearchNotes(value);
    setSearchNotesValue(value);
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  const handleNotesInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchNotesValue(e.target.value);
  };

  // 处理时间范围变化
  const handleTimeRangeChange = (dates: [dayjs.Dayjs | null, dayjs.Dayjs | null] | null) => {
    if (dates && dates[0] && dates[1]) {
      setTimeRange([dates[0], dates[1]]);
    } else {
      setTimeRange(null);
    }
    // 重置分页到第一页
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  // 切换回收站模式
  const toggleRecycleBin = () => {
    setIsRecycleBin(!isRecycleBin);
    setPagination(prev => ({
      ...prev,
      current: 1,
    }));
  };

  // 处理添加班级
  const handleAdd = () => {
    if (!selectedTenantId) {
      showError(null, '请先在顶部导航栏选择租户');
      return;
    }
    setCurrentClazz(null);
    setModalVisible(true);
  };

  // 处理编辑班级
  const handleEdit = (record: ClazzResponse) => {
    setCurrentClazz(record);
    setModalVisible(true);
  };

  // 处理上传图片
  const handleUploadPic = (record: ClazzResponse) => {
    setCurrentClazz(record);
    setPicUploaderVisible(true);
  };

  // 处理禁用班级
  const handleDisable = (id: number) => {
    confirm({
      title: '确认禁用',
      icon: <ExclamationCircleOutlined />,
      content: '确定要禁用这个班级吗？禁用后可在回收站中恢复。',
      okText: '确认',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await updateClazz(id, { active: 0 }, selectedTenantId!);
          showSuccess('禁用成功');
          fetchClazzes();
        } catch (error: unknown) {
          showError(error, '禁用班级失败');
        }
      },
    });
  };

  // 处理恢复班级
  const handleRestore = async (clazzId: number) => {
    try {
      await updateClazz(clazzId, { active: 1 }, selectedTenantId!);
      showSuccess('恢复成功');
      fetchClazzes();
    } catch (error) {
      showError(error, '恢复班级失败');
    }
  };

  // 表单提交成功后的回调
  const handleFormSuccess = () => {
    setModalVisible(false);
    fetchClazzes();
  };

  // 图片上传成功后的回调
  const handlePicUploadSuccess = () => {
    setPicUploaderVisible(false);
    fetchClazzes();
  };

  // 处理重置刷新
  const handleRefresh = async () => {
    setSearchName('');
    setSearchNameValue('');
    setSearchNotes('');
    setSearchNotesValue('');
    setTimeRange(null);

    setPagination(prev => ({
      ...prev,
      current: 1,
      pageSize: 10,
    }));

    setTableKey(prevKey => prevKey + 1);
    
    if (selectedTenantId) {
      await fetchClazzes();
    }
  };

  // 处理表格变化
  const handleTableChange: TableProps<ClazzResponse>['onChange'] = (paginationConfig) => {
    if (paginationConfig) {
      setPagination(prev => ({
        ...prev,
        current: paginationConfig.current || 1,
        pageSize: paginationConfig.pageSize || 10,
      }));
    }
  };

  // 处理查看详情
  const handleViewDetail = (record: ClazzResponse) => {
    navigate(`/system/clazz/${record.id}`);
  };

  // 表格列定义
  const getColumns = () => {
    const baseColumns = [
      {
        title: 'ID',
        dataIndex: 'id',
        key: 'id',
        width: 60,
      },
      {
        title: '图片',
        dataIndex: 'pic',
        key: 'pic',
        width: 60,
        render: (pic: string | null, record: ClazzResponse) => (
          <div
            style={{ cursor: 'pointer' }}
            onClick={() => handleUploadPic(record)}
          >
            <Avatar
              size={40}
              src={pic}
              icon={<UserOutlined />}
              shape="square"
              style={{ backgroundColor: '#f0f0f0' }}
            />
          </div>
        ),
      },
      {
        title: '名称',
        dataIndex: 'name',
        key: 'name',
        width: '25%',
      },
      {
        title: '描述',
        dataIndex: 'description',
        key: 'description',
        ellipsis: {
          showTitle: false,
        },
        render: (text: string | null) => (
          <Tooltip title={text} placement="topLeft">
            {text || '-'}
          </Tooltip>
        ),
      },
      {
        title: '备注',
        dataIndex: 'notes',
        key: 'notes',
        ellipsis: {
          showTitle: false,
        },
        render: (text: string | null) => (
          <Tooltip title={text} placement="topLeft">
            {text || '-'}
          </Tooltip>
        ),
      },
      {
        title: '开班时间',
        key: 'classTime',
        width: 200,
        render: (_: unknown, record: ClazzResponse) => {
          if (record.btime && record.etime) {
            const startTime = dayjs(record.btime).format('YYYY-MM-DD');
            const endTime = dayjs(record.etime).format('YYYY-MM-DD');
            return `${startTime} 至 ${endTime}`;
          }
          return '-';
        },
      },
      {
        title: '创建时间',
        dataIndex: 'ctime',
        key: 'ctime',
        width: 160,
        render: (text: string) => text ? dayjs(text).format('YYYY-MM-DD HH:mm') : '-',
      },
    ];

    if (isRecycleBin) {
      // 回收站模式下的操作列
      baseColumns.push({
        title: '操作',
        key: 'action',
        width: 160,
        render: (_: unknown, record: ClazzResponse) => (
          <Space size="middle">
            <Tooltip title="详情">
              <Button
                type="primary"
                style={{ backgroundColor: '#1890ff', borderColor: '#1890ff' }}
                icon={<EyeOutlined />}
                size="small"
                onClick={() => handleViewDetail(record)}
              />
            </Tooltip>
            <Tooltip title="编辑">
              <Button
                type="primary"
                icon={<EditOutlined />}
                size="small"
                onClick={() => handleEdit(record)}
              />
            </Tooltip>
            <Tooltip title="恢复">
              <Button
                type="primary"
                style={{ backgroundColor: '#52c41a', borderColor: '#52c41a' }}
                icon={<UndoOutlined />}
                size="small"
                onClick={() => handleRestore(record.id)}
              />
            </Tooltip>
          </Space>
        ),
      } as never);
    } else {
      // 正常模式下的操作列
      baseColumns.push({
        title: '操作',
        key: 'action',
        width: 160,
        render: (_: unknown, record: ClazzResponse) => (
          <Space size="middle">
            <Tooltip title="详情">
              <Button
                type="primary"
                style={{ backgroundColor: '#1890ff', borderColor: '#1890ff' }}
                icon={<EyeOutlined />}
                size="small"
                onClick={() => handleViewDetail(record)}
              />
            </Tooltip>
            <Tooltip title="编辑">
              <Button
                type="primary"
                icon={<EditOutlined />}
                size="small"
                onClick={() => handleEdit(record)}
              />
            </Tooltip>
            <Tooltip title="禁用">
              <Button
                type="primary"
                style={{ backgroundColor: '#ff4d4f', borderColor: '#ff4d4f' }}
                icon={<StopOutlined />}
                size="small"
                onClick={() => handleDisable(record.id)}
              />
            </Tooltip>
          </Space>
        ),
      } as never);
    }

    return baseColumns;
  };

  return (
    <div>
      {/* 面包屑导航 */}
      <Breadcrumb 
        style={{ marginBottom: 16 }}
        items={[
          {
            title: (
              <Link to="/system/home">
                <HomeOutlined /> 主页
              </Link>
            ),
          },
          {
            title: '租户空间',
          },
          {
            title: '班级管理',
          },
        ]}
      />

      <Card
        title={isRecycleBin ? "班级回收站" : "班级管理"}
        extra={
          <Space>
            <Search
              placeholder="搜索班级名称"
              allowClear
              onSearch={handleNameSearch}
              style={{ width: 160 }}
              ref={searchNameInputRef}
              value={searchNameValue}
              onChange={handleNameInputChange}
            />
            <Search
              placeholder="搜索备注"
              allowClear
              onSearch={handleNotesSearch}
              style={{ width: 160 }}
              ref={searchNotesInputRef}
              value={searchNotesValue}
              onChange={handleNotesInputChange}
            />
            <RangePicker
              placeholder={['开始时间', '结束时间']}
              style={{ width: 250 }}
              value={timeRange}
              onChange={handleTimeRangeChange}
              allowClear
            />
            {!isRecycleBin && selectedTenantId && (
              <Tooltip title="添加班级">
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAdd}
                />
              </Tooltip>
            )}
            <Tooltip title="重置刷新">
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
              />
            </Tooltip>
            {isRecycleBin ? (
              <Tooltip title="返回">
                <Button
                  type="primary"
                  icon={<RollbackOutlined />}
                  onClick={toggleRecycleBin}
                />
              </Tooltip>
            ) : (
              <Tooltip title="回收站">
                <Button
                  type="default"
                  icon={<DeleteFilled />}
                  onClick={toggleRecycleBin}
                />
              </Tooltip>
            )}
          </Space>
        }
      >
        <Table
          key={tableKey}
          columns={getColumns()}
          dataSource={clazzes}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          locale={{
            emptyText: selectedTenantId ? (isRecycleBin ? '回收站中没有班级' : '暂无班级数据') : '请先在顶部导航栏选择租户'
          }}
          onChange={handleTableChange}
          sortDirections={['ascend', 'descend', 'ascend']}
          showSorterTooltip={false}
        />
      </Card>

      {selectedTenantId && (
        <ClazzForm
          visible={modalVisible}
          onCancel={() => setModalVisible(false)}
          onSuccess={handleFormSuccess}
          clazz={currentClazz}
          tenantId={selectedTenantId}
        />
      )}

      {selectedTenantId && currentClazz && (
        <PicUploader
          visible={picUploaderVisible}
          onCancel={() => setPicUploaderVisible(false)}
          onSuccess={handlePicUploadSuccess}
          clazzId={currentClazz.id}
          tenantId={selectedTenantId}
          currentPic={currentClazz.pic}
        />
      )}
    </div>
  );
};

export default ClazzManagement; 