import React, { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import { PrismLight as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vs } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { Button, Tooltip, App } from 'antd';
import { ExpandAltOutlined, ShrinkOutlined, CopyOutlined, CheckOutlined } from '@ant-design/icons';
import MermaidRenderer from './MermaidRenderer';

// 代码块组件
interface CodeBlockProps {
  className?: string;
  children?: React.ReactNode;
  renderMermaid?: boolean;
}

const CodeBlock: React.FC<CodeBlockProps> = ({ className, children, renderMermaid = true, ...props }) => {
  const { message } = App.useApp();
  
  // React Hook 必须在组件顶部调用，不能在条件语句内部
  const [expanded, setExpanded] = useState(false);
  const [copied, setCopied] = useState(false);
  const codeBlockRef = React.useRef<HTMLDivElement>(null);
  
  const match = /language-(\w+)/.exec(className || '');
  const language = match && match[1];
  const isInline = !match;

  // 处理Mermaid图表
  if (language === 'mermaid') {
    const mermaidCode = String(children).replace(/\n$/, '');
    if (renderMermaid) {
      // 使用key来强制重新渲染组件
      return <MermaidRenderer key={`mermaid-${mermaidCode}`} chart={mermaidCode} />;
    } else {
      // 如果不渲染Mermaid，显示原始代码
      return (
        <div className="mermaid-code-placeholder" style={{
          padding: '16px',
          backgroundColor: '#f6f8fa',
          borderRadius: '6px',
          fontFamily: 'monospace',
          whiteSpace: 'pre-wrap',
          overflowX: 'auto',
          fontSize: '14px',
          lineHeight: '1.5',
          border: '1px dashed #d0d7de'
        }}>
          <div style={{ marginBottom: '8px', color: '#666' }}>
            Mermaid 图表代码
          </div>
          {mermaidCode}
        </div>
      );
    }
  }

  // 处理普通代码块
  if (!isInline && language) {
    const codeContent = String(children).replace(/\n$/, '');
    const lines = codeContent.split('\n');
    const isLongCode = lines.length > 10;

    // 如果代码超过10行且未展开，只显示前10行
    const displayContent = isLongCode && !expanded
      ? lines.slice(0, 10).join('\n')
      : codeContent;

    // 复制代码
    const handleCopy = () => {
      navigator.clipboard.writeText(codeContent)
        .then(() => {
          setCopied(true);
          message.success('代码已复制');
          setTimeout(() => setCopied(false), 2000);
        })
        .catch(() => {
          message.error('复制失败');
        });
    };

    // 切换展开/收起状态
    const toggleExpand = () => {
      const newExpandedState = !expanded;
      setExpanded(newExpandedState);

      // 如果是从展开状态切换到收起状态，滚动到代码块顶部
      if (!newExpandedState && codeBlockRef.current) {
        codeBlockRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    };

    return (
      <div style={{ position: 'relative' }}>
        <div style={{
          position: 'absolute',
          right: '10px',
          top: '10px',
          zIndex: 10,
          display: 'flex',
          flexDirection: 'row',
          gap: '8px'
        }}>
          <Tooltip title="复制代码">
            <Button
              type="text"
              icon={copied ? <CheckOutlined style={{ color: '#52c41a' }} /> : <CopyOutlined />}
              onClick={handleCopy}
              size="small"
              style={{ backgroundColor: 'rgba(255, 255, 255, 0.8)' }}
            />
          </Tooltip>
          {isLongCode && (
            <Tooltip title={expanded ? '收起代码' : '展开代码'}>
              <Button
                type="text"
                icon={expanded ? <ShrinkOutlined /> : <ExpandAltOutlined />}
                onClick={toggleExpand}
                size="small"
                style={{ backgroundColor: 'rgba(255, 255, 255, 0.8)' }}
              />
            </Tooltip>
          )}
        </div>

        <div ref={codeBlockRef} style={{ position: 'relative' }}>
          <SyntaxHighlighter
            style={vs}
            language={language}
            PreTag="div"
            showLineNumbers={true}
            startingLineNumber={1}
            customStyle={{
              fontSize: '14px',
              lineHeight: '1.5',
              maxHeight: expanded ? 'none' : isLongCode ? '250px' : 'none',
              overflow: 'auto',
              marginBottom: 0,
              borderBottomLeftRadius: isLongCode && !expanded ? 0 : '6px',
              borderBottomRightRadius: isLongCode && !expanded ? 0 : '6px'
            }}
            {...props}
          >
            {displayContent}
          </SyntaxHighlighter>

          {isLongCode && !expanded && (
            <div style={{
              textAlign: 'center',
              padding: '8px',
              backgroundColor: 'rgba(0, 0, 0, 0.03)',
              borderTop: '1px dashed #ddd',
              cursor: 'pointer',
              borderBottomLeftRadius: '4px',
              borderBottomRightRadius: '4px',
              marginTop: 0,
              fontSize: '12px',
              fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
            }} onClick={toggleExpand}>
              点击展开剩余 {lines.length - 10} 行代码
            </div>
          )}

          {isLongCode && expanded && (
            <div style={{
              textAlign: 'center',
              padding: '8px',
              backgroundColor: 'rgba(0, 0, 0, 0.03)',
              borderTop: '1px dashed #ddd',
              cursor: 'pointer',
              borderBottomLeftRadius: '4px',
              borderBottomRightRadius: '4px',
              marginTop: 0,
              fontSize: '12px',
              fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
            }} onClick={toggleExpand}>
              收起代码
            </div>
          )}
        </div>
      </div>
    );
  } else {
    return (
      <code className={className} {...props}>
        {children}
      </code>
    );
  }
};

// 导入常用语言支持
import jsx from 'react-syntax-highlighter/dist/esm/languages/prism/jsx';
import javascript from 'react-syntax-highlighter/dist/esm/languages/prism/javascript';
import typescript from 'react-syntax-highlighter/dist/esm/languages/prism/typescript';
import python from 'react-syntax-highlighter/dist/esm/languages/prism/python';
import html from 'react-syntax-highlighter/dist/esm/languages/prism/markup';

// 注册语言
SyntaxHighlighter.registerLanguage('jsx', jsx);
SyntaxHighlighter.registerLanguage('javascript', javascript);
SyntaxHighlighter.registerLanguage('typescript', typescript);
SyntaxHighlighter.registerLanguage('python', python);
SyntaxHighlighter.registerLanguage('html', html);

interface MarkdownRendererProps {
  content: string;
  className?: string;
  renderMermaid?: boolean; // 控制是否渲染Mermaid图表
}

const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({ content, className, renderMermaid = true }) => {
  App.useApp();

  // 检测内容是否为空
  if (!content) {
    // 当内容为空时，返回一个空的div，保持与有内容时相同的结构和样式
    return (
      <div className={`markdown-content ${className || ''}`} style={{
        minHeight: '20px',
        lineHeight: '1.5',
        padding: '0',
        margin: '0'
      }}>
        <div style={{ minHeight: '20px', lineHeight: '1.5' }}>&nbsp;</div>
      </div>
    );
  }

  // 预处理内容：将普通文本的换行符转换为 Markdown 格式
  // 检测是否包含 Markdown 语法或特殊内容
  const hasMarkdownSyntax = /[#*\->`[\]|]/.test(content) || 
                           content.includes('```') || 
                           content.includes('mermaid') ||
                           content.includes('gantt') ||
                           content.includes('graph') ||
                           content.includes('flowchart') ||
                           content.includes('sequenceDiagram') ||
                           content.includes('classDiagram');
  
  let processedContent = content;
  
  // 只有在确认是纯文本时才进行换行符转换
  if (!hasMarkdownSyntax) {
    // 对于普通文本，将单个换行符转换为 Markdown 的硬换行
    processedContent = content
      .replace(/\n\n+/g, '\n\n') // 将多个连续换行符规范化为两个
      .replace(/(?<!\n)\n(?!\n)/g, '  \n'); // 将单个换行符转换为 Markdown 的硬换行（两个空格+换行符）
  }

  return (
    <div className={`markdown-content markdown-content-last-child-no-margin ${className || ''}`} style={{
      minHeight: '20px',
      lineHeight: '1.5',
      padding: '0',
      margin: '0'
    }}>
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeRaw]}
        components={{
          // 重置所有元素的默认边距
           p: ({ children }) => (
             <p style={{ 
               margin: '0 0 16px 0', 
               lineHeight: '1.5'
             }}>
               {children}
             </p>
           ),
          h1: ({ children }) => (
            <h1 style={{ margin: '0 0 16px 0', lineHeight: '1.2' }}>
              {children}
            </h1>
          ),
          h2: ({ children }) => (
            <h2 style={{ margin: '0 0 16px 0', lineHeight: '1.3' }}>
              {children}
            </h2>
          ),
          h3: ({ children }) => (
            <h3 style={{ margin: '0 0 16px 0', lineHeight: '1.4' }}>
              {children}
            </h3>
          ),
          h4: ({ children }) => (
            <h4 style={{ margin: '0 0 16px 0', lineHeight: '1.4' }}>
              {children}
            </h4>
          ),
          h5: ({ children }) => (
            <h5 style={{ margin: '0 0 16px 0', lineHeight: '1.4' }}>
              {children}
            </h5>
          ),
          h6: ({ children }) => (
            <h6 style={{ margin: '0 0 16px 0', lineHeight: '1.4' }}>
              {children}
            </h6>
          ),
          ul: ({ children }) => (
            <ul style={{ margin: '0 0 16px 0', paddingLeft: '20px' }}>
              {children}
            </ul>
          ),
          ol: ({ children }) => (
            <ol style={{ margin: '0 0 16px 0', paddingLeft: '20px' }}>
              {children}
            </ol>
          ),
          li: ({ children }) => (
            <li style={{ margin: '0', lineHeight: '1.5' }}>
              {children}
            </li>
          ),
          blockquote: ({ children }) => (
            <blockquote style={{ margin: '0 0 16px 0', paddingLeft: '16px', borderLeft: '4px solid #ddd' }}>
              {children}
            </blockquote>
          ),
           // 自定义表格渲染
          table: ({ children }) => (
            <table className="markdown-table" style={{
              borderCollapse: 'collapse',
              width: '100%',
              marginBottom: '16px',
              border: '1px solid #d9d9d9'
            }}>
              {children}
            </table>
          ),
          thead: ({ children }) => (
            <thead style={{ backgroundColor: '#fafafa' }}>
              {children}
            </thead>
          ),
          th: ({ children }) => (
            <th style={{
              padding: '8px 12px',
              textAlign: 'left',
              fontWeight: 600,
              border: '1px solid #d9d9d9',
              backgroundColor: '#fafafa'
            }}>
              {children}
            </th>
          ),
          td: ({ children }) => (
            <td style={{
              padding: '8px 12px',
              border: '1px solid #d9d9d9'
            }}>
              {children}
            </td>
          ),
          // 自定义代码块渲染
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          code: (props: any) => {
            return <CodeBlock {...props} renderMermaid={renderMermaid} />;
          },
        }}
      >
        {processedContent}
      </ReactMarkdown>
      <style>{`
        .markdown-content-last-child-no-margin > *:last-child {
          margin-bottom: 0 !important;
        }
        .markdown-content-last-child-no-margin p:last-child {
          margin-bottom: 0 !important;
        }
        .markdown-content-last-child-no-margin h1:last-child,
        .markdown-content-last-child-no-margin h2:last-child,
        .markdown-content-last-child-no-margin h3:last-child,
        .markdown-content-last-child-no-margin h4:last-child,
        .markdown-content-last-child-no-margin h5:last-child,
        .markdown-content-last-child-no-margin h6:last-child {
          margin-bottom: 0 !important;
        }
        .markdown-content-last-child-no-margin ul:last-child,
        .markdown-content-last-child-no-margin ol:last-child {
          margin-bottom: 0 !important;
        }
        .markdown-content-last-child-no-margin blockquote:last-child {
          margin-bottom: 0 !important;
        }
        .markdown-content-last-child-no-margin table:last-child {
          margin-bottom: 0 !important;
        }
      `}</style>
    </div>
  );
};

export default MarkdownRenderer;