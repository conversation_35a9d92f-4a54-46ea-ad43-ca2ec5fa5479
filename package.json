{"name": "ai-tmai-console-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build-with-type-check": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@ant-design/pro-components": "^2.8.9", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@tanstack/react-query": "^5.74.4", "@types/crypto-js": "^4.2.2", "@xyflow/react": "^12.8.2", "antd": "^5.24.8", "axios": "^1.8.4", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "lodash": "^4.17.21", "quill": "^2.0.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^10.1.0", "react-quill-new": "^3.4.6", "react-router-dom": "^7.5.1", "remark-gfm": "^4.0.1", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/lodash": "^4.17.16", "@types/node": "^22.14.1", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.2.0"}}