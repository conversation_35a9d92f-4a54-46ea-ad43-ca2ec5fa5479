/* Worksheet页面样式 */

/* 主题色彩变量 */
:root {
  --primary-color: #21808d;
  --primary-color-light: rgba(33, 128, 141, 0.15);
  --text-primary: #262626;
  --text-secondary: #8c8c8c;
  --border-color: #f0f0f0;
  --background-light: #fafafa;
  --white: #fff;
  --shadow-light: 2px 2px 8px rgba(0, 0, 0, 0.15);
}

/* 通用按钮样式 */
.worksheet-button {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

.worksheet-button:focus {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* 统一的按钮样式 - 取消、不保存、编辑按钮 */
.custom-button {
  border: 1px solid rgb(33, 128, 141) !important;
  color: rgb(33, 128, 141) !important;
  background: white !important;
  transition: all 0.3s ease !important;
}

.custom-button:hover {
  border: 1px solid rgb(33, 128, 141) !important;
  color: rgb(33, 128, 141) !important;
  background: rgba(33, 128, 141, 0.04) !important;
  box-shadow: none !important;
}

.custom-button:focus {
  border: 1px solid rgb(33, 128, 141) !important;
  outline: none !important;
  box-shadow: none !important;
  color: rgb(33, 128, 141) !important;
  background: white !important;
}

.custom-button:active {
  border: 1px solid rgb(33, 128, 141) !important;
  outline: none !important;
  box-shadow: none !important;
  color: rgb(33, 128, 141) !important;
  background: white !important;
}

/* 保存按钮样式 */
.save-button {
  background-color: rgb(33, 128, 141) !important;
  border-color: rgb(33, 128, 141) !important;
  color: white !important;
  transition: all 0.3s ease !important;
}

.save-button:hover {
  background-color: rgba(33, 128, 141, 0.8) !important;
  border-color: rgba(33, 128, 141, 0.8) !important;
  color: white !important;
  box-shadow: none !important;
}

.save-button:focus {
  background-color: rgb(33, 128, 141) !important;
  border-color: rgb(33, 128, 141) !important;
  color: white !important;
  outline: none !important;
  box-shadow: none !important;
}

.save-button:active {
  background-color: rgb(33, 128, 141) !important;
  border-color: rgb(33, 128, 141) !important;
  color: white !important;
  outline: none !important;
  box-shadow: none !important;
}

/* 保存草稿按钮样式 */
.save-draft-button {
  border: 1px solid rgb(33, 128, 141) !important;
  color: rgb(33, 128, 141) !important;
  background: white !important;
  transition: all 0.3s ease !important;
  display: flex !important;
  align-items: center !important;
  gap: 6px !important;
  font-size: 16px !important;
  font-weight: 500 !important;
  border-radius: 6px !important;
  padding: 4px 8px !important;
}

.save-draft-button:hover {
  border: 1px solid rgb(33, 128, 141) !important;
  color: rgb(33, 128, 141) !important;
  background: rgba(33, 128, 141, 0.04) !important;
  box-shadow: none !important;
}

.save-draft-button:focus {
  border: 1px solid rgb(33, 128, 141) !important;
  outline: none !important;
  box-shadow: none !important;
  color: rgb(33, 128, 141) !important;
  background: white !important;
}

.save-draft-button:active {
  border: 1px solid rgb(33, 128, 141) !important;
  outline: none !important;
  box-shadow: none !important;
  color: rgb(33, 128, 141) !important;
  background: white !important;
}

/* 主标题样式 */
.worksheet-title {
  margin: 0;
  color: var(--text-primary);
  font-size: 28px !important;
  font-weight: 600;
}

/* 状态标签样式 */
.status-tag {
  margin: 0;
  font-size: 12px;
  font-weight: 500;
}

.status-tag-pending {
  color: var(--text-secondary);
}

.status-tag-in-progress {
  color: var(--primary-color);
}

.status-tag-submitted {
  color: #13343b;
}

/* 老师信息样式 */
.teacher-avatar {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  margin-right: 6px;
  object-fit: cover;
}

.teacher-icon {
  margin-right: 6px;
  color: var(--text-secondary);
  font-size: 16px;
}

.teacher-text {
  font-size: 14px;
}

/* 信息文本样式 */
.info-text {
  font-size: 14px;
}

.info-icon {
  margin-right: 6px;
  color: var(--text-secondary);
  font-size: 16px;
}

/* 返回按钮样式 */
.back-button {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* 折叠按钮样式 */
.collapse-button {
  position: absolute;
  top: 16px;
  left: 0px;
  z-index: 1000;
  background: var(--white);
  border: 1px solid #d9d9d9;
  border-radius: 0 6px 6px 0;
  font-size: 16px;
  width: 32px;
  height: 32px;
  box-shadow: var(--shadow-light);
  outline: none;
}

.collapse-button:hover {
  border: 1px solid #d9d9d9 !important;
  outline: none !important;
  box-shadow: var(--shadow-light) !important;
}

.collapse-button:focus {
  border: 1px solid #d9d9d9 !important;
  outline: none !important;
  box-shadow: var(--shadow-light) !important;
}

/* 侧边栏样式 */
.worksheet-sider {
  background: var(--background-light);
  border-right: 1px solid var(--border-color);
}

.sider-header {
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
  background: var(--white);
}

/* 练习背景按钮样式 */
.background-button {
  padding: 8px 12px;
  height: auto;
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
  border-radius: 6px;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

.background-button.active {
  background: var(--primary-color-light);
  font-weight: 700;
  color: var(--primary-color);
}

.background-button.active:hover {
  background: var(--primary-color-light) !important;
  color: var(--primary-color) !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

.background-button:not(.active) {
  background: transparent;
}

.background-button:not(.active):hover {
  background: var(--primary-color-light) !important;
  color: var(--primary-color) !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* 单元菜单样式 */
.unit-menu {
  border: none;
  background: transparent;
}

.unit-menu-item {
  margin: 8px 8px;
  border-radius: 8px;
  height: auto;
  line-height: normal;
  padding: 12px;
  width: calc(100% - 16px);
  transition: background-color 0.2s ease;
}

.unit-menu-item.selected {
  background: var(--primary-color-light);
  cursor: default !important;
}

.unit-menu-item:not(.selected) {
  background: transparent;
  cursor: pointer;
}

.unit-menu-item:not(.selected):hover {
  background: var(--primary-color-light) !important;
}

.unit-menu-item-text {
  font-size: 16px;
  display: flex;
  align-items: center;
  line-height: 1.4;
}

.unit-menu-item-text.selected {
  font-weight: 700;
  color: var(--primary-color);
}

.unit-menu-item-text:not(.selected) {
  font-weight: 500;
  color: var(--text-primary);
}

/* 单元序号字体样式与标题保持一致 */
.unit-number {
  font-weight: inherit;
}

/* 内容区域样式 */
.content-area {
  padding: 18px 24px;
  background: var(--white);
}

.section-title {
  margin: 0;
  color: var(--text-primary);
  font-size: 18px;
  display: flex;
  align-items: center;
}

.section-title-icon {
  margin-right: 8px;
  color: var(--primary-color);
}

/* 统一的背景样式 */
.unified-background {
  background: rgba(248, 250, 252, 1);
  padding: 8px 20px;
  border-radius: 8px;
  border: 2px dashed rgba(33, 128, 141, 0.3);
  font-size: 16px;
  line-height: 1.6;
  color: #434343;
}

/* 统一背景中所有文字大小为15px */
.unified-background * {
  font-size: 16px !important;
}

/* 统一背景样式中的HTML内容行间距调整 */
.unified-background p {
  margin: 4px 0;
  line-height: 1.6;
}

.unified-background div {
  margin: 4px 0;
  line-height: 1.6;
}

.unified-background h1,
.unified-background h2,
.unified-background h3,
.unified-background h4,
.unified-background h5,
.unified-background h6 {
  margin: 8px 0 4px 0;
  color: #262626;
  font-weight: 600;
}

.unified-background ul,
.unified-background ol {
  margin: 4px 0;
  padding-left: 20px;
}

.unified-background li {
  margin: 2px 0;
  line-height: 1.6;
}

/* 练习背景样式 */
.background-info {
  /* 继承统一背景样式 */
}

/* 单元背景样式 */
.unit-background {
  margin-top: 20px;
  /* 继承统一背景样式 */
}

/* Question collapse */
.question-collapse {
  background: white;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  margin-bottom: 24px;
  box-shadow: none !important;
  overflow: hidden;
}

.question-collapse .ant-collapse-item {
  border: none !important;
}

.question-collapse .ant-collapse-header {
  background: white !important;
  padding: 16px 20px !important;
  border: none !important;
  border-bottom: 1px solid #f5f5f5 !important;
  border-radius: 0 !important;
}

.question-collapse .ant-collapse-content {
  border: none !important;
}

.question-collapse .ant-collapse-content-box {
  padding: 0 20px 20px 20px !important;
}

/* 收起按钮样式调整 */
.question-collapse .ant-collapse-expand-icon {
  position: absolute !important;
  right: 8px !important;
  left: auto !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  color: var(--primary-color) !important;
  font-size: 16px !important;
  transition: all 0.3s ease !important;
}

.question-collapse .ant-collapse-expand-icon:hover {
  color: var(--primary-color) !important;
}

.question-collapse .ant-collapse-header-text {
  padding-right: 40px !important;
}

.question-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-right: 4px;
}

.question-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--primary-color);
  margin: 0;
  display: flex;
  align-items: center;
  line-height: 1.4;
}

.question-title-icon {
  margin-right: 8px;
  color: var(--primary-color);
}

.question-action-button {
  padding: 8px 12px;
  height: auto;
  font-size: 15px;
  font-weight: 500;
  color: var(--text-primary);
  border-radius: 6px;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  background: transparent;
  transition: all 0.3s ease;
}

.question-action-button .anticon {
  color: var(--primary-color);
}

.question-action-button:hover {
  background: var(--primary-color-light) !important;
  color: var(--primary-color) !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

.question-action-button:hover .anticon {
  color: var(--primary-color) !important;
}

/* Question background */
.question-background {
  /* 继承统一背景样式 */
}

/* Answer area */
.answer-area {
  margin-top: 16px;
}

.answer-textarea {
  margin-bottom: 16px;
  padding-bottom: 56px !important;
  font-size: 16px !important;
  background-color: rgba(33, 128, 141, 0.05) !important;
}

.answer-textarea:hover {
  border-color: rgb(33, 128, 141) !important;
}

.answer-textarea:focus,
.answer-textarea.ant-input:focus,
.answer-textarea.ant-input-focused {
  border-color: rgb(33, 128, 141) !important;
  box-shadow: 0 0 0 2px rgba(33, 128, 141, 0.2) !important;
  background-color: rgba(33, 128, 141, 0.05) !important;
}

.answer-actions {
  text-align: right;
}

.answer-button {
  margin-right: 8px;
  border: none;
  outline: none;
  box-shadow: none;
}

.ai-comment-button {
  background-color: rgb(33, 128, 141) !important;
  border-color: rgb(33, 128, 141) !important;
  color: white !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  font-size: 16px !important;
  font-weight: 500 !important;
}

.ai-comment-button:hover {
  background-color: rgba(33, 128, 141, 0.8) !important;
  border-color: rgba(33, 128, 141, 0.8) !important;
  color: white !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

.ai-comment-button:focus {
  background-color: rgb(33, 128, 141) !important;
  border-color: rgb(33, 128, 141) !important;
  color: white !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* Chat bubbles */
.chat-bubble-student {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
}

.chat-bubble-student-content {
  max-width: 97%;
}

.chat-bubble-student-message {
  background: rgba(33, 128, 141, 0.1);
  padding: 16px 24px 4px 24px;
  border-radius: 16px 16px 0 16px;
  border: 1px solid rgba(33, 128, 141, 0.15);
  margin-bottom: 8px;
  font-size: 16px;
  font-weight: 400;
  line-height: 1.6;
}

/* 学员作答气泡框内容样式 - 移除强制覆盖，使用 markdown 默认样式 */

.chat-bubble-student-label {
  text-align: right;
  font-size: 14px;
  color: #999;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.chat-bubble-ai {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 20px;
}

.chat-bubble-ai-content {
  max-width: 97%;
}

.chat-bubble-ai-message {
  background: rgba(229, 231, 235, 0.8);
  padding: 16px 24px 4px 24px;
  border-radius: 16px 16px 16px 0;
  border: 1px solid rgba(33, 128, 141, 0.15);
  margin-bottom: 8px;
  font-size: 16px;
  font-weight: 400;
  line-height: 1.6;
}

/* AI老师点评气泡框内容样式 - 移除强制覆盖，使用 markdown 默认样式 */

.chat-bubble-ai-label {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #999;
}

.chat-bubble-ai-icon {
  margin-right: 4px;
}

.retry-actions {
  text-align: right;
}

.retry-button {
  border: none;
  outline: none;
  box-shadow: none;
}

.retry-button:disabled {
  background-color: #f5f5f5 !important;
  border-color: #d9d9d9 !important;
  color: rgb(255, 255, 255) !important;
  cursor: not-allowed;
  opacity: 0.8;
}

.retry-button:disabled:hover {
  background-color: #f5f5f5 !important;
  border-color: #d9d9d9 !important;
  color: rgb(255, 255, 255) !important;
  opacity: 0.8;
}

/* 抽屉样式 */
.drawer-content {
  padding: 16px 0;
}

.drawer-section {
  margin-bottom: 24px;
}

.drawer-section:last-child {
  margin-bottom: 0;
}

.guide-item {
  margin-bottom: 16px;
}

/* Worksheet 布局样式 */
.worksheet-layout {
  min-height: 70vh;
  background: var(--white);
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

.worksheet-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0;
}

.worksheet-header {
  margin-bottom: 0;
}

.worksheet-header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.worksheet-header-info {
  margin-bottom: 16px;
}

.worksheet-header-meta {
  display: flex;
  align-items: center;
  gap: 24px;
  flex-wrap: wrap;
}

.unit-menu-container {
  height: calc(100vh - 200px);
  max-height: calc(100vh - 200px);
  overflow-y: auto;
  padding: 8px 0;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50vh;
}

/* 折叠菜单按钮样式 */
.menu-fold-button {
  font-size: 16px;
  width: 28px;
  height: 28px;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

.menu-fold-button:hover {
  background: var(--primary-color-light) !important;
  color: var(--primary-color) !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* 序号样式 */
.unit-number {
  padding: 2px 6px;
  border-radius: 6px;
  font-size: 14px;
  margin-right: 12px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  min-height: 24px;
  text-align: center;
  line-height: 1;
  border: 1px solid var(--primary-color);
  color: var(--primary-color);
  background-color: transparent;
}

/* 选中状态的序号样式 */
.unit-menu-item.selected .unit-number {
  background-color: var(--primary-color);
  color: white;
  border: 1px solid var(--primary-color);
}

.section-number {
  background-color: #21808d;
  color: white;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 18px;
  font-weight: 600;
  margin-right: 12px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  min-height: 24px;
  text-align: center;
  line-height: 1;
  vertical-align: top;
}

.question-number {
  background-color: #21808d;
  color: white;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 600;
  margin-right: 12px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  min-height: 24px;
  text-align: center;
  line-height: 1;
  vertical-align: top;
}

/* 练习目标抽屉样式 */
.framework-section {
  background: rgba(33, 128, 141, 0.1);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #f0f0f0;
}

.framework-header {
  margin-bottom: 16px;
}

.framework-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.framework-logo {
  width: 24px;
  height: 24px;
  object-fit: cover;
}

.modules-container {
  margin-top: 16px;
}

.modules-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 12px;
}

.module-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
  transition: all 0.2s ease;
}

.module-item:hover {
  border-color: var(--primary-color);
  box-shadow: 0 2px 8px rgba(33, 128, 141, 0.1);
}

.module-name {
  font-weight: 500;
  color: #333;
  text-align: center;
  font-size: 14px;
}
