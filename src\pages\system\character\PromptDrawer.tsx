import React, { useState, useEffect } from 'react';
import { Drawer, Button, Input, message, Spin, Tabs } from 'antd';
import { updateCharacter, type CharacterUpdate, type CharacterResponse } from '../../../services/system/character';

interface PromptDrawerProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
  character: CharacterResponse | null;
  loading: boolean;
  tenantId: number;
}

const { TextArea } = Input;

const PromptDrawer: React.FC<PromptDrawerProps> = ({
  visible,
  onClose,
  onSuccess,
  character,
  loading,
  tenantId
}) => {
  const [pvProfile, setPvProfile] = useState<string>('');
  const [pvAbility, setPvAbility] = useState<string>('');
  const [pvRestriction, setPvRestriction] = useState<string>('');
  const [submitLoading, setSubmitLoading] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<string>('profile');
  
  // 保存原始值用于比较
  const [originalValues, setOriginalValues] = useState<{
    pvProfile: string;
    pvAbility: string;
    pvRestriction: string;
  }>({ pvProfile: '', pvAbility: '', pvRestriction: '' });

  // 当抽屉打开且有人物角色数据时，初始化提示词内容
  useEffect(() => {
    if (visible && character) {
      const profile = character.pv_profile || '';
      const ability = character.pv_ability || '';
      const restriction = character.pv_restriction || '';
      
      setPvProfile(profile);
      setPvAbility(ability);
      setPvRestriction(restriction);
      
      // 保存原始值
      setOriginalValues({
        pvProfile: profile,
        pvAbility: ability,
        pvRestriction: restriction
      });
    }
  }, [visible, character]);

  // 处理保存
  const handleSave = async () => {
    if (!character) return;
    
    try {
      setSubmitLoading(true);
      
      // 只更新发生变化的字段
      const updateData: Partial<CharacterUpdate> = {};
      
      if (pvProfile !== originalValues.pvProfile) {
        updateData.pv_profile = pvProfile || undefined;
      }
      
      if (pvAbility !== originalValues.pvAbility) {
        updateData.pv_ability = pvAbility || undefined;
      }
      
      if (pvRestriction !== originalValues.pvRestriction) {
        updateData.pv_restriction = pvRestriction || undefined;
      }
      
      // 如果没有字段发生变化，则不调用接口
      if (Object.keys(updateData).length === 0) {
        message.info('没有数据变化');
        onClose();
        return;
      }
      
      // 调用API更新提示词
      await updateCharacter(character.id, updateData, tenantId);
      
      message.success('提示词更新成功');
      onSuccess();
      onClose();
    } catch (error) {
      console.error('更新提示词失败:', error);
      message.error('更新提示词失败，请重试');
    } finally {
      setSubmitLoading(false);
    }
  };

  // 清空状态
  useEffect(() => {
    if (!visible) {
      setPvProfile('');
      setPvAbility('');
      setPvRestriction('');
      setActiveTab('profile');
      setOriginalValues({ pvProfile: '', pvAbility: '', pvRestriction: '' });
    }
  }, [visible]);

  const tabItems = [
    {
      key: 'profile',
      label: '人物资料',
      children: (
        <div>
          <div style={{ marginBottom: 16, padding: '12px 16px', backgroundColor: '#f5f5f5', borderRadius: '6px' }}>
            <div style={{ color: '#666', fontSize: '14px' }}>
              用于定义人物的基本信息、背景故事、特征等，这些信息将作为提示词变量在对话中使用
            </div>
          </div>
          <TextArea
            placeholder="请输入人物资料提示词，例如：这是一个经验丰富的项目经理，拥有10年的软件开发管理经验..."
            value={pvProfile}
            onChange={(e) => setPvProfile(e.target.value)}
            rows={20}
            style={{ height: 'calc(100vh - 320px)', overflow: 'auto' }}
            showCount
            maxLength={2000}
          />
        </div>
      ),
    },
    {
      key: 'ability',
      label: '人物能力',
      children: (
        <div>
          <div style={{ marginBottom: 16, padding: '12px 16px', backgroundColor: '#f5f5f5', borderRadius: '6px' }}>
            <div style={{ color: '#666', fontSize: '14px' }}>
              定义人物所具备的技能、专业知识、能力范围等，用于指导AI在扮演该角色时的表现
            </div>
          </div>
          <TextArea
            placeholder="请输入人物能力提示词，例如：能够进行项目规划、团队管理、风险评估、技术架构设计..."
            value={pvAbility}
            onChange={(e) => setPvAbility(e.target.value)}
            rows={20}
            style={{ height: 'calc(100vh - 320px)', overflow: 'auto' }}
            showCount
            maxLength={2000}
          />
        </div>
      ),
    },
    {
      key: 'restriction',
      label: '人物限制',
      children: (
        <div>
          <div style={{ marginBottom: 16, padding: '12px 16px', backgroundColor: '#f5f5f5', borderRadius: '6px' }}>
            <div style={{ color: '#666', fontSize: '14px' }}>
              定义人物的行为限制、不能做的事情、回答范围边界等，确保AI扮演角色时符合设定
            </div>
          </div>
          <TextArea
            placeholder="请输入人物限制提示词，例如：不能提供法律建议、不涉及敏感政治话题、只在项目管理领域内回答..."
            value={pvRestriction}
            onChange={(e) => setPvRestriction(e.target.value)}
            rows={20}
            style={{ height: 'calc(100vh - 320px)', overflow: 'auto' }}
            showCount
            maxLength={2000}
          />
        </div>
      ),
    },
  ];

  return (
    <Drawer
      title={`提示词编辑 - ${character?.name || ''}`}
      placement="right"
      width="70%"
      onClose={onClose}
      open={visible}
      maskClosable={true}
      extra={
        <Button
          type="primary"
          onClick={handleSave}
          loading={submitLoading}
          disabled={loading || !character}
        >
          保存
        </Button>
      }
    >
      <Spin spinning={loading} tip={loading ? "正在加载人物角色详情..." : "正在保存..."}>
        {loading && !character ? (
          <div style={{ height: '400px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <div>正在加载人物角色详情...</div>
          </div>
        ) : (
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            items={tabItems}
            style={{ height: '100%' }}
          />
        )}
      </Spin>
    </Drawer>
  );
};

export default PromptDrawer; 