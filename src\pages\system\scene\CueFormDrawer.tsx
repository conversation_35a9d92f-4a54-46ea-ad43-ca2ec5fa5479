import React, { useState, useEffect } from 'react';
import { Drawer, Form, Input, Button, message, Select, Radio } from 'antd';
import { 
  createSceneCue,
  updateSceneCue,
  type SceneCueResponse,
  type SceneCueCreate,
  type SceneCueUpdate,
  type SceneCharacterResponse
} from '../../../services/system/scene';
import { getGlobalTenantInfo } from '../../../components/GlobalTenantSelector';
import { showError } from '../../../utils/errorHandler';

const { TextArea } = Input;
const { Option } = Select;

interface CueFormDrawerProps {
  visible: boolean;
  mode: 'create' | 'edit';
  sceneId?: string;
  cue?: SceneCueResponse;
  characters: SceneCharacterResponse[];
  onClose: () => void;
  onSuccess: () => void;
}

const CueFormDrawer: React.FC<CueFormDrawerProps> = ({
  visible,
  mode,
  sceneId,
  cue,
  characters,
  onClose,
  onSuccess
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // 获取当前租户ID
  const getCurrentTenantId = (): number | null => {
    const tenantInfo = getGlobalTenantInfo();
    return tenantInfo?.id || null;
  };

  // 创建剧情线索
  const handleCreateCue = async () => {
    try {
      const values = await form.validateFields();
      const tenantId = getCurrentTenantId();
      if (!tenantId || !sceneId) {
        message.error('缺少必要参数');
        return;
      }

      // 验证必选字段
      if (!values.character_name) {
        message.error('请选择人物');
        return;
      }
      if (!values.content) {
        message.error('请输入发言内容');
        return;
      }

      setLoading(true);
      
      // 根据角色名找到对应的cid
      const selectedCharacter = characters.find(char => char.name === values.character_name);
      if (!selectedCharacter) {
        message.error('找不到对应的角色信息');
        return;
      }

      const createData: SceneCueCreate = {
        tenant_id: tenantId,
        sid: parseInt(sceneId),
        cid: selectedCharacter.cid,
        content: values.content,
        serial: values.speech_mode
      };

      await createSceneCue(createData);
      message.success('剧情线索创建成功');
      handleClose();
      onSuccess();
    } catch (error) {
      if (error && typeof error === 'object' && 'errorFields' in error) {
        // 表单验证错误，不需要显示错误消息
        return;
      }
      showError(error, '创建剧情线索失败');
    } finally {
      setLoading(false);
    }
  };

  // 更新剧情线索
  const handleUpdateCue = async () => {
    try {
      if (!cue) return;
      
      const values = await form.validateFields();
      const tenantId = getCurrentTenantId();
      if (!tenantId) {
        message.error('缺少必要参数');
        return;
      }

      // 验证必选字段
      if (!values.character_name) {
        message.error('请选择人物');
        return;
      }
      if (!values.content) {
        message.error('请输入发言内容');
        return;
      }

      setLoading(true);
      
      const updateData: SceneCueUpdate = {
        content: values.content,
        serial: values.speech_mode
      };

      await updateSceneCue(cue.id, updateData, tenantId);
      message.success('剧情线索更新成功');
      handleClose();
      onSuccess();
    } catch (error) {
      if (error && typeof error === 'object' && 'errorFields' in error) {
        return;
      }
      showError(error, '更新剧情线索失败');
    } finally {
      setLoading(false);
    }
  };

  // 关闭抽屉
  const handleClose = () => {
    form.resetFields();
    onClose();
  };

  // 提交表单
  const handleSubmit = () => {
    if (mode === 'create') {
      handleCreateCue();
    } else {
      handleUpdateCue();
    }
  };

  // 当抽屉打开时，根据模式初始化数据
  useEffect(() => {
    if (visible) {
      // 获取第一个played=1的角色作为默认选中
      const defaultCharacter = characters.find(character => character.played === 1);
      const defaultCharacterName = defaultCharacter?.name || '';

      if (mode === 'create') {
        form.resetFields();
        form.setFieldsValue({
          speech_mode: 1,  // 重置时确保默认值
          character_name: defaultCharacterName // 默认选中第一个played=1的角色
        });
      } else if (mode === 'edit' && cue) {
        // 根据cue.cid找到对应的角色名
        const editCharacter = characters.find(char => char.cid === cue.cid);
        const editCharacterName = editCharacter?.name || defaultCharacterName;
        
        form.setFieldsValue({
          content: cue.content || '',
          character_name: editCharacterName,
          speech_mode: Number(cue.serial ?? 1) // 强制转为 number，确保 Radio.Group 正常选中
        });
      }
    }
  }, [visible, mode, cue, characters]);

  return (
    <Drawer
      title={mode === 'edit' ? "编辑剧情线索" : "添加剧情线索"}
      width="60%"
      open={visible}
      onClose={handleClose}
      extra={
        <div style={{ display: 'flex', gap: '8px' }}>
          <Button onClick={handleClose}>
            取消
          </Button>
          <Button 
            type="primary" 
            onClick={handleSubmit}
            loading={loading}
          >
            {mode === 'edit' ? "更新" : "创建"}
          </Button>
        </div>
      }
    >
      <Form
        form={form}
        layout="vertical"
        requiredMark={false}
        initialValues={{
          speech_mode: 1  // 默认为顺序发言
        }}
      >
        <div style={{ marginBottom: '16px' }}>
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: '8px',
            marginBottom: '12px'
          }}>
            <span style={{ 
              fontSize: '14px', 
              color: '#666'
            }}>
              当
            </span>
            
            <Form.Item
              name="character_name"
              style={{ margin: 0, minWidth: '200px' }}
              rules={[
                { required: true, message: '请选择人物' }
              ]}
            >
              <Select
                placeholder="选择人物"
                style={{ minWidth: '200px' }}
              >
                {characters
                  .filter(character => character.played === 1)
                  .map(character => (
                    <Option key={character.name} value={character.name}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                        {character.avatar && (
                          <img 
                            src={character.avatar} 
                            alt={character.name}
                            style={{ 
                              width: '28px', 
                              height: '28px', 
                              borderRadius: '50%',
                              objectFit: 'cover'
                            }}
                          />
                        )}
                        <span style={{ color: '#333', fontWeight: 'bold', fontSize: '15px' }}>
                          {character.name}
                        </span>
                      </div>
                    </Option>
                  ))}
              </Select>
            </Form.Item>
          </div>

          <Form.Item
            name="content"
            style={{ margin: 0, marginBottom: '12px' }}
            rules={[
              { required: true, message: '请输入发言内容' },
              { max: 2000, message: '发言内容长度不能超过2000个字符' }
            ]}
          >
            <TextArea 
              placeholder="输入发言内容" 
              rows={6}
              showCount
              maxLength={2000}
              style={{ resize: 'vertical' }}
            />
          </Form.Item>

          <div style={{ marginBottom: '24px' }}>
            <span style={{ 
              fontSize: '14px', 
              color: '#666'
            }}>
              时
            </span>
          </div>
        </div>

        <Form.Item
          label="引发发言形式"
          name="speech_mode"
          rules={[
            { required: true, message: '请选择引发发言形式' }
          ]}
        >
          <Radio.Group>
            <Radio value={0}>并行发言</Radio>
            <Radio value={1}>顺序发言</Radio>
          </Radio.Group>
        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default CueFormDrawer; 