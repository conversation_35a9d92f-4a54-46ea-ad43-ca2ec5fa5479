import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, InputNumber, Row, Col } from 'antd';
import { createScene, updateScene, type SceneCreate, type SceneUpdate, type SceneResponse } from '../../../services/system/scene';
import { showError, showSuccess } from '../../../utils/errorHandler';

const { TextArea } = Input;

interface SceneFormProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  scene?: SceneResponse | null;
  tenantId: number;
}

const SceneForm: React.FC<SceneFormProps> = ({
  visible,
  onCancel,
  onSuccess,
  scene,
  tenantId,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const isEditing = !!scene;

  useEffect(() => {
    if (visible) {
      if (scene) {
        // 编辑模式，填充表单数据
        form.setFieldsValue({
          title: scene.title,
          intro: scene.intro || '', // 改为intro
          duration: scene.duration || undefined,
          version: scene.version || '',
          notes: scene.notes || '',
        });
      } else {
        // 新增模式，重置表单
        form.resetFields();
      }
    }
  }, [visible, scene, form]);

  const handleSubmit = async () => {
    if (!tenantId) {
      showError('请先选择租户', '操作失败');
      return;
    }

    try {
      const values = await form.validateFields();
      setLoading(true);

      if (isEditing && scene) {
        // 编辑场景 - 只传递有值的字段
        const updateData: SceneUpdate = {};
        
        if (values.title) {
          updateData.title = values.title;
        }
        if (values.intro) {
          updateData.intro = values.intro;
        }
        if (values.duration) {
          updateData.duration = values.duration;
        }
        if (values.version) {
          updateData.version = values.version;
        }
        if (values.notes) {
          updateData.notes = values.notes;
        }
        
        await updateScene(scene.id, updateData);
        showSuccess('场景更新成功');
      } else {
        // 创建场景 - 传递所有字段
        const createData: SceneCreate = {
          title: values.title,
          intro: values.intro || undefined,
          duration: values.duration || undefined,
          version: values.version || undefined,
          notes: values.notes || undefined,
          active: 1, // 默认启用
          published: 0, // 默认未发布
          tenant_id: tenantId,
        };
        await createScene(createData);
        showSuccess('场景创建成功');
      }
      
      onSuccess();
    } catch (error) {
      if (error && typeof error === 'object' && 'errorFields' in error) {
        // 表单验证错误
        return;
      }
      showError(error, isEditing ? '更新场景失败' : '创建场景失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title={isEditing ? '编辑场景' : '添加场景'}
      open={visible}
      onOk={handleSubmit}
      onCancel={handleCancel}
      confirmLoading={loading}
      destroyOnHidden
      width={520}
    >
      <Form
        form={form}
        layout="vertical"
      >
        <Form.Item
          label="场景名称"
          name="title"
          rules={[
            { required: true, message: '请输入场景名称' },
            { max: 100, message: '场景名称长度不能超过100字符' },
          ]}
        >
          <Input 
            placeholder="请输入场景名称" 
            showCount 
            maxLength={100}
          />
        </Form.Item>

        <Form.Item
          label="简介"
          name="intro" // 改为intro
          rules={[
            { max: 1000, message: '简介长度不能超过1000字符' },
          ]}
        >
          <TextArea
            placeholder="请输入场景简介（可选）"
            rows={3}
            showCount
            maxLength={1000}
          />
        </Form.Item>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="练习时长（分钟）"
              name="duration"
              rules={[
                { type: 'number', min: 1, message: '练习时长必须大于0分钟' },
              ]}
            >
              <InputNumber
                placeholder="预估练习时长"
                min={1}
                style={{ width: '100%' }}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="版本"
              name="version"
              rules={[
                { max: 50, message: '版本长度不能超过50字符' },
              ]}
            >
              <Input 
                placeholder="请输入场景版本（可选）" 
                showCount 
                maxLength={50}
              />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          label="备注"
          name="notes"
          rules={[
            { max: 1000, message: '备注长度不能超过1000字符' },
          ]}
        >
          <TextArea
            placeholder="请输入场景备注（可选）"
            rows={3}
            showCount
            maxLength={1000}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default SceneForm; 