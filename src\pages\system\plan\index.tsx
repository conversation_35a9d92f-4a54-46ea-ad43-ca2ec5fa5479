import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Card, Table, Button, Space, Input, Tooltip, Modal, Avatar, Breadcrumb } from 'antd';
import { PlusOutlined, EditOutlined, StopOutlined, ReloadOutlined, ExclamationCircleOutlined, DeleteFilled, RollbackOutlined, UndoOutlined, FileImageOutlined, HomeOutlined, EyeOutlined } from '@ant-design/icons';
import type { TableProps } from 'antd/es/table';
import { 
  getPlans, 
  updatePlan,
  type PlanResponse
} from '../../../services/system/plan';
import { showError, showSuccess } from '../../../utils/errorHandler';
import { getGlobalTenantInfo, type GlobalTenantInfo } from '../../../components/GlobalTenantSelector';
import PlanForm from './PlanForm';
import PicUploader from './PicUploader';
import { Link, useNavigate } from 'react-router-dom';

const { Search } = Input;
const { confirm } = Modal;

const PlanManagement: React.FC = () => {
  const navigate = useNavigate();
  const [plans, setPlans] = useState<PlanResponse[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [picUploaderVisible, setPicUploaderVisible] = useState(false);
  const [currentPlan, setCurrentPlan] = useState<PlanResponse | null>(null);
  const [selectedTenantId, setSelectedTenantId] = useState<number | undefined>(undefined);
  const [searchName, setSearchName] = useState('');
  const [searchNameValue, setSearchNameValue] = useState(''); // 用于存储名称输入框的当前值
  const [searchDescription, setSearchDescription] = useState('');
  const [searchDescriptionValue, setSearchDescriptionValue] = useState(''); // 用于存储描述输入框的当前值
  const [searchNotes, setSearchNotes] = useState('');
  const [searchNotesValue, setSearchNotesValue] = useState(''); // 用于存储备注输入框的当前值
  const [isRecycleBin, setIsRecycleBin] = useState(false);
  const [tableKey, setTableKey] = useState<number>(0);

  // 分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 创建对搜索框的引用
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const searchNameInputRef = useRef<any>(null);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const searchDescriptionInputRef = useRef<any>(null);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const searchNotesInputRef = useRef<any>(null);



  // 从 GlobalTenantSelector 读取全局租户信息
  const loadGlobalTenant = useCallback(() => {
    const tenantInfo: GlobalTenantInfo | null = getGlobalTenantInfo();
    if (tenantInfo) {
      setSelectedTenantId(tenantInfo.id);
      return tenantInfo;
    } else {
      setSelectedTenantId(undefined);
      return null;
    }
  }, []);

  // 监听全局租户变化
  useEffect(() => {
    // 初始加载
    loadGlobalTenant();

    // 监听 localStorage 变化（跨标签页）
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'globalTenant') {
        loadGlobalTenant();
      }
    };

    // 监听自定义事件（同一页面内的更新）
    const handleGlobalTenantChange = (e: Event) => {
      const customEvent = e as CustomEvent;
      const { id } = customEvent.detail;
      if (id) {
        setSelectedTenantId(id);
      } else {
        setSelectedTenantId(undefined);
      }
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('globalTenantChanged', handleGlobalTenantChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('globalTenantChanged', handleGlobalTenantChange);
    };
  }, [loadGlobalTenant]);

  // 获取练习计划列表
  const fetchPlans = useCallback(async () => {
    if (!selectedTenantId) {
      setPlans([]);
      return;
    }

    try {
      setLoading(true);
      
      // 按照ID降序排序，最新的在前面
      const sortBy = 'id';
      const sortOrder = 'desc';
      
      const response = await getPlans({
        tenant_id: selectedTenantId,
        skip: (pagination.current - 1) * pagination.pageSize,
        limit: pagination.pageSize,
        active: isRecycleBin ? 0 : 1, // 使用active参数进行服务端过滤
        sort_by: sortBy,
        sort_order: sortOrder,
        // 添加搜索参数，如果为空则不传递给API
        ...(searchName && { name: searchName }),
        ...(searchDescription && { description: searchDescription }),
        ...(searchNotes && { notes: searchNotes })
      });
      
      setPlans(response.items);
      setPagination(prev => ({
        ...prev,
        total: response.total
      }));
    } catch (error) {
      showError(error, '获取练习计划列表失败');
    } finally {
      setLoading(false);
    }
  }, [selectedTenantId, searchName, searchDescription, searchNotes, isRecycleBin, pagination.current, pagination.pageSize]);

  useEffect(() => {
    if (selectedTenantId) {
      fetchPlans();
    }
  }, [fetchPlans]);

  // 处理名称搜索
  const handleNameSearch = (value: string) => {
    setSearchName(value);
    setSearchNameValue(value);
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  // 处理名称搜索输入框值变化，但不触发搜索
  const handleNameInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchNameValue(e.target.value);
  };

  // 处理描述搜索
  const handleDescriptionSearch = (value: string) => {
    setSearchDescription(value);
    setSearchDescriptionValue(value);
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  // 处理描述搜索输入框值变化，但不触发搜索
  const handleDescriptionInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchDescriptionValue(e.target.value);
  };

  // 处理备注搜索
  const handleNotesSearch = (value: string) => {
    setSearchNotes(value);
    setSearchNotesValue(value);
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  // 处理备注搜索输入框值变化，但不触发搜索
  const handleNotesInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchNotesValue(e.target.value);
  };

  // 切换回收站模式
  const toggleRecycleBin = () => {
    setIsRecycleBin(!isRecycleBin);
    // 重置分页状态，避免切换模式时出现显示异常
    setPagination(prev => ({
      ...prev,
      current: 1,
    }));
  };

  // 处理添加练习计划
  const handleAdd = () => {
    if (!selectedTenantId) {
      showError(null, '请先在顶部导航栏选择租户');
      return;
    }
    setCurrentPlan(null);
    setModalVisible(true);
  };

  // 处理编辑练习计划
  const handleEdit = (record: PlanResponse) => {
    setCurrentPlan(record);
    setModalVisible(true);
  };

  // 处理上传图片
  const handleUploadPic = (record: PlanResponse) => {
    setCurrentPlan(record);
    setPicUploaderVisible(true);
  };

  // 处理查看详情
  const handleViewDetail = (record: PlanResponse) => {
    navigate(`/system/plan/${record.id}`);
  };

  // 处理禁用练习计划（将删除改为禁用）
  const handleDisable = (id: number) => {
    const actionText = '禁用';
    confirm({
      title: `确认${actionText}`,
      icon: <ExclamationCircleOutlined />,
      content: `确定要${actionText}这个练习计划吗？禁用后可在回收站中恢复。`,
      okText: '确认',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          // 将练习计划禁用（设置active为0）
          await updatePlan(id, { active: 0 });
          showSuccess(`${actionText}成功`);
          fetchPlans();
        } catch (error: unknown) {
          showError(error, `${actionText}练习计划失败`);
        }
      },
    });
  };

  // 处理恢复练习计划
  const handleRestore = async (planId: number) => {
    try {
      await updatePlan(planId, { active: 1 });
      showSuccess('恢复成功');
      fetchPlans();
    } catch (error) {
      showError(error, '恢复练习计划失败');
    }
  };

  // 表单提交成功后的回调
  const handleFormSuccess = () => {
    setModalVisible(false);
    fetchPlans();
  };

  // 图片上传成功后的回调
  const handlePicUploadSuccess = () => {
    setPicUploaderVisible(false);
    fetchPlans();
  };

  // 处理重置刷新
  const handleRefresh = async () => {
    // 清空搜索框
    setSearchName('');
    setSearchNameValue('');
    setSearchDescription('');
    setSearchDescriptionValue('');
    setSearchNotes('');
    setSearchNotesValue('');

    // 重置分页状态
    setPagination(prev => ({
      ...prev,
      current: 1,
      pageSize: 10,
    }));

    setTableKey(prevKey => prevKey + 1);
    
    // 显式调用接口重新获取数据
    if (selectedTenantId) {
      await fetchPlans();
    }
  };

  // 处理表格变化（只保留分页处理）
  const handleTableChange: TableProps<PlanResponse>['onChange'] = (paginationConfig) => {
    if (paginationConfig) {
      setPagination(prev => ({
        ...prev,
        current: paginationConfig.current || 1,
        pageSize: paginationConfig.pageSize || 10,
      }));
    }
  };

  // 表格列定义
  const getColumns = () => {
    const baseColumns = [
      {
        title: 'ID',
        dataIndex: 'id',
        key: 'id',
        width: 100,
      },
      {
        title: '图片',
        dataIndex: 'pic',
        key: 'pic',
        width: 80,
        render: (pic: string | null, record: PlanResponse) => (
          <div
            style={{ cursor: 'pointer' }}
            onClick={() => handleUploadPic(record)}
          >
            <Avatar
              size={40}
              src={pic}
              icon={<FileImageOutlined />}
              shape="square"
              style={{ backgroundColor: '#f0f0f0' }}
            />
          </div>
        ),
      },
      {
        title: '名称',
        dataIndex: 'name',
        key: 'name',
      },
      {
        title: '描述',
        dataIndex: 'description',
        key: 'description',
        ellipsis: {
          showTitle: false,
        },
        render: (text: string | null) => (
          <Tooltip title={text} placement="topLeft">
            {text || '-'}
          </Tooltip>
        ),
      },
      {
        title: '备注',
        dataIndex: 'notes',
        key: 'notes',
        ellipsis: {
          showTitle: false,
        },
        render: (text: string | null) => (
          <Tooltip title={text} placement="topLeft">
            {text || '-'}
          </Tooltip>
        ),
      },
    ];

    if (isRecycleBin) {
      // 回收站模式下的操作列
      baseColumns.push({
        title: '操作',
        key: 'action',
        width: 160,
        render: (_: unknown, record: PlanResponse) => (
          <Space size="middle">
            <Tooltip title="详情">
              <Button
                type="primary"
                style={{ backgroundColor: '#1890ff', borderColor: '#1890ff' }}
                icon={<EyeOutlined />}
                size="small"
                onClick={() => handleViewDetail(record)}
              />
            </Tooltip>
            <Tooltip title="编辑">
              <Button
                type="primary"
                icon={<EditOutlined />}
                size="small"
                onClick={() => handleEdit(record)}
              />
            </Tooltip>
            <Tooltip title="恢复">
              <Button
                type="primary"
                style={{ backgroundColor: '#52c41a', borderColor: '#52c41a' }}
                icon={<UndoOutlined />}
                size="small"
                onClick={() => handleRestore(record.id)}
              />
            </Tooltip>
          </Space>
        ),
      } as never);
    } else {
      // 正常模式下的操作列
      baseColumns.push({
        title: '操作',
        key: 'action',
        width: 160,
        render: (_: unknown, record: PlanResponse) => (
          <Space size="middle">
            <Tooltip title="详情">
              <Button
                type="primary"
                style={{ backgroundColor: '#1890ff', borderColor: '#1890ff' }}
                icon={<EyeOutlined />}
                size="small"
                onClick={() => handleViewDetail(record)}
              />
            </Tooltip>
            <Tooltip title="编辑">
              <Button
                type="primary"
                icon={<EditOutlined />}
                size="small"
                onClick={() => handleEdit(record)}
              />
            </Tooltip>
            <Tooltip title="禁用">
              <Button
                type="primary"
                style={{ backgroundColor: '#ff4d4f', borderColor: '#ff4d4f' }}
                icon={<StopOutlined />}
                size="small"
                onClick={() => handleDisable(record.id)}
              />
            </Tooltip>
          </Space>
        ),
      } as never);
    }

    return baseColumns;
  };

  return (
    <div>
      {/* 面包屑导航 */}
      <Breadcrumb 
        style={{ marginBottom: 16 }}
        items={[
          {
            title: (
              <Link to="/system/home">
                <HomeOutlined /> 主页
              </Link>
            ),
          },
          {
            title: '租户空间',
          },
          {
            title: '练习计划',
          },
        ]}
      />

      <Card
        title={isRecycleBin ? "练习计划回收站" : "练习计划"}
        extra={
          <Space>
            <Search
              placeholder="搜索计划名称"
              allowClear
              onSearch={handleNameSearch}
              style={{ width: 160 }}
              ref={searchNameInputRef}
              value={searchNameValue}
              onChange={handleNameInputChange}
            />
            <Search
              placeholder="搜索描述"
              allowClear
              onSearch={handleDescriptionSearch}
              style={{ width: 160 }}
              ref={searchDescriptionInputRef}
              value={searchDescriptionValue}
              onChange={handleDescriptionInputChange}
            />
            <Search
              placeholder="搜索备注"
              allowClear
              onSearch={handleNotesSearch}
              style={{ width: 160 }}
              ref={searchNotesInputRef}
              value={searchNotesValue}
              onChange={handleNotesInputChange}
            />
            {/* 添加练习计划按钮只在非回收站模式下显示 */}
            {!isRecycleBin && selectedTenantId && (
              <Tooltip title="添加练习计划">
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAdd}
                />
              </Tooltip>
            )}
            <Tooltip title="重置刷新">
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
              />
            </Tooltip>
            {/* 回收站/返回按钮 */}
            {isRecycleBin ? (
              <Tooltip title="返回">
                <Button
                  type="primary"
                  icon={<RollbackOutlined />}
                  onClick={toggleRecycleBin}
                />
              </Tooltip>
            ) : (
              <Tooltip title="回收站">
                <Button
                  type="default"
                  icon={<DeleteFilled />}
                  onClick={toggleRecycleBin}
                />
              </Tooltip>
            )}
          </Space>
        }
      >
        <Table
          key={tableKey}
          columns={getColumns()}
          dataSource={plans}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          locale={{
            emptyText: selectedTenantId ? (isRecycleBin ? '回收站中没有练习计划' : '暂无练习计划数据') : '请先在顶部导航栏选择租户'
          }}
          onChange={handleTableChange}
          sortDirections={['ascend', 'descend', 'ascend']}
          showSorterTooltip={false}
        />
      </Card>

      {selectedTenantId && (
        <PlanForm
          visible={modalVisible}
          onCancel={() => setModalVisible(false)}
          onSuccess={handleFormSuccess}
          plan={currentPlan}
          tenantId={selectedTenantId}
        />
      )}

      {selectedTenantId && currentPlan && (
        <PicUploader
          visible={picUploaderVisible}
          onCancel={() => setPicUploaderVisible(false)}
          onSuccess={handlePicUploadSuccess}
          planId={currentPlan.id}
          tenantId={selectedTenantId}
          currentPic={currentPlan.pic}
        />
      )}
    </div>
  );
};

export default PlanManagement; 