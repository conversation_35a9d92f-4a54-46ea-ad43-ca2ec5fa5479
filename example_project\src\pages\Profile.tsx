import React, { useState, useEffect } from 'react';
import { Card, Typography, Avatar, Button, Form, Input, message, Modal } from 'antd';
import { UserOutlined, LockOutlined, EyeInvisibleOutlined, EyeTwoTone } from '@ant-design/icons';
import { useAuthStore } from '../utils/authStore';
import { getProfile, updatePassword } from '../services/auth';
import MainLayout from '../layouts/MainLayout';
import '../styles/Profile.css';

const { Title, Text } = Typography;

interface ProfileData {
  uid: number;
  username: string;
  name: string;
  tenant_id: number;
  gender: number;
}

interface PasswordFormValues {
  oldPassword: string;
  newPassword: string;
  confirmPassword: string;
}

const Profile: React.FC = () => {
  const { user } = useAuthStore();
  const [profileData, setProfileData] = useState<ProfileData | null>(null);
  const [, setLoading] = useState(true);
  const [passwordModalVisible, setPasswordModalVisible] = useState(false);
  const [passwordForm] = Form.useForm();
  const [passwordLoading, setPasswordLoading] = useState(false);

  // 获取用户详细信息
  const fetchProfile = async () => {
    try {
      setLoading(true);
      const data = await getProfile();
      setProfileData(data);
    } catch (error) {
      console.error('获取用户信息失败:', error);
      message.error('获取用户信息失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProfile();
  }, []);

  // 处理密码修改
  const handlePasswordUpdate = async (values: PasswordFormValues) => {
    try {
      setPasswordLoading(true);
      await updatePassword(values.oldPassword, values.newPassword);
      message.success('密码修改成功');
      setPasswordModalVisible(false);
      passwordForm.resetFields();
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      console.error('密码修改失败:', error);
      let errorMessage = '密码修改失败，请稍后重试';
      
      if (error.response) {
        const status = error.response.status;
        const data = error.response.data;
        
        if (status === 400) {
          errorMessage = '原密码错误';
        } else if (status === 422) {
          errorMessage = '密码格式不正确';
        } else if (data && data.detail) {
          errorMessage = typeof data.detail === 'string' ? data.detail : '密码修改失败';
        } else if (data && data.message) {
          errorMessage = typeof data.message === 'string' ? data.message : '密码修改失败';
        }
      }
      
      message.error(errorMessage);
    } finally {
      setPasswordLoading(false);
    }
  };

  // 取消密码修改
  const handlePasswordCancel = () => {
    setPasswordModalVisible(false);
    passwordForm.resetFields();
  };

  return (
    <MainLayout>
      <div className="profile-container">
        <div className="profile-header">
          <Title level={2}>账户设置</Title>
          <Text type="secondary">管理您的账户信息和 API 密钥</Text>
        </div>



        <div className="profile-content">
          {/* 用户头像和基本信息 */}
          <Card className="profile-card">
            <div className="profile-avatar-section">
              <Avatar
                size={120}
                icon={<UserOutlined />}
                className="profile-avatar"
              />
              <div className="profile-basic-info">
                <Title level={3} className="profile-name">
                  {profileData?.name || user?.name || '未设置姓名'}
                </Title>
                <Text className="profile-username">
                  {profileData?.username || user?.username}
                </Text>
              </div>
            </div>
          </Card>

          {/* 详细信息卡片 */}
          <Card className="profile-details-card">
            <div className="profile-section">
              <div className="section-header">
                <Title level={4}>基本信息</Title>
              </div>
              
              <div className="info-grid">
                <div className="info-item">
                  <div className="info-label">
                    <UserOutlined className="info-icon" />
                    <span>用户名</span>
                  </div>
                  <div className="info-value">
                    {profileData?.username || user?.username}
                  </div>
                </div>

                <div className="info-item">
                  <div className="info-label">
                    <LockOutlined className="info-icon" />
                    <span>重置密码</span>
                  </div>
                  <div className="info-value">
                    <Button 
                      type="text" 
                      className="edit-button custom-button"
                      onClick={() => setPasswordModalVisible(true)}
                    >
                      编辑
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </div>

        {/* 修改密码模态框 */}
        <Modal
          title="修改密码"
          open={passwordModalVisible}
          onCancel={handlePasswordCancel}
          width={400}
          className="password-modal"
          maskClosable={true}
          footer={[
            <Button key="cancel" className="custom-button" onClick={handlePasswordCancel}>
              取消
            </Button>,
            <Button 
              key="submit"
              htmlType="submit"
              loading={passwordLoading}
              className="save-button"
              onClick={() => passwordForm.submit()}
            >
              确认修改
            </Button>
          ]}
        >
          <Form
            form={passwordForm}
            layout="vertical"
            onFinish={handlePasswordUpdate}
            autoComplete="off"
          >
            <Form.Item
              name="oldPassword"
              label="当前密码"
              rules={[
                { required: true, message: '请输入当前密码' },
                { min: 6, message: '密码至少6个字符' }
              ]}
            >
              <Input.Password
                placeholder="请输入当前密码"
                iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
              />
            </Form.Item>

            <Form.Item
              name="newPassword"
              label="新密码"
              rules={[
                { required: true, message: '请输入新密码' },
                { min: 6, message: '密码至少6个字符' },
                { max: 50, message: '密码不能超过50个字符' }
              ]}
            >
              <Input.Password
                placeholder="请输入新密码"
                iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
              />
            </Form.Item>

            <Form.Item
              name="confirmPassword"
              label="确认新密码"
              dependencies={['newPassword']}
              rules={[
                { required: true, message: '请确认新密码' },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue('newPassword') === value) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error('两次输入的密码不一致'));
                  },
                }),
              ]}
            >
              <Input.Password
                placeholder="请再次输入新密码"
                iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
              />
            </Form.Item>


          </Form>
        </Modal>
      </div>
    </MainLayout>
  );
};

export default Profile;