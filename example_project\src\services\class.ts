import { get } from './api';

// 班级信息接口
export interface ClassInfo {
  id: number;
  name: string;
  pic?: string | null;
  description?: string | null;
  btime: string; // 开始时间
  etime: string; // 结束时间
}

// 班级列表响应接口
export interface ClassListResponse {
  classes: ClassInfo[];
}

/**
 * 获取当前用户所在班级列表
 * @returns Promise<ClassInfo[]> 班级列表
 */
export const getClasses = async (): Promise<ClassInfo[]> => {
  try {
    const response = await get<ClassListResponse>('/class/classes');
    return response.classes;
  } catch (error) {
    console.error('获取班级列表失败:', error);
    throw error;
  }
};