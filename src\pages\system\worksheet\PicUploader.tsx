import React, { useState, useEffect } from 'react';
import { Modal, Upload, message } from 'antd';
import { LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import type { RcFile, UploadProps } from 'antd/es/upload';
import { 
  getPicUploadUrl, 
  deletePicFile, 
  updateExercisePic 
} from '../../../services/system/worksheet';

interface PicUploaderProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  exerciseId: number;
  tenantId: number;
  currentPic?: string;
}

const PicUploader: React.FC<PicUploaderProps> = ({
  visible,
  onCancel,
  onSuccess,
  exerciseId,
  tenantId,
  currentPic
}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [imageUrl, setImageUrl] = useState<string | undefined>(currentPic);

  // 当currentPic或visible变化时，更新imageUrl
  useEffect(() => {
    if (visible) {
      setImageUrl(currentPic);
    }
  }, [currentPic, visible]);

  // 上传前检查文件
  const beforeUpload = (file: RcFile) => {
    // 检查文件类型
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error('只能上传图片文件!');
      return false;
    }

    // 检查文件大小
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error('图片必须小于2MB!');
      return false;
    }

    return true;
  };

  // 自定义上传方法
  const customUpload: UploadProps['customRequest'] = async (options) => {
    const { file, onSuccess: uploadSuccess, onError } = options;

    if (!file) {
      message.error('文件不存在');
      return;
    }

    setLoading(true);

    try {
      const rcFile = file as RcFile;

      // 1. 获取上传URL和图片路径
      const uploadUrlResponse = await getPicUploadUrl(tenantId, rcFile.name);
      const { upload_url, file_path } = uploadUrlResponse;

      // 2. 使用fetch API上传文件到OSS
      const fetchResponse = await fetch(upload_url, {
        method: 'PUT',
        body: file as Blob,
        headers: {
          'Content-Type': (file as RcFile).type,
        },
      });

      if (!fetchResponse.ok) {
        throw new Error('上传失败');
      }

      // 保存老的图片路径（用于删除）
      const oldPicPath = currentPic;

      // 3. 更新练习图片信息
      await updateExercisePic(exerciseId, tenantId, file_path);

      // 4. 更新预览图
      if (file instanceof File || file instanceof Blob) {
        const reader = new FileReader();
        reader.onload = (e) => {
          setImageUrl(e.target?.result as string);
        };
        reader.readAsDataURL(file);
      }

      message.success('图片上传成功');
      uploadSuccess?.(fetchResponse);

      // 上传成功后立即调用成功回调，关闭对话框并刷新列表
      onSuccess();

      // 5. 异步删除老的图片文件（如果存在且不为空），不影响用户体验
      if (oldPicPath) {
        // 异步执行删除操作，不等待结果
        deletePicFile({ file_path: oldPicPath }).catch((deleteError) => {
          console.warn('删除老图片文件失败，但不影响主流程:', deleteError);
          // 删除失败不影响主流程，只记录警告
        });
      }
    } catch {
      message.error('图片上传失败');
      onError?.(new Error('上传失败'));
    } finally {
      setLoading(false);
    }
  };

  // 上传按钮
  const uploadButton = (
    <div>
      {loading ? <LoadingOutlined /> : <PlusOutlined />}
      <div style={{ marginTop: 8 }}>上传</div>
    </div>
  );

  // 处理确认
  const handleOk = () => {
    // 只有在成功上传后才调用成功回调
    if (imageUrl !== currentPic) {
      onSuccess();
    } else {
      onCancel();
    }
  };

  return (
    <Modal
      title="上传作业单图片"
      open={visible}
      onCancel={onCancel}
      onOk={handleOk}
      maskClosable={true}
    >
      <Upload
        name="pic"
        listType="picture-card"
        className="pic-uploader"
        showUploadList={false}
        beforeUpload={beforeUpload}
        customRequest={customUpload}
      >
        {imageUrl ? (
          <img src={imageUrl} alt="作业单图片" style={{ width: '100%' }} />
        ) : (
          uploadButton
        )}
      </Upload>
      <div style={{ marginTop: 16 }}>
        <p>支持JPG、PNG格式，文件大小不超过2MB</p>
      </div>
    </Modal>
  );
};

export default PicUploader; 