import { get, post, put, del } from '../api';

// 练习计划相关类型定义
export interface PlanCreate {
  tenant_id: number;
  name: string;
  pic?: string;
  description?: string;
  notes?: string;
  active?: number;
}

export interface PlanUpdate {
  name?: string;
  pic?: string;
  description?: string;
  notes?: string;
  active?: number;
}

export interface PlanResponse {
  id: number;
  tenant_id: number;
  name: string;
  pic?: string;
  description?: string;
  notes?: string;
  active: number;
  ctime: string;
  mtime: string;
}

export interface PlanListResponse {
  total: number;
  items: PlanResponse[];
}

export interface GetPlansParams {
  tenant_id: number;
  skip?: number;
  limit?: number;
  active?: number;
  sort_by?: string;
  sort_order?: string;
  name?: string;
  description?: string;
  notes?: string;
}

export interface PicUploadURL {
  upload_url: string;
  file_path: string;
  file_url: string;
  expires_in: number;
}

export interface PicDeleteRequest {
  file_path: string;
}

// API 接口调用
export const getPlans = async (params: GetPlansParams): Promise<PlanListResponse> => {
  return get('/sys/plan', params);
};

export const getPlan = async (planId: number): Promise<PlanResponse> => {
  return get(`/sys/plan/${planId}`);
};

export const createPlan = async (data: PlanCreate): Promise<PlanResponse> => {
  return post('/sys/plan', data);
};

export const updatePlan = async (planId: number, data: PlanUpdate): Promise<PlanResponse> => {
  return put(`/sys/plan/${planId}`, data);
};

export const getPicUploadUrl = async (tenant_id: number, file_name: string): Promise<PicUploadURL> => {
  return get('/sys/plan/pic/upload-url', { 
    tenant_id, 
    file_name 
  });
};

export const deletePicFile = async (data: PicDeleteRequest): Promise<void> => {
  return del('/sys/plan/pic', { data });
};

// 计划练习相关类型定义
export interface PlanExerciseCreate {
  tenant_id: number;
  pid: number;
  eid: number;
  depend?: number; // 0：不依赖；1：依赖
  priority?: number;
}

export interface PlanExerciseUpdate {
  depend?: number; // 0：不依赖；1：依赖
  priority?: number;
}

export interface PlanExerciseResponse {
  id: number;
  tenant_id: number;
  pid: number;
  eid: number;
  depend: number; // 0：不依赖；1：依赖
  priority: number;
  title: string; // 练习标题
  type: number; // 练习类型（1：作业单；2：角色扮演）
  pic?: string; // 练习图片URL
  intro?: string; // 练习简介
}

export interface PlanExerciseListResponse {
  total: number;
  items: PlanExerciseResponse[];
}

export interface PlanExerciseOrderItem {
  id: number;
  priority: number;
}

export interface PlanExerciseBatchOrderRequest {
  tenant_id: number;
  plan_id: number;
  plan_exercises: PlanExerciseOrderItem[];
}

export interface PlanExerciseBatchOrderResponse {
  success_count: number;
  total_count: number;
  updated_plan_exercises: PlanExerciseResponse[];
}

// 计划练习API接口
export const getPlanExercises = async (planId: number, tenantId: number): Promise<PlanExerciseListResponse> => {
  return get(`/sys/plan/${planId}/exercises`, { tenant_id: tenantId });
};

export const createPlanExercise = async (planId: number, data: PlanExerciseCreate): Promise<PlanExerciseResponse> => {
  return post(`/sys/plan/${planId}/exercises`, data);
};

export const updatePlanExercise = async (planId: number, planExerciseId: number, data: PlanExerciseUpdate): Promise<PlanExerciseResponse> => {
  return put(`/sys/plan/${planId}/exercises/${planExerciseId}`, data);
};

export const deletePlanExercise = async (planExerciseId: number): Promise<void> => {
  return del(`/sys/plan/exercises/${planExerciseId}`);
};

export const batchUpdatePlanExerciseOrder = async (data: PlanExerciseBatchOrderRequest): Promise<PlanExerciseBatchOrderResponse> => {
  return put('/sys/plan/exercises/batch/order', data);
}; 