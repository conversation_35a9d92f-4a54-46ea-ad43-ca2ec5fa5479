import React, { useState, useEffect, useCallback, useRef } from 'react';
import { 
  Form,
  Input, 
  Button, 
  Space, 
  Breadcrumb, 
  Spin, 
  Row,
  Col,
  Typography,
  theme,
  Tabs,
  message,
  Modal,
  InputNumber,
  Select,
  Tooltip
} from 'antd';
import { 
  HomeOutlined, 
  EditOutlined,
  SaveOutlined,
  CloseOutlined,
  FileTextOutlined,
  InfoCircleOutlined,
  TeamOutlined,
  CompassOutlined,
  BookOutlined
} from '@ant-design/icons';
import { useParams, useNavigate, Link, useBlocker } from 'react-router-dom';
import { 
  getScene, 
  updateScene,
  type SceneResponse
} from '../../../services/system/scene';
import { showError } from '../../../utils/errorHandler';
import SceneBackgroundTab, { type SceneBackgroundTabRef } from './SceneBackgroundTab';
import SceneIntroTab, { type SceneIntroTabRef } from './SceneIntroTab';
import SceneCharactersTab from './SceneCharactersTab';
import SceneGuidesTab from './SceneGuidesTab';
import SceneCuesTab, { type SceneCuesTabRef } from './SceneCuesTab';
import styles from '../../../components/RichTextEditor.module.css';
import '../../../styles/detail-page.css';

const { Title, Text } = Typography;
const { Option } = Select;

const SceneDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [scene, setScene] = useState<SceneResponse | null>(null);
  const [initialLoading, setInitialLoading] = useState(true);
  const { token } = theme.useToken();
  const sceneBackgroundTabRef = useRef<SceneBackgroundTabRef>(null);
  const sceneIntroTabRef = useRef<SceneIntroTabRef>(null);
  const sceneCuesTabRef = useRef<SceneCuesTabRef>(null);
  
  // 内联编辑状态
  const [editingTitle, setEditingTitle] = useState(false);
  const [editingDuration, setEditingDuration] = useState(false);
  const [editingVersion, setEditingVersion] = useState(false);
  const [editingNotes, setEditingNotes] = useState(false);
  const [editingPublished, setEditingPublished] = useState(false);
  const [tempTitle, setTempTitle] = useState('');
  const [tempDuration, setTempDuration] = useState<number | undefined>(undefined);
  const [tempVersion, setTempVersion] = useState('');
  const [tempNotes, setTempNotes] = useState('');
  const [tempPublished, setTempPublished] = useState<number>(0);
  const [activeTab, setActiveTab] = useState('intro');
  
  // 跟踪已访问的tab
  const [visitedTabs, setVisitedTabs] = useState<Set<string>>(new Set(['intro']));
  
  // 确认对话框状态
  const [showUnsavedModal, setShowUnsavedModal] = useState(false);
  const [pendingAction, setPendingAction] = useState<(() => void) | null>(null);

  // 获取场景详情
  const fetchScene = useCallback(async () => {
    if (!id) return;
    
    try {
      setInitialLoading(true);
      const response = await getScene(parseInt(id));
      setScene(response);
      form.setFieldsValue({
        title: response.title,
        intro: response.intro,
        bgtext: response.bgtext,
        duration: response.duration,
        version: response.version,
        notes: response.notes,
        published: response.published,
      });
      setTempTitle(response.title);
      setTempDuration(response.duration);
      setTempVersion(response.version || '');
      setTempNotes(response.notes || '');
      setTempPublished(response.published);
    } catch (error) {
      showError(error, '获取场景详情失败');
      navigate('/system/scene');
    } finally {
      setInitialLoading(false);
    }
  }, [id, navigate]);

  // 保存标题
  const handleSaveTitle = async () => {
    if (!scene || !tempTitle.trim()) return;
    
    try {
      await updateScene(scene.id, { title: tempTitle.trim() });
      setScene({ ...scene, title: tempTitle.trim() });
      setEditingTitle(false);
      message.success('标题更新成功');
    } catch (error) {
      showError(error, '更新标题失败');
    }
  };

  // 保存练习时长
  const handleSaveDuration = async () => {
    if (!scene) return;
    
    try {
      await updateScene(scene.id, { duration: tempDuration });
      setScene({ ...scene, duration: tempDuration });
      setEditingDuration(false);
      message.success('练习时长更新成功');
    } catch (error) {
      showError(error, '更新练习时长失败');
    }
  };

  // 保存版本
  const handleSaveVersion = async () => {
    if (!scene) return;
    
    try {
      await updateScene(scene.id, { version: tempVersion });
      setScene({ ...scene, version: tempVersion });
      setEditingVersion(false);
      message.success('版本更新成功');
    } catch (error) {
      showError(error, '更新版本失败');
    }
  };

  // 保存备注
  const handleSaveNotes = async () => {
    if (!scene) return;
    
    try {
      await updateScene(scene.id, { notes: tempNotes });
      setScene({ ...scene, notes: tempNotes });
      setEditingNotes(false);
      message.success('备注更新成功');
    } catch (error) {
      showError(error, '更新备注失败');
    }
  };

  // 保存发布状态
  const handleSavePublished = async () => {
    if (!scene) return;
    
    try {
      await updateScene(scene.id, { published: tempPublished });
      setScene({ ...scene, published: tempPublished });
      setEditingPublished(false);
      message.success('发布状态更新成功');
    } catch (error) {
      showError(error, '更新发布状态失败');
    }
  };

  // 开始编辑各字段
  const startEditTitle = () => {
    setTempTitle(scene?.title || '');
    setEditingTitle(true);
  };

  const startEditDuration = () => {
    setTempDuration(scene?.duration);
    setEditingDuration(true);
  };

  const startEditVersion = () => {
    setTempVersion(scene?.version || '');
    setEditingVersion(true);
  };

  const startEditNotes = () => {
    setTempNotes(scene?.notes || '');
    setEditingNotes(true);
  };

  const startEditPublished = () => {
    setTempPublished(scene?.published || 0);
    setEditingPublished(true);
  };

  // 取消编辑
  const cancelEditTitle = () => {
    setTempTitle(scene?.title || '');
    setEditingTitle(false);
  };

  const cancelEditDuration = () => {
    setTempDuration(scene?.duration);
    setEditingDuration(false);
  };

  const cancelEditVersion = () => {
    setTempVersion(scene?.version || '');
    setEditingVersion(false);
  };

  const cancelEditNotes = () => {
    setTempNotes(scene?.notes || '');
    setEditingNotes(false);
  };

  const cancelEditPublished = () => {
    setTempPublished(scene?.published || 0);
    setEditingPublished(false);
  };

  // 处理背景介绍保存成功
  const handleBgtextSaveSuccess = useCallback((updatedBgtext: string) => {
    if (scene) {
      setScene({ ...scene, bgtext: updatedBgtext });
    }
  }, [scene]);

  // 处理简介保存成功
  const handleIntroSaveSuccess = useCallback((updatedIntro: string) => {
    if (scene) {
      setScene({ ...scene, intro: updatedIntro });
    }
  }, [scene]);

  // 检查是否有未保存的更改
  const checkUnsavedChanges = () => {
    const hasBackgroundChanges = sceneBackgroundTabRef.current?.hasUnsavedChanges() || false;
    const hasIntroChanges = sceneIntroTabRef.current?.hasUnsavedChanges() || false;
    const hasCuesChanges = sceneCuesTabRef.current?.hasUnsavedChanges() || false;
    return hasBackgroundChanges || hasIntroChanges || hasCuesChanges;
  };

  // 显示确认对话框
  const showConfirmModal = (action: () => void) => {
    if (checkUnsavedChanges()) {
      setPendingAction(() => action);
      setShowUnsavedModal(true);
    } else {
      action();
    }
  };

  // 处理保存并继续
  const handleSaveAndContinue = async () => {
    try {
      // 保存背景介绍内容
      if (sceneBackgroundTabRef.current?.hasUnsavedChanges()) {
        await sceneBackgroundTabRef.current?.saveContent();
      }
      // 保存简介内容
      if (sceneIntroTabRef.current?.hasUnsavedChanges()) {
        await sceneIntroTabRef.current?.saveContent();
      }
      // 保存剧情内容
      if (sceneCuesTabRef.current?.hasUnsavedChanges()) {
        await sceneCuesTabRef.current?.saveContent();
      }
      setShowUnsavedModal(false);
      if (pendingAction) {
        pendingAction();
        setPendingAction(null);
      }
    } catch {
      // 保存失败，不执行待定操作
    }
  };

  // 处理不保存直接继续
  const handleDiscardAndContinue = () => {
    // 恢复到初始内容
    sceneBackgroundTabRef.current?.resetToInitial();
    // 恢复简介内容
    sceneIntroTabRef.current?.resetToInitial();
    // 恢复剧情内容
    sceneCuesTabRef.current?.resetToInitial();
    setShowUnsavedModal(false);
    if (pendingAction) {
      pendingAction();
      setPendingAction(null);
    }
  };

  // 处理取消
  const handleCancel = () => {
    setShowUnsavedModal(false);
    setPendingAction(null);
  };

  // 使用路由阻塞器 - 只在当前页面激活时启用
  const blocker = useBlocker(
    ({ currentLocation, nextLocation }) =>
      currentLocation.pathname !== nextLocation.pathname && 
      currentLocation.pathname.startsWith('/system/scene/') &&
      checkUnsavedChanges()
  );

  // 处理tab切换
  const handleTabChange = async (tabKey: string) => {
    const performTabChange = async () => {
      setActiveTab(tabKey);
      
      if (!visitedTabs.has(tabKey)) {
        setVisitedTabs(prev => new Set(prev).add(tabKey));
      }
    };

    // 检查当前活跃tab是否有未保存的更改
    const hasCurrentTabChanges = () => {
      if (activeTab === 'background') {
        return sceneBackgroundTabRef.current?.hasUnsavedChanges() || false;
      } else if (activeTab === 'intro') {
        return sceneIntroTabRef.current?.hasUnsavedChanges() || false;
      } else if (activeTab === 'cues') {
        return sceneCuesTabRef.current?.hasUnsavedChanges() || false;
      }
      return false;
    };

    if (hasCurrentTabChanges()) {
      showConfirmModal(performTabChange);
    } else {
      performTabChange();
    }
  };

  useEffect(() => {
    fetchScene();
  }, [fetchScene]);

  // 监听页面卸载事件
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (checkUnsavedChanges()) {
        e.preventDefault();
        e.returnValue = '您有未保存的内容，确定要离开吗？';
        return '您有未保存的内容，确定要离开吗？';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  // 处理路由阻塞
  useEffect(() => {
    if (blocker.state === 'blocked') {
      showConfirmModal(() => {
        blocker.proceed();
      });
    }
  }, [blocker]);

  if (initialLoading) {
    return (
      <div style={{ textAlign: 'center', padding: '100px 0' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!scene) {
    return (
      <div style={{ textAlign: 'center', padding: '100px 0' }}>
        <Text type="secondary">场景不存在</Text>
      </div>
    );
  }

  return (
    <div className="detail-page-container">
      {/* 面包屑导航 */}
      <Breadcrumb 
        style={{ marginBottom: 8 }}
        items={[
          {
            title: (
              <Link to="/system/home">
                <HomeOutlined /> 主页
              </Link>
            ),
          },
          {
            title: '租户空间',
          },
          {
            title: (
              <Link to="/system/scene">
                场景管理
              </Link>
            ),
          },
          {
            title: '场景详情',
          },
        ]}
      />

      <div style={{ backgroundColor: token.colorBgContainer, flex: 1, display: 'flex', flexDirection: 'column' }}>
        {/* 头部区域 */}
        <div className="detail-page-header" style={{ borderBottom: `1px solid ${token.colorBorderSecondary}` }}>
          {/* 标题行：标题、创建时间、发布状态 */}
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'flex-end',
            marginBottom: '16px'
          }}>
            {/* 标题 */}
            <div style={{ flex: 1, display: 'flex', alignItems: 'flex-end', gap: '8px' }}>
              {editingTitle ? (
                <div style={{ display: 'flex', alignItems: 'flex-end', gap: '8px' }}>
                  <Input
                    value={tempTitle}
                    onChange={(e) => setTempTitle(e.target.value)}
                    size="large"
                    style={{ fontSize: '20px', fontWeight: 600, minWidth: '300px' }}
                    onPressEnter={handleSaveTitle}
                    onKeyDown={(e) => {
                      if (e.key === 'Escape') {
                        cancelEditTitle();
                      }
                    }}
                    autoFocus
                  />
                  <Space>
                    <Button
                      type="primary"
                      size="small"
                      icon={<SaveOutlined />}
                      onClick={handleSaveTitle}
                    />
                    <Button
                      size="small"
                      icon={<CloseOutlined />}
                      onClick={cancelEditTitle}
                    />
                  </Space>
                </div>
              ) : (
                <div style={{ display: 'flex', alignItems: 'flex-end', gap: '8px' }}>
                  <Title 
                    level={3} 
                    style={{ 
                      margin: 0,
                      cursor: 'pointer'
                    }}
                    onClick={startEditTitle}
                  >
                    {scene?.title || '加载中...'}
                  </Title>
                  <Button
                    type="text"
                    icon={<EditOutlined />}
                    size="small"
                    onClick={startEditTitle}
                    style={{ color: token.colorTextSecondary }}
                  />
                </div>
              )}
            </div>
            
            {/* 右侧：创建时间、发布状态 */}
            <div style={{ 
              display: 'flex', 
              alignItems: 'flex-end',
              gap: '16px'
            }}>
              {/* 创建时间 */}
              <Text 
                type="secondary" 
                style={{ fontSize: '12px', whiteSpace: 'nowrap' }}
              >
                创建于：{scene?.ctime ? new Date(scene.ctime).toLocaleString() : '未知'}
              </Text>
              
              {/* 发布状态 */}
              {editingPublished ? (
                <div style={{ display: 'flex', alignItems: 'flex-end', gap: '4px' }}>
                  <Select
                    value={tempPublished}
                    onChange={setTempPublished}
                    size="small"
                    style={{ width: '80px' }}
                    autoFocus
                  >
                    <Option value={0}>未发布</Option>
                    <Option value={1}>已发布</Option>
                  </Select>
                  <Button
                    type="primary"
                    size="small"
                    icon={<SaveOutlined />}
                    onClick={handleSavePublished}
                  />
                  <Button
                    size="small"
                    icon={<CloseOutlined />}
                    onClick={cancelEditPublished}
                  />
                </div>
              ) : (
                <Button
                  type={scene?.published === 1 ? "primary" : "default"}
                  size="small"
                  onClick={startEditPublished}
                  style={{
                    backgroundColor: scene?.published === 1 ? token.colorPrimary : token.colorBgContainer,
                    borderColor: scene?.published === 1 ? token.colorPrimary : token.colorBorder,
                    color: scene?.published === 1 ? token.colorWhite : token.colorText
                  }}
                >
                  {scene?.published === 1 ? '已发布' : '未发布'}
                </Button>
              )}
            </div>
          </div>

          {/* 字段信息行 */}
          <Row gutter={[16, 12]}>
            {/* 时长 */}
            <Col span={4}>
              <div className="detail-field-row">
                <Text className="detail-field-label" style={{ color: token.colorTextSecondary }}>
                  练习时长：
                </Text>
                {editingDuration ? (
                  <div className="detail-edit-container" style={{ position: 'relative' }}>
                    <InputNumber
                      value={tempDuration}
                      onChange={(value) => setTempDuration(value || undefined)}
                      placeholder="分钟"
                      min={0}
                      style={{
                        width: '60px',
                        borderRadius: '6px',
                        fontSize: '14px',
                        backgroundColor: '#fafafa',
                        border: '1px solid #d9d9d9'
                      }}
                      autoFocus
                      onPressEnter={handleSaveDuration}
                      onKeyDown={(e) => {
                        if (e.key === 'Escape') {
                          cancelEditDuration();
                        }
                      }}
                    />
                    <Button
                      type="primary"
                      size="small"
                      icon={<SaveOutlined />}
                      onClick={handleSaveDuration}
                      style={{ marginLeft: '4px', minWidth: '24px', padding: '0 4px' }}
                    />
                    <Button
                      size="small"
                      icon={<CloseOutlined />}
                      onClick={cancelEditDuration}
                      style={{ marginLeft: '2px', minWidth: '24px', padding: '0 4px' }}
                    />
                  </div>
                ) : (
                  <Tooltip title={scene?.duration ? `${scene.duration} 分钟` : '（暂无）'} placement="top">
                    <Text 
                      className={`detail-field-content ${scene?.duration ? 'has-value' : 'empty'}`}
                      style={{
                        color: scene?.duration ? token.colorText : token.colorTextTertiary,
                      }}
                      onClick={startEditDuration}
                    >
                      {scene?.duration ? `${scene.duration} 分钟` : '（暂无）'}
                    </Text>
                  </Tooltip>
                )}
              </div>
            </Col>

            {/* 版本 */}
            <Col span={6}>
              <div className="detail-field-row">
                <Text className="detail-field-label" style={{ color: token.colorTextSecondary }}>
                  版本：
                </Text>
                {editingVersion ? (
                  <div className="detail-edit-container">
                    <Input
                      value={tempVersion}
                      onChange={(e) => setTempVersion(e.target.value)}
                      placeholder="添加版本..."
                      className="detail-edit-input"
                      autoFocus
                      onPressEnter={handleSaveVersion}
                      onKeyDown={(e) => {
                        if (e.key === 'Escape') {
                          cancelEditVersion();
                        }
                      }}
                    />
                    <Button
                      type="primary"
                      size="small"
                      icon={<SaveOutlined />}
                      onClick={handleSaveVersion}
                    />
                    <Button
                      size="small"
                      icon={<CloseOutlined />}
                      onClick={cancelEditVersion}
                    />
                  </div>
                ) : (
                  <Tooltip title={scene?.version || '（暂无）'} placement="top">
                    <Text 
                      className={`detail-field-content ${scene?.version ? 'has-value' : 'empty'}`}
                      style={{
                        color: scene?.version ? token.colorText : token.colorTextTertiary,
                      }}
                      onClick={startEditVersion}
                    >
                      {scene?.version || '（暂无）'}
                    </Text>
                  </Tooltip>
                )}
              </div>
            </Col>

            {/* 备注 */}
            <Col span={12}>
              <div className="detail-field-row">
                <Text className="detail-field-label" style={{ color: token.colorTextSecondary }}>
                  备注：
                </Text>
                {editingNotes ? (
                  <div className="detail-edit-container">
                    <Input
                      value={tempNotes}
                      onChange={(e) => setTempNotes(e.target.value)}
                      placeholder="添加备注..."
                      className="detail-edit-input"
                      autoFocus
                      onPressEnter={handleSaveNotes}
                      onKeyDown={(e) => {
                        if (e.key === 'Escape') {
                          cancelEditNotes();
                        }
                      }}
                    />
                    <Button
                      type="primary"
                      size="small"
                      icon={<SaveOutlined />}
                      onClick={handleSaveNotes}
                    />
                    <Button
                      size="small"
                      icon={<CloseOutlined />}
                      onClick={cancelEditNotes}
                    />
                  </div>
                ) : (
                  <Tooltip title={scene?.notes || '（暂无）'} placement="top">
                    <Text 
                      className={`detail-field-content ${scene?.notes ? 'has-value' : 'empty'}`}
                      style={{
                        color: scene?.notes ? token.colorText : token.colorTextTertiary,
                      }}
                      onClick={startEditNotes}
                    >
                      {scene?.notes || '（暂无）'}
                    </Text>
                  </Tooltip>
                )}
              </div>
            </Col>
          </Row>
        </div>

        {/* 主体内容区域 */}
        <div className="detail-page-content">
          <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
            {/* 标签页 */}
            <div style={{ 
              flex: 1,
              display: 'flex', 
              flexDirection: 'column' 
            }}>
              <Tabs
                activeKey={activeTab}
                onChange={handleTabChange}
                destroyOnHidden={true}
                style={{ 
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column'
                }}
                className={styles.fullHeightTabs}
                items={[
                  {
                    key: 'intro',
                    label: (
                      <span style={{ fontSize: '16px' }}>
                        <InfoCircleOutlined style={{ marginRight: '8px', fontSize: '18px' }} />
                        简介
                      </span>
                    ),
                    children: <SceneIntroTab 
                      ref={sceneIntroTabRef}
                      form={form} 
                      sceneId={scene?.id} 
                      initialIntro={scene?.intro || ''}
                      onSaveSuccess={handleIntroSaveSuccess}
                    />,
                  },
                  {
                    key: 'background',
                    label: (
                      <span style={{ fontSize: '16px' }}>
                        <FileTextOutlined style={{ marginRight: '8px', fontSize: '18px' }} />
                        背景介绍
                      </span>
                    ),
                    children: <SceneBackgroundTab 
                      ref={sceneBackgroundTabRef}
                      form={form} 
                      sceneId={scene?.id} 
                      initialBgtext={scene?.bgtext || ''}
                      onSaveSuccess={handleBgtextSaveSuccess}
                    />,
                  },
                  {
                    key: 'characters',
                    label: (
                      <span style={{ fontSize: '16px' }}>
                        <TeamOutlined style={{ marginRight: '8px', fontSize: '18px' }} />
                        人物角色
                      </span>
                    ),
                    children: <SceneCharactersTab 
                      sceneId={scene?.id?.toString()}
                    />,
                  },
                  {
                    key: 'cues',
                    label: (
                      <span style={{ fontSize: '16px' }}>
                        <BookOutlined style={{ marginRight: '8px', fontSize: '18px' }} />
                        剧情设定
                      </span>
                    ),
                    children: <SceneCuesTab 
                      ref={sceneCuesTabRef}
                      sceneId={scene?.id?.toString()}
                    />,
                  },
                  {
                    key: 'guides',
                    label: (
                      <span style={{ fontSize: '16px' }}>
                        <CompassOutlined style={{ marginRight: '8px', fontSize: '18px' }} />
                        答题指南
                      </span>
                    ),
                    children: <SceneGuidesTab 
                      sceneId={scene?.id?.toString()}
                    />,
                  },
                ]}
              />
            </div>
          </div>
        </div>
      </div>

      {/* 未保存更改确认对话框 */}
      <Modal
        title="未保存的更改"
        open={showUnsavedModal}
        onCancel={handleCancel}
        footer={[
          <Button key="cancel" onClick={handleCancel}>
            取消
          </Button>,
          <Button key="discard" onClick={handleDiscardAndContinue}>
            不保存
          </Button>,
          <Button key="save" type="primary" onClick={handleSaveAndContinue}>
            保存并继续
          </Button>,
        ]}
        closable={false}
        maskClosable={false}
      >
        <p>您有未保存的内容更改，是否要保存？</p>
      </Modal>
    </div>
  );
};

export default SceneDetail; 