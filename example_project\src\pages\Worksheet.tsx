import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Typography, Space, Button, App, Modal, Spin, Empty, Tag, Layout, Menu, Input, Tooltip, Collapse, Avatar } from 'antd';
import { useParams, useNavigate } from 'react-router-dom';
import { getWorksheetData, getUnitQuestions, getQuestionComment, retryQuestion, saveSingleQuestionDraft, batchUpdateQuestionDrafts } from '../services/worksheet';
import { getTeacherInfo, getTeacherAvatar, updateExerciseLogStatus } from '../services/exercise';
import type { WorksheetBasicResponse, WorksheetUnitsResponse, UnitResponse, QuestionResponse, QuestionCommentRequest, QuestionRetryRequest, QuestionDraftBatchRequest } from '../services/worksheet';
import StreamingMarkdownRenderer from '../components/StreamingMarkdownRenderer';
import MarkdownRenderer from '../components/MarkdownRenderer';
import GuideDrawer from '../components/GuideDrawer';
import TargetDrawer from '../components/TargetDrawer';
import { LOCAL_STORAGE_PREFIXES, setStorageItem, getStorageItem, removeStorageItem } from '../utils/localStorage';
import {
  RollbackOutlined,
  ClockCircleOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  InfoCircleFilled,
  InfoCircleOutlined,
  TagOutlined,
  CompassOutlined,
  UserOutlined,
  RedoOutlined,
  UpOutlined,
  SaveOutlined
} from '@ant-design/icons';
import { MainLayout } from '../layouts';
import '../styles/Worksheet.css';

const { Title, Text } = Typography;
const { Sider, Content } = Layout;

const Worksheet: React.FC = () => {
  const { exerciseId, classId } = useParams<{
    exerciseId: string;
    classId: string;
  }>();
  const navigate = useNavigate();
  const { message, modal } = App.useApp();
  const [worksheetBasic, setWorksheetBasic] = useState<WorksheetBasicResponse | null>(null);
  const [worksheetUnits, setWorksheetUnits] = useState<WorksheetUnitsResponse | null>(null);
  const [selectedUnit, setSelectedUnit] = useState<UnitResponse | null>(null);
  const [unitQuestions, setUnitQuestions] = useState<QuestionResponse[]>([]);
  const [loading, setLoading] = useState(false);
  const [questionsLoading, setQuestionsLoading] = useState(false);
  const [collapsed, setCollapsed] = useState(false);

  const [targetDrawerVisible, setTargetDrawerVisible] = useState(false);
  const [guideDrawerVisible, setGuideDrawerVisible] = useState(false);
  const [selectedQuestion, setSelectedQuestion] = useState<QuestionResponse | null>(null);
  const [answerTexts, setAnswerTexts] = useState<{[key: string]: string}>({});
  const [showBackground, setShowBackground] = useState(false);
  const [streamingComments, setStreamingComments] = useState<{[key: string]: string}>({});
  const [commentingQuestions, setCommentingQuestions] = useState<Set<string>>(new Set());
  const [savingDrafts, setSavingDrafts] = useState<Set<string>>(new Set());
  const [originalDrafts, setOriginalDrafts] = useState<{[key: string]: string}>({});
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const textareaRefs = useRef<{[key: string]: any}>({});

  // 生成状态存储的key
  const getStateKey = useCallback(() => {
    if (!classId || !exerciseId) return null;
    return `${LOCAL_STORAGE_PREFIXES.WORKSHEET_STATE}${classId}_${exerciseId}`;
  }, [classId, exerciseId]);

  // 检查是否有未保存的更改
  const checkUnsavedChanges = useCallback(() => {
    if (!selectedUnit) return false;
    
    for (let i = 0; i < unitQuestions.length; i++) {
      const questionKey = `${selectedUnit.id}-${i}`;
      const currentText = answerTexts[questionKey] || '';
      const originalText = originalDrafts[questionKey] || '';
      if (currentText !== originalText) {
        return true;
      }
    }
    return false;
  }, [selectedUnit, unitQuestions, answerTexts, originalDrafts]);

  // 更新未保存更改状态
  useEffect(() => {
    const hasChanges = checkUnsavedChanges();
    setHasUnsavedChanges(hasChanges);
  }, [checkUnsavedChanges]);

  // 处理批量保存草稿
  const handleBatchSaveDrafts = useCallback(async () => {
    if (!worksheetBasic?.elid || !selectedUnit) {
      message.error('缺少必要信息，无法保存草稿');
      return;
    }

    const draftsToSave: { qid: number; draft: string }[] = [];
    const updatedOriginalDrafts: {[key: string]: string} = { ...originalDrafts };
    
    // 收集所有有变化的草稿
    for (let i = 0; i < unitQuestions.length; i++) {
      const questionKey = `${selectedUnit.id}-${i}`;
      const currentText = answerTexts[questionKey] || '';
      const originalText = originalDrafts[questionKey] || '';
      
      if (currentText !== originalText) {
        draftsToSave.push({
          qid: unitQuestions[i].id,
          draft: currentText
        });
        updatedOriginalDrafts[questionKey] = currentText;
      }
    }
    
    if (draftsToSave.length === 0) {
      message.info('没有需要保存的草稿');
      return;
    }

    try {
      const request: QuestionDraftBatchRequest = {
        elid: worksheetBasic.elid,
        list: draftsToSave
      };
      
      await batchUpdateQuestionDrafts(request);
      
      // 更新原始草稿状态
      setOriginalDrafts(updatedOriginalDrafts);
      
      // 更新问题列表中的draft
      setUnitQuestions(prev => prev.map(q => {
        const draftItem = draftsToSave.find(d => d.qid === q.id);
        return draftItem ? { ...q, draft: draftItem.draft } : q;
      }));
      
      message.success(`成功保存 ${draftsToSave.length} 个问题的草稿`);
    } catch (error) {
      console.error('批量保存草稿失败:', error);
      message.error('批量保存草稿失败，请重试');
    }
  }, [worksheetBasic?.elid, selectedUnit, originalDrafts, unitQuestions, answerTexts, message]);

  // 处理提交作业
  const handleSubmitExercise = useCallback(async () => {
    if (!worksheetBasic?.elid) {
      message.error('缺少必要信息，无法提交作业');
      return;
    }

    try {
      setSubmitting(true);
      
      await updateExerciseLogStatus(worksheetBasic.elid, 2);
      
      // 更新本地状态
      setWorksheetBasic(prev => prev ? {
        ...prev,
        status: 2,
        stime: new Date().toISOString()
      } : null);
      
      message.success('作业提交成功');
    } catch (error) {
      console.error('提交作业失败:', error);
      message.error('提交作业失败，请重试');
    } finally {
      setSubmitting(false);
    }
  }, [worksheetBasic?.elid, message]);

  // 处理撤销提交
  const handleCancelSubmit = useCallback(async () => {
    if (!worksheetBasic?.elid) {
      message.error('缺少必要信息，无法撤销提交');
      return;
    }

    try {
      setSubmitting(true);
      
      await updateExerciseLogStatus(worksheetBasic.elid, 1);
      
      // 更新本地状态
      setWorksheetBasic(prev => prev ? {
        ...prev,
        status: 1,
        stime: null
      } : null);
      
      message.success('撤销提交成功');
    } catch (error) {
      console.error('撤销提交失败:', error);
      message.error('撤销提交失败，请重试');
    } finally {
      setSubmitting(false);
    }
  }, [worksheetBasic?.elid, message]);

  // 页面离开提醒
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        e.preventDefault();
        return '您有作答未保存，需要在继续操作前保存草稿吗？';
      }
    };

    const handlePopState = () => {
      if (hasUnsavedChanges) {
        // 阻止默认的后退行为
        window.history.pushState(null, '', window.location.href);
        
        modal.confirm({
          title: '保存确认',
          content: <div style={{ padding: '24px 0' }}>您有作答未保存，需要在继续操作前保存草稿吗？</div>,
          footer: [
            <div key="footer" style={{ display: 'flex', justifyContent: 'flex-end', gap: '8px', marginTop: "16px"}}>
               <Button className="custom-button" onClick={() => {
                 Modal.destroyAll();
               }}>
                 取消
               </Button>
               <Button className="custom-button" onClick={() => {
                 // 恢复textarea内容为draft
                 if (selectedUnit && unitQuestions.length > 0) {
                   setAnswerTexts(prev => {
                     const newAnswerTexts = { ...prev };
                     unitQuestions.forEach((_question, index) => {
                       const questionKey = `${selectedUnit.id}-${index}`;
                       newAnswerTexts[questionKey] = originalDrafts[questionKey] || '';
                     });
                     return newAnswerTexts;
                   });
                 }
                 Modal.destroyAll();
                 // 不保存，直接后退
                 window.history.back();
               }}>
                 不保存
               </Button>
              <Button 
                className="save-button"
                onClick={async () => {
                  try {
                    await handleBatchSaveDrafts();
                    Modal.destroyAll();
                    // 保存成功后执行后退
                    window.history.back();
                  } catch (error) {
                    console.error('浏览器后退前保存草稿失败:', error);
                    message.error('保存草稿失败，请重试');
                  }
                }}
              >
                保存
              </Button>
            </div>
          ],
          keyboard: true,
        maskClosable: true
        });
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    window.addEventListener('popstate', handlePopState);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('popstate', handlePopState);
    };
  }, [hasUnsavedChanges, handleBatchSaveDrafts, modal, message]);



  // 保存状态到localStorage
  const saveState = useCallback((unitId: number | null, showBg: boolean) => {
    const key = getStateKey();
    if (!key) return;
    
    const state = {
      selectedUnitId: unitId,
      showBackground: showBg,
      timestamp: Date.now()
    };
    
    setStorageItem(key, JSON.stringify(state));
  }, [getStateKey]);



  // 获取单元问题
  const fetchUnitQuestions = useCallback(async (worksheetId: number, unitId: number, elid: number) => {
    try {
      setQuestionsLoading(true);
      const data = await getUnitQuestions(worksheetId, unitId, elid);
      setUnitQuestions(data.question_list);
      
      // 初始化answerTexts和originalDrafts，确保用户输入的内容不会丢失
      setAnswerTexts(prev => {
        const newAnswerTexts = { ...prev };
        data.question_list.forEach((question, index) => {
          const questionKey = `${unitId}-${index}`;
          // 只有当answerTexts中没有该问题的内容时，才使用draft初始化
          if (!newAnswerTexts[questionKey] && question.draft) {
            newAnswerTexts[questionKey] = question.draft;
          }
        });
        return newAnswerTexts;
      });
      
      // 初始化原始草稿状态
      setOriginalDrafts(prev => {
        const newOriginalDrafts = { ...prev };
        data.question_list.forEach((question, index) => {
          const questionKey = `${unitId}-${index}`;
          newOriginalDrafts[questionKey] = question.draft || '';
        });
        return newOriginalDrafts;
      });
    } catch (error) {
      console.error('获取单元问题失败:', error);
      message.error('获取单元问题失败');
    } finally {
      setQuestionsLoading(false);
    }
  }, [message]);

  // 获取作业单数据（并行调用）
  const fetchWorksheetData = useCallback(async () => {
    if (!exerciseId || !classId) return;

    try {
      setLoading(true);
      // 并行调用两个API获取基本信息和单元列表
      const { basic, units } = await getWorksheetData(
        Number(exerciseId),
        Number(classId)
      );
      
      setWorksheetBasic(basic);
      setWorksheetUnits(units);
      
      // 数据加载完成后，尝试恢复之前的状态
      // 注意：这里需要在下一个事件循环中执行，确保worksheetUnits状态已更新
      setTimeout(() => {
        const key = getStateKey();
        if (!key) {
          // 如果无法生成key，使用默认状态
          setSelectedUnit(null);
          setShowBackground(true);
          return;
        }
        
        try {
          const savedState = getStorageItem(key);
          if (!savedState) {
            // 没有保存的状态，使用默认状态
            setSelectedUnit(null);
            setShowBackground(true);
            return;
          }
          
          const state = JSON.parse(savedState);
          
          // 检查状态是否过期（24小时）
          if (Date.now() - state.timestamp > 24 * 60 * 60 * 1000) {
            removeStorageItem(key);
            setSelectedUnit(null);
            setShowBackground(true);
            return;
          }
          
          // 如果保存的是显示背景状态
          if (state.showBackground) {
            setSelectedUnit(null);
            setShowBackground(true);
            return;
          }
          
          // 如果保存的是选中的单元，验证单元是否还存在
          if (state.selectedUnitId && units.unit_list) {
            const unit = units.unit_list.find(u => u.id === state.selectedUnitId);
            if (unit) {
              setSelectedUnit(unit);
              setShowBackground(false);
              // 自动加载该单元的问题
              fetchUnitQuestions(Number(exerciseId), unit.id, basic.elid || 0);
              return;
            }
          }
          
          // 如果恢复失败，使用默认状态
          setSelectedUnit(null);
          setShowBackground(true);
        } catch (error) {
          console.warn('恢复状态失败:', error);
          setSelectedUnit(null);
          setShowBackground(true);
        }
      }, 0);
      
    } catch (error) {
      console.error('获取作业单数据失败:', error);
      message.error('获取作业单数据失败');
    } finally {
      setLoading(false);
    }
  }, [exerciseId, classId, message, getStateKey, fetchUnitQuestions]);

  // 处理单元选择
  const handleUnitSelect = async (unit: UnitResponse) => {
    // 如果有未保存的草稿，询问用户是否保存
    if (hasUnsavedChanges) {
      modal.confirm({
         title: '保存确认',
         content: <div style={{ marginBottom: '8px' }}>您有作答未保存，需要在继续操作前保存草稿吗？</div>,
         footer: [
           <div key="footer" style={{ display: 'flex', justifyContent: 'flex-end', gap: '8px' }}>
             <Button className="custom-button" onClick={() => {
               Modal.destroyAll();
             }}>
               取消
             </Button>
             <Button className="custom-button" onClick={() => {
               // 恢复textarea内容为draft
               if (selectedUnit && unitQuestions.length > 0) {
                 setAnswerTexts(prev => {
                   const newAnswerTexts = { ...prev };
                   unitQuestions.forEach((_question, index) => {
                     const questionKey = `${selectedUnit.id}-${index}`;
                     newAnswerTexts[questionKey] = originalDrafts[questionKey] || '';
                   });
                   return newAnswerTexts;
                 });
               }
               Modal.destroyAll();
               // 不保存，直接继续操作
               setSelectedUnit(unit);
               setShowBackground(false);
               saveState(unit.id, false);
               if (worksheetBasic) {
                 fetchUnitQuestions(Number(exerciseId), unit.id, worksheetBasic.elid || 0);
               }
             }}>
               不保存
             </Button>
             <Button 
               className="save-button"
               onClick={async () => {
                 try {
                   await handleBatchSaveDrafts();
                   Modal.destroyAll();
                   // 保存成功后继续操作
                   setSelectedUnit(unit);
                   setShowBackground(false);
                   saveState(unit.id, false);
                   if (worksheetBasic) {
                     fetchUnitQuestions(Number(exerciseId), unit.id, worksheetBasic.elid || 0);
                   }
                 } catch (error) {
                   console.error('切换单元前保存草稿失败:', error);
                   message.error('保存草稿失败，请重试');
                 }
               }}
             >
               保存
             </Button>
           </div>
         ],
         keyboard: true,
         maskClosable: true
       });
      return;
    }
    
    setSelectedUnit(unit);
    setShowBackground(false);
    // 保存状态到localStorage
    saveState(unit.id, false);
    if (worksheetBasic) {
      fetchUnitQuestions(Number(exerciseId), unit.id, worksheetBasic.elid || 0);
    }
  };

  // 处理显示背景信息
  const handleShowBackground = () => {
    // 如果有未保存的草稿，询问用户是否保存
    if (hasUnsavedChanges) {
      modal.confirm({
        title: '保存确认',
        content: <div style={{ marginBottom: '8px' }}>您有作答未保存，需要在继续操作前保存草稿吗？</div>,
        footer: [
          <div key="footer" style={{ display: 'flex', justifyContent: 'flex-end', gap: '8px' }}>
            <Button className="custom-button" onClick={() => {
              Modal.destroyAll();
            }}>
              取消
            </Button>
            <Button className="custom-button" onClick={() => {
              // 恢复textarea内容为draft
              if (selectedUnit && unitQuestions.length > 0) {
                setAnswerTexts(prev => {
                  const newAnswerTexts = { ...prev };
                  unitQuestions.forEach((_question, index) => {
                    const questionKey = `${selectedUnit.id}-${index}`;
                    newAnswerTexts[questionKey] = originalDrafts[questionKey] || '';
                  });
                  return newAnswerTexts;
                });
              }
              Modal.destroyAll();
              // 不保存，直接继续操作，清除选中的单元
              setSelectedUnit(null);
              setShowBackground(true);
              saveState(null, true);
            }}>
              不保存
            </Button>
            <Button 
              className="save-button"
              onClick={async () => {
                try {
                  await handleBatchSaveDrafts();
                  Modal.destroyAll();
                  // 保存成功后继续操作，清除选中的单元
                  setSelectedUnit(null);
                  setShowBackground(true);
                  saveState(null, true);
                } catch (error) {
                  console.error('切换背景前保存草稿失败:', error);
                  message.error('保存草稿失败，请重试');
                }
              }}
            >
              保存
            </Button>
          </div>
        ],
        keyboard: true,
        maskClosable: true
      });
      return;
    }
    
    setSelectedUnit(null);
    setShowBackground(true);
    // 保存状态到localStorage
    saveState(null, true);
  };

  // 获取老师信息（从本地存储）
  const getTeacherInfoFromStorage = useCallback(() => {
    if (worksheetBasic?.tid) {
      return getTeacherInfo(worksheetBasic.tid);
    }
    return null;
  }, [worksheetBasic?.tid]);

  // 处理练习目标抽屉
  const handleShowTarget = (question: QuestionResponse) => {
    setSelectedQuestion(question);
    setTargetDrawerVisible(true);
  };

  // 处理练习指南抽屉
  const handleShowGuide = (question: QuestionResponse) => {
    setSelectedQuestion(question);
    setGuideDrawerVisible(true);
  };

  // 处理AI老师点评
  const handleAIComment = async (question: QuestionResponse, questionIndex: number) => {
    const questionKey = `${selectedUnit?.id}-${questionIndex}`;
    const answerText = answerTexts[questionKey] || question.draft || '';
    
    if (!answerText.trim()) {
      message.warning('请完成作答后再进行点评！');
      return;
    }

    if (!worksheetBasic || !selectedUnit) {
      message.error('缺少必要信息，无法进行点评');
      return;
    }

    try {
      setCommentingQuestions(prev => new Set(prev).add(questionKey));
      setStreamingComments(prev => ({ ...prev, [questionKey]: '' }));

      const request: QuestionCommentRequest = {
        elid: worksheetBasic.elid || 0,
        qid: question.id,
        answer: answerText
      };

      // 用于存储完整的流式内容
      let fullStreamContent = '';

      await getQuestionComment(request, {
        onChunk: (chunk: string) => {
          // 解析SSE数据格式
          try {
            // 移除 'data: ' 前缀
            const cleanChunk = chunk.replace(/^data: /, '').trim();
            
            // 跳过空行和 [DONE] 标记
            if (!cleanChunk || cleanChunk === '[DONE]') {
              return;
            }
            
            // 解析JSON数据
            const data = JSON.parse(cleanChunk);
            
            // 提取实际的内容
            if (data && data.choices && data.choices[0] && data.choices[0].delta && data.choices[0].delta.content) {
              const content = data.choices[0].delta.content;
              fullStreamContent += content;
              setStreamingComments(prev => ({
                ...prev,
                [questionKey]: fullStreamContent
              }));
            }
          } catch (error) {
            // 如果解析失败，可能是非JSON格式的数据，直接累加
            console.warn('解析流式数据失败，使用原始数据:', error);
            fullStreamContent += chunk;
            setStreamingComments(prev => ({
              ...prev,
              [questionKey]: fullStreamContent
            }));
          }
        },
        onComplete: () => {
          // 使用本地变量中的完整内容更新问题列表
          setUnitQuestions(prev => prev.map(q => 
            q.id === question.id 
              ? { ...q, comment: fullStreamContent, answer: answerText }
              : q
          ));
          // 清理流式状态，但保留内容用于最终显示
          setTimeout(() => {
            setStreamingComments(prev => {
              const newState = { ...prev };
              delete newState[questionKey];
              return newState;
            });
          }, 100); // 短暂延迟确保UI更新完成
        },
        onError: (error: Error) => {
          console.error('AI老师点评失败:', error);
          message.error('AI老师点评失败，请重试');
          setStreamingComments(prev => {
            const newState = { ...prev };
            delete newState[questionKey];
            return newState;
          });
        }
      });

    } catch (error) {
      console.error('AI老师点评失败:', error);
      message.error('AI老师点评失败，请重试');
      setStreamingComments(prev => {
        const newState = { ...prev };
        delete newState[questionKey];
        return newState;
      });
    } finally {
      setCommentingQuestions(prev => {
        const newSet = new Set(prev);
        newSet.delete(questionKey);
        return newSet;
      });
    }
  };

  // 处理保存单个问题草稿
  const handleSaveDraft = async (question: QuestionResponse, questionIndex: number) => {
    const questionKey = `${selectedUnit?.id}-${questionIndex}`;
    const draftText = answerTexts[questionKey] || '';
    
    if (!worksheetBasic?.elid || !selectedUnit) {
      message.error('缺少必要信息，无法保存草稿');
      return;
    }

    try {
      setSavingDrafts(prev => new Set(prev).add(questionKey));
      
      await saveSingleQuestionDraft(worksheetBasic.elid, question.id, draftText);
      
      // 更新原始草稿状态
      setOriginalDrafts(prev => ({
        ...prev,
        [questionKey]: draftText
      }));
      
      // 更新问题列表中的draft
      setUnitQuestions(prev => prev.map(q => 
        q.id === question.id 
          ? { ...q, draft: draftText }
          : q
      ));
      
      message.success('草稿保存成功');
    } catch (error) {
      console.error('保存草稿失败:', error);
      message.error('保存草稿失败，请重试');
    } finally {
      setSavingDrafts(prev => {
        const newSet = new Set(prev);
        newSet.delete(questionKey);
        return newSet;
      });
    }
  };



  // 处理重新练习
  const handleRetry = async (question: QuestionResponse, questionIndex: number) => {
    if (!worksheetBasic?.elid || !selectedUnit) {
      message.error('缺少必要信息，无法重新练习');
      return;
    }

    try {
      const request: QuestionRetryRequest = {
        elid: worksheetBasic.elid,
        qid: question.id
      };

      await retryQuestion(request);
      
      // 更新本地状态：将answer复制到draft，清空answer和comment
      const questionKey = `${selectedUnit.id}-${questionIndex}`;
      setAnswerTexts(prev => ({
        ...prev,
        [questionKey]: question.answer || ''
      }));
      
      // 更新原始草稿状态
      setOriginalDrafts(prev => ({
        ...prev,
        [questionKey]: question.answer || ''
      }));
      
      setUnitQuestions(prev => prev.map(q => 
        q.id === question.id 
          ? { ...q, draft: question.answer || '', answer: null, comment: null }
          : q
      ));
      
      // 清理流式状态
      setStreamingComments(prev => {
        const newState = { ...prev };
        delete newState[questionKey];
        return newState;
      });
      
      // 聚焦到对应的textarea
      setTimeout(() => {
        const textarea = textareaRefs.current[questionKey];
        if (textarea && textarea.focus) {
          textarea.focus();
        }
      }, 100);
      
      message.success('您可以重新作答了');
    } catch (error) {
      console.error('重新练习失败:', error);
      message.error('重新练习失败，请重试');
    }
  };

  useEffect(() => {
    fetchWorksheetData();
  }, [fetchWorksheetData]);

  // 获取状态标签
  const getStatusTag = (status: number) => {
    switch (status) {
      case 0:
        return <Tag color="#8c8c8c" className="status-tag status-tag-pending">待练习</Tag>;
      case 1:
        return <Tag color="#21808d" className="status-tag status-tag-in-progress">练习中</Tag>;
      case 2:
        return <Tag color="#13343b" className="status-tag status-tag-submitted">已提交</Tag>;
      default:
        return <Tag color="#8c8c8c" className="status-tag status-tag-pending">未开始</Tag>;
    }
  };

  // 渲染老师信息
  const renderTeacherInfo = () => {
    const teacherInfo = getTeacherInfoFromStorage();
    if (!teacherInfo && !worksheetBasic?.tname) return null;
    
    const name = teacherInfo?.tname || worksheetBasic?.tname;
    const avatarSrc = worksheetBasic?.tid ? getTeacherAvatar(worksheetBasic.tid) : null;
    
    return (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        {avatarSrc ? (
          <img 
            src={avatarSrc} 
            alt="老师头像" 
            className="teacher-avatar"
            onError={(e) => {
              // 头像加载失败时显示默认图标
              const target = e.target as HTMLImageElement;
              target.style.display = 'none';
              const icon = target.nextElementSibling as HTMLElement;
              if (icon) icon.style.display = 'inline';
            }}
          />
        ) : null}
        <UserOutlined 
          className="teacher-icon"
          style={{ display: avatarSrc ? 'none' : 'inline' }}
        />
        <Text type="secondary" className="teacher-text">指导老师: {name}</Text>
      </div>
    );
  };

  // 渲染问题列表
  const renderQuestions = () => {
    if (!unitQuestions || unitQuestions.length === 0) {
      return <Empty description="暂无问题" />;
    }

    // 获取当前单元的序号
    const unitIndex = worksheetUnits?.unit_list?.findIndex(unit => unit.id === selectedUnit?.id) ?? -1;
    const unitNumber = unitIndex + 1;

    return (
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        {unitQuestions.map((question, index) => (
          <Collapse 
            key={index} 
            defaultActiveKey={['1']} 
            className="question-collapse"
            collapsible="icon"
            expandIcon={({ isActive }) => (
              <Button 
                type="text" 
                icon={<UpOutlined rotate={isActive ? 0 : 180} />}
                style={{
                  width: '32px',
                  height: '32px',
                  borderRadius: '8px',
                  background: 'transparent',
                  boxShadow: 'none',
                  border: 'none',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  outline: 'none',
                  transition: 'all 0.2s ease',
                  fontSize: '16px'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.background = '#f5f5f5';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.background = 'transparent';
                }}
              />
            )}
            items={[
              {
                key: '1',
                label: (
                  <div className="question-header-content">
                    <Title level={4} className="question-title">
                      <span className="question-number">{unitNumber}.{index + 1}</span>
                      {question.title}
                    </Title>
                    <Space>
                      <Button 
                        type="text" 
                        icon={<TagOutlined />}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleShowTarget(question);
                        }}
                        className="question-action-button"
                      >
                        练习目标
                      </Button>
                      <Button 
                        type="text" 
                        icon={<CompassOutlined />}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleShowGuide(question);
                        }}
                        className="question-action-button"
                      >
                        练习指南
                      </Button>

                    </Space>
                  </div>
                ),
                children: (
                  <div className="question-panel">
                    {/* 问题背景 */}
                    {question.bgtext && (
                      <div style={{ marginTop: 16, marginBottom: 16}}>
                        <div className="question-background unified-background">
                          <div dangerouslySetInnerHTML={{ __html: question.bgtext }} />
                        </div>
                      </div>
                    )}

                    {/* 答题区域 */}
                    <div className="answer-area">
                    {(() => {
                      const questionKey = `${selectedUnit?.id}-${index}`;
                      const hasComment = question.comment || streamingComments[questionKey];
                      const isCommenting = commentingQuestions.has(questionKey);
                      
                      if (hasComment || isCommenting) {
                        return (
                          // 已有点评或正在点评的情况 - 显示对话气泡
                          <div>
                            {/* 学员作答 */}
                            <div className="chat-bubble-student">
                              <div className="chat-bubble-student-content">
                                <div className="chat-bubble-student-message">
                                  {/* 使用 MarkdownRenderer 组件，确保样式一致性 */}
                                  <MarkdownRenderer 
                                    content={question.answer || answerTexts[questionKey] || '暂无作答内容'}
                                  />
                                </div>
                                <div className="chat-bubble-student-label">
                                  学员作答
                                  <Avatar 
                                    icon={<UserOutlined />}
                                    size={20}
                                    style={{ 
                                      backgroundColor: 'rgb(19, 52, 59)',
                                      marginLeft: '4px'
                                    }}
                                  />
                                </div>
                              </div>
                            </div>
                            
                            {/* AI老师点评 */}
                            <div className="chat-bubble-ai">
                              <div className="chat-bubble-ai-content">
                                <div className="chat-bubble-ai-message">
                                  {isCommenting ? (
                                    <StreamingMarkdownRenderer 
                                      content={streamingComments[questionKey] || ''}
                                      isStreaming={true}
                                    />
                                  ) : (
                                    // 使用 MarkdownRenderer 组件，确保样式一致性
                                    <MarkdownRenderer 
                                      content={question.comment || ''}
                                    />
                                  )}
                                </div>
                                <div className="chat-bubble-ai-label">
                                  {worksheetBasic?.tid && getTeacherAvatar(worksheetBasic.tid) ? (
                                    <Avatar 
                                      src={getTeacherAvatar(worksheetBasic.tid)} 
                                      size={20}
                                      className="chat-bubble-ai-icon"
                                    />
                                  ) : (
                                    <UserOutlined className="chat-bubble-ai-icon" />
                                  )}
                                  AI老师点评
                                </div>
                              </div>
                            </div>
                            
                            {/* 重新练习按钮 */}
                            {!isCommenting && (
                              <div className="retry-actions">
                                <Button 
                                  type="primary" 
                                  icon={<RedoOutlined />}
                                  onClick={() => handleRetry(question, index)}
                                  className="retry-button"
                                  disabled={worksheetBasic?.status === 2}
                                >
                                  重新练习
                                </Button>
                              </div>
                            )}
                          </div>
                        );
                      }
                      
                      return (
                        // 未点评的情况 - 显示作答框
                        <div style={{ position: 'relative' }}>
                          <Input.TextArea
                            ref={(ref) => {
                              if (ref) {
                                textareaRefs.current[questionKey] = ref;
                              }
                            }}
                            autoSize={{ minRows: 4 }}
                            placeholder="请在此作答..."
                            value={answerTexts[questionKey] || (answerTexts[questionKey] === '' ? '' : question.draft) || ''}
                            onChange={(e) => {
                              setAnswerTexts(prev => ({
                                ...prev,
                                [questionKey]: e.target.value
                              }));
                            }}
                            className="answer-textarea"
                            disabled={worksheetBasic?.status === 2}
                          />
                          <div style={{
                            position: 'absolute',
                            bottom: '28px',
                            right: '12px',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '8px',
                            zIndex: 10
                          }}>
                            <Button 
                              type="text"
                              icon={<SaveOutlined />}
                              loading={savingDrafts.has(questionKey)}
                              onClick={() => handleSaveDraft(question, index)}
                              className="save-draft-button"
                              disabled={worksheetBasic?.status === 2}
                            >
                              保存草稿
                            </Button>
                            <Button 
                              icon={
                                worksheetBasic?.tid && getTeacherAvatar(worksheetBasic.tid) ? (
                                  <Avatar 
                                    src={getTeacherAvatar(worksheetBasic.tid)} 
                                    size={20}
                                  />
                                ) : (
                                  <UserOutlined style={{ verticalAlign: 'middle' }} />
                                )
                              }
                              onClick={() => handleAIComment(question, index)}
                              className="ai-comment-button"
                              style={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: '6px',
                                fontSize: '16px'
                              }}
                              disabled={worksheetBasic?.status === 2}
                            >
                              AI老师点评
                            </Button>
                          </div>
                        </div>
                      );
                    })()}
                    </div>
                  </div>
                )
              }
            ]}
          />
        ))}
      </Space>
    );
  };

  // 渲染单元列表菜单
  const renderUnitMenu = () => {
    if (!worksheetUnits?.unit_list || worksheetUnits.unit_list.length === 0) {
      return (
        <div style={{ padding: '20px', textAlign: 'center' }}>
          <Empty description="暂无单元" image={Empty.PRESENTED_IMAGE_SIMPLE} />
        </div>
      );
    }

    const menuItems = worksheetUnits.unit_list.map((unit, index) => ({
      key: unit.id.toString(),
      label: (
        <div>
          <div className={`unit-menu-item-text ${
            selectedUnit?.id === unit.id ? 'selected' : ''
          }`}>
            <span className="unit-number">{index + 1}</span>
            {unit.name}
          </div>
        </div>
      ),
      onClick: () => {
        // 如果当前单元已经被选中，则不执行点击操作
        if (selectedUnit?.id === unit.id) {
          return;
        }
        handleUnitSelect(unit);
      },
      className: `unit-menu-item ${
        selectedUnit?.id === unit.id ? 'selected' : ''
      }`
    }));

    return (
      <Menu
        mode="inline"
        selectedKeys={selectedUnit ? [selectedUnit.id.toString()] : []}
        className="unit-menu"
        items={menuItems}
      />
    );
  };

  return (
    <MainLayout>
      <div className="worksheet-container">
        {loading ? (
          <div style={{ 
            display: 'flex', 
            flexDirection: 'column',
            justifyContent: 'center', 
            alignItems: 'center', 
            height: '100%',
            minHeight: '80vh'
          }}>
            <Spin size="large" />
            <div style={{ marginTop: '16px' }}>
              <Text type="secondary">正在加载作业单信息...</Text>
            </div>
          </div>
        ) : worksheetBasic && worksheetUnits ? (
          <div>
            {/* 顶部基本信息部分 */}
            <div className="worksheet-header">
              <div className="worksheet-header-content">
                <div>
                  <div className="worksheet-header-info">
                    <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
                      <Title level={2} className="worksheet-title" style={{ margin: 0 }}>
                        {worksheetBasic.title}
                      </Title>
                      {/* 提交作业按钮 */}
                      {worksheetBasic.status === 2 ? (
                        <Button
                          type="primary"
                          danger
                          loading={submitting}
                          onClick={handleCancelSubmit}
                          style={{
                            borderRadius: '8px',
                            fontWeight: '500'
                          }}
                        >
                          撤销提交
                        </Button>
                      ) : (
                        <Button
                          type="primary"
                          loading={submitting}
                          onClick={handleSubmitExercise}
                          style={{
                            borderRadius: '8px',
                            fontWeight: '500',
                            background: '#21808d',
                            borderColor: '#21808d'
                          }}
                        >
                          提交作业
                        </Button>
                      )}
                    </div>
                  </div>
                  
                  <div className="worksheet-header-meta">
                    {getStatusTag(worksheetBasic.status)}
                    
                    {renderTeacherInfo()}
                    
                    {worksheetBasic.duration && (
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        <ClockCircleOutlined className="info-icon" />
                        <Text type="secondary" className="info-text">预计时长: {worksheetBasic.duration} 分钟</Text>
                      </div>
                    )}
                    

                  </div>
                </div>
                
                <Tooltip title="返回" placement='left'>
                  <Button
                    type="text"
                    icon={<RollbackOutlined />}
                    onClick={() => {
                      // 如果有未保存的草稿，询问用户是否保存
                      if (hasUnsavedChanges) {
                        modal.confirm({
                            title: '保存确认',
                            content: <div style={{ marginBottom: '8px' }}>您有作答未保存，需要在继续操作前保存草稿吗？</div>,
                            footer: [
                              <div key="footer" style={{ display: 'flex', justifyContent: 'flex-end', gap: '8px' }}>
                                <Button className="custom-button" onClick={() => {
                                  Modal.destroyAll();
                                }}>
                                  取消
                                </Button>
                                <Button className="custom-button" onClick={() => {
                                  // 恢复textarea内容为draft
                                  if (selectedUnit && unitQuestions.length > 0) {
                                    setAnswerTexts(prev => {
                                      const newAnswerTexts = { ...prev };
                                      unitQuestions.forEach((_question, index) => {
                                        const questionKey = `${selectedUnit.id}-${index}`;
                                        newAnswerTexts[questionKey] = originalDrafts[questionKey] || '';
                                      });
                                      return newAnswerTexts;
                                    });
                                  }
                                  Modal.destroyAll();
                                  navigate(`/class/${classId}/exercises`);
                                }}>
                                  不保存
                                </Button>
                                <Button 
                                  className="save-button"
                                  onClick={async () => {
                                    try {
                                      await handleBatchSaveDrafts();
                                      Modal.destroyAll();
                                      navigate(`/class/${classId}/exercises`);
                                    } catch (error) {
                                      console.error('返回前保存草稿失败:', error);
                                      message.error('保存草稿失败，请重试');
                                    }
                                  }}
                                >
                                  保存
                                </Button>
                              </div>
                            ],
                            keyboard: true,
                            maskClosable: true
                          });
                        return;
                      }
                      navigate(`/class/${classId}/exercises`);
                    }}
                    style={{
                      width: '48px',
                      height: '48px',
                      borderRadius: '12px',
                      background: 'rgba(255, 255, 255, 0.9)',
                      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}
                  />
                </Tooltip>
              </div>
            </div>

            {/* 下半部答题部分 */}
            <Layout className="worksheet-layout" style={{
              boxShadow: '0 4px 12px rgba(38, 38, 38, 0.15)',
              borderRadius: '12px',
              overflow: 'hidden'
            }}>
              {collapsed && (
                <Button
                  type="text"
                  icon={<MenuUnfoldOutlined />}
                  onClick={() => setCollapsed(false)}
                  className="collapse-button"
                />
              )}
              <Sider
                collapsible
                collapsed={collapsed}
                onCollapse={setCollapsed}
                width={240}
                className="worksheet-sider"
                breakpoint="lg"
                collapsedWidth={0}
                trigger={null}
              >
                <div className="sider-header">
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Button
                    type="text"
                    icon={showBackground ? 
                      <InfoCircleFilled style={{ marginRight: '0px', color: '#21808d', fontSize: '16px'}} /> :
                      <InfoCircleOutlined style={{ marginRight: '0px', color: '#21808d', fontSize: '16px'}} />
                    }
                    onClick={handleShowBackground}
                    className={`background-button ${showBackground ? 'active' : ''}`}
                    >
                      练习背景
                    </Button>
                    {!collapsed && (
                      <Button
                        type="text"
                        icon={<MenuFoldOutlined />}
                        onClick={() => setCollapsed(!collapsed)}
                        className="menu-fold-button"
                      />
                    )}
                  </div>
                </div>
                <div className="unit-menu-container">
                  {renderUnitMenu()}
                </div>
              </Sider>

              <Layout>
                <Content className="content-area">
                  {showBackground && (
                    <div>
                      <div style={{ marginBottom: '18px' }}>
                        <Title level={3} className="section-title">
                          <InfoCircleFilled className="section-title-icon" />
                          练习背景
                        </Title>
                      </div>
                      <div className="background-info unified-background">
                        <div dangerouslySetInnerHTML={{ __html: worksheetBasic.bgtext || '暂无背景信息' }} />
                      </div>
                    </div>
                  )}

                  {selectedUnit && (
                    <>
                      <div style={{ marginBottom: '18px' }}>
                        <Title level={3} className="section-title">
                          <span className="section-number">{(worksheetUnits?.unit_list?.findIndex(unit => unit.id === selectedUnit?.id) ?? -1) + 1}</span>
                          {selectedUnit.name}
                        </Title>
                        {selectedUnit.bgtext && (
                          <div className="unit-background unified-background">
                            <div dangerouslySetInnerHTML={{ __html: selectedUnit.bgtext }} />
                          </div>
                        )}
                      </div>
                      
                      <Spin spinning={questionsLoading}>
                        {renderQuestions()}
                      </Spin>
                    </>
                  )}

                  {!selectedUnit && !showBackground && worksheetUnits.unit_list && worksheetUnits.unit_list.length > 0 && (
                    <div className="empty-state">
                      <Empty 
                        description="请从左侧选择一个单元开始练习" 
                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                      />
                    </div>
                  )}
                </Content>
              </Layout>
            </Layout>
            {/* 练习目标抽屉 */}
            <TargetDrawer
              visible={targetDrawerVisible}
              onClose={() => setTargetDrawerVisible(false)}
              frameworks={selectedQuestion?.framework_list || []}
              title={`练习目标${selectedQuestion ? ` - ${selectedQuestion.title}` : ''}`}
              width={600}
            />

            {/* 练习指南抽屉 */}
            <GuideDrawer
              visible={guideDrawerVisible}
              onClose={() => setGuideDrawerVisible(false)}
              guides={selectedQuestion?.guide_list || []}
              title={`练习指南${selectedQuestion ? ` - ${selectedQuestion.title}` : ''}`}
              width="65%"
            />
          </div>
        ) : (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="暂无作业单信息"
            style={{ padding: '50px' }}
          />
        )}
      </div>
    </MainLayout>
  );
};

export default Worksheet;