/* TrainingCamp 页面样式 */

/* 日历容器样式 */
.fc {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* 日历头部样式 */
.fc-header-toolbar {
  margin-bottom: 0.8em;
  padding: 0;
}

.fc-toolbar-chunk {
  display: flex;
  align-items: center;
  gap: 6px;
}

/* 右侧按钮组右对齐 */
.fc-header-toolbar .fc-toolbar-chunk:last-child {
  justify-content: flex-end;
  margin-left: auto;
}

/* 左侧标题样式 */
.fc-header-toolbar .fc-toolbar-chunk:first-child {
  justify-content: flex-start;
}

.fc-button {
  background-color: #333 !important;
  border-color: #333 !important;
  color: white;
  border-radius: 4px;
  padding: 4px 10px;
  font-size: 11px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.fc-button:hover {
  background-color: #777 !important;
  border-color: #777 !important;
}

.fc-button:focus {
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.2);
}

.fc-button-active {
  background-color: #000000 !important;
  border-color: #000000 !important;
}

.fc-button:disabled {
  background-color: #000000 !important;
  border-color: #000000 !important;
  opacity: 0.6;
}

/* 日历标题样式 */
.fc-toolbar-title {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

/* 日期单元格样式 */
.fc-daygrid-day {
  border: 1px solid #f0f0f0;
}

.fc-daygrid-day:hover {
  background-color: #fafafa;
}

.fc-day-today {
  background-color: #f5f5f5 !important;
}

/* 日历头部样式调整 */
.fc-col-header-cell {
  background-color: #f5f5f5;
  font-weight: 600;
  color: #262626;
  font-size: 11px;
  border-bottom: 1px solid #e8e8e8;
}

.fc-col-header-cell-cushion {
  color: #262626;
  font-weight: 600;
  padding: 8px 4px;
}

.fc-daygrid-day-number {
  color: #262626;
  font-weight: 500;
  font-size: 12px;
}

/* 事件样式 */
.fc-event {
  border-radius: 4px;
  border: none;
  padding: 2px 4px;
  font-size: 11px;
  font-weight: 500;
  margin: 1px 0;
  cursor: pointer;
  transition: all 0.2s ease;
  /* 移除固定背景色，使用JavaScript动态设置的颜色 */
}

.fc-event:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  opacity: 0.8;
}

.fc-event-title {
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 更多链接样式 */
.fc-more-link {
  color: #262626;
  font-size: 11px;
  font-weight: 500;
}

.fc-more-link:hover {
  color: #434343;
  text-decoration: none;
}

/* 列表视图样式 */
.fc-list-event {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.fc-list-event:hover {
  background-color: #f5f5f5;
}

.fc-list-event-title {
  font-weight: 500;
  color: #262626;
}

.fc-list-event-time {
  color: #8c8c8c;
  font-size: 12px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .fc-header-toolbar {
    flex-direction: column;
    gap: 8px;
  }
  
  .fc-toolbar-chunk {
    display: flex;
    justify-content: center;
  }
  
  .fc-button {
    font-size: 11px;
    padding: 3px 8px;
  }
  
  .fc-toolbar-title {
    font-size: 16px;
    margin: 8px 0;
  }
}

/* 课程卡片悬停效果增强 */
.training-camp-card {
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.training-camp-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border-color: #d9d9d9;
}

/* 月份标题样式优化 */
.month-header {
  background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 24px;
  border: 1px solid #e8e8e8;
}

.month-title {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

/* 加载状态优化 */
.loading-container {
  background: linear-gradient(135deg, #fafafa 0%, #f0f0f0 100%);
  border-radius: 12px;
  padding: 60px 20px;
}

/* 空状态优化 */
.empty-container {
  background: linear-gradient(135deg, #fafafa 0%, #f0f0f0 100%);
  border-radius: 12px;
  padding: 60px 20px;
  text-align: center;
}