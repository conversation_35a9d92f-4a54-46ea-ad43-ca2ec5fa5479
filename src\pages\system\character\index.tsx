import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Card, Table, Button, Space, Input, Tooltip, Modal, Tag, Breadcrumb, Select, Avatar } from 'antd';
import { PlusOutlined, EditOutlined, StopOutlined, ReloadOutlined, ExclamationCircleOutlined, DeleteFilled, RollbackOutlined, UndoOutlined, HomeOutlined, BookOutlined, ProfileOutlined, SendOutlined, UserOutlined } from '@ant-design/icons';
import type { SortOrder } from 'antd/es/table/interface';
import type { TableProps } from 'antd/es/table';
import { 
  getCharacters, 
  getCharacter,
  updateCharacter,
  type CharacterResponse
} from '../../../services/system/character';
import { showError, showSuccess } from '../../../utils/errorHandler';
import { getGlobalTenantInfo, type GlobalTenantInfo } from '../../../components/GlobalTenantSelector';
import CharacterForm from './CharacterForm';
import PromptDrawer from './PromptDrawer';
import AvatarUploader from './AvatarUploader';
import { Link } from 'react-router-dom';

const { Search } = Input;
const { confirm } = Modal;

// 性别映射
const genderMap: Record<number, { name: string; color: string }> = {
  0: { name: '未知', color: 'default' },
  1: { name: '男', color: 'blue' },
  2: { name: '女', color: 'pink' },
};

const CharacterManagement: React.FC = () => {
  const [characters, setCharacters] = useState<CharacterResponse[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [avatarUploaderVisible, setAvatarUploaderVisible] = useState(false);
  const [currentCharacter, setCurrentCharacter] = useState<CharacterResponse | null>(null);
  const [characterLoading, setCharacterLoading] = useState(false);
  const [selectedTenantId, setSelectedTenantId] = useState<number | undefined>(undefined);
  const [searchName, setSearchName] = useState('');
  const [searchNameValue, setSearchNameValue] = useState(''); // 用于存储姓名输入框的当前值
  const [searchProfile, setSearchProfile] = useState('');
  const [searchProfileValue, setSearchProfileValue] = useState(''); // 用于存储资料输入框的当前值
  const [searchNotes, setSearchNotes] = useState('');
  const [searchNotesValue, setSearchNotesValue] = useState(''); // 用于存储备注输入框的当前值
  const [isRecycleBin, setIsRecycleBin] = useState(false);
  const [selectedPublishedStatus, setSelectedPublishedStatus] = useState<number | undefined>(undefined);
  const [tableKey, setTableKey] = useState<number>(0);
  
  // 分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  
  // 排序状态
  const [sortOrders, setSortOrders] = useState<{
    [key: string]: 'asc' | 'desc' | undefined
  }>({
    id: 'desc',
    ctime: undefined
  });

  // 创建对搜索框的引用
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const searchNameInputRef = useRef<any>(null);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const searchProfileInputRef = useRef<any>(null);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const searchNotesInputRef = useRef<any>(null);

  // 从 GlobalTenantSelector 读取全局租户信息
  const loadGlobalTenant = useCallback(() => {
    const tenantInfo: GlobalTenantInfo | null = getGlobalTenantInfo();
    if (tenantInfo) {
      setSelectedTenantId(tenantInfo.id);
      return tenantInfo;
    } else {
      setSelectedTenantId(undefined);
      return null;
    }
  }, []);

  // 监听全局租户变化
  useEffect(() => {
    // 初始加载
    loadGlobalTenant();

    // 监听 localStorage 变化（跨标签页）
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'globalTenant') {
        loadGlobalTenant();
      }
    };

    // 监听自定义事件（同一页面内的更新）
    const handleGlobalTenantChange = (e: Event) => {
      const customEvent = e as CustomEvent;
      const { id } = customEvent.detail;
      if (id) {
        setSelectedTenantId(id);
      } else {
        setSelectedTenantId(undefined);
      }
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('globalTenantChanged', handleGlobalTenantChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('globalTenantChanged', handleGlobalTenantChange);
    };
  }, [loadGlobalTenant]);

  // 获取人物角色列表
  const fetchCharacters = useCallback(async () => {
    if (!selectedTenantId) {
      setCharacters([]);
      return;
    }

    try {
      setLoading(true);
      
      let sortBy: 'id' | 'ctime' | undefined;
      let sortOrder: 'asc' | 'desc' | undefined;
      
      if (sortOrders.id) {
        sortBy = 'id';
        sortOrder = sortOrders.id;
      } else if (sortOrders.ctime) {
        sortBy = 'ctime';
        sortOrder = sortOrders.ctime;
      }
      
      const response = await getCharacters(selectedTenantId, {
        skip: (pagination.current - 1) * pagination.pageSize,
        limit: pagination.pageSize,
        active: isRecycleBin ? 0 : 1, // 使用active参数进行服务端过滤
        published: selectedPublishedStatus,
        sort_by: sortBy,
        sort_order: sortOrder,
        name: searchName || undefined, // 添加姓名搜索参数
        profile: searchProfile || undefined, // 添加资料搜索参数
        notes: searchNotes || undefined, // 添加备注搜索参数
      });
      
      setCharacters(response.items);
      setPagination(prev => ({
        ...prev,
        total: response.total
      }));
    } catch (error) {
      showError(error, '获取人物角色列表失败');
    } finally {
      setLoading(false);
    }
  }, [selectedTenantId, searchName, searchProfile, searchNotes, selectedPublishedStatus, sortOrders, isRecycleBin, pagination.current, pagination.pageSize]);

  useEffect(() => {
    if (selectedTenantId) {
      fetchCharacters();
    }
  }, [fetchCharacters]);

  // 处理姓名搜索
  const handleNameSearch = (value: string) => {
    setSearchName(value);
    setSearchNameValue(value);
  };

  // 处理姓名搜索输入框值变化，但不触发搜索
  const handleNameInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchNameValue(e.target.value);
  };

  // 处理资料搜索
  const handleProfileSearch = (value: string) => {
    setSearchProfile(value);
    setSearchProfileValue(value);
  };

  // 处理资料搜索输入框值变化，但不触发搜索
  const handleProfileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchProfileValue(e.target.value);
  };

  // 处理备注搜索
  const handleNotesSearch = (value: string) => {
    setSearchNotes(value);
    setSearchNotesValue(value);
  };

  // 处理备注搜索输入框值变化，但不触发搜索
  const handleNotesInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchNotesValue(e.target.value);
  };

  // 处理发布状态过滤
  const handlePublishedStatusChange = (value: number | undefined) => {
    setSelectedPublishedStatus(value);
  };

  // 切换回收站模式
  const toggleRecycleBin = () => {
    setIsRecycleBin(!isRecycleBin);
    // 重置分页状态，避免切换模式时出现显示异常
    setPagination(prev => ({
      ...prev,
      current: 1,
    }));
  };

  // 处理添加人物角色
  const handleAdd = () => {
    if (!selectedTenantId) {
      showError(null, '请先在顶部导航栏选择租户');
      return;
    }
    setCurrentCharacter(null);
    setModalVisible(true);
  };

  // 处理编辑人物角色
  const handleEdit = (record: CharacterResponse) => {
    if (!selectedTenantId) {
      showError(null, '请先在顶部导航栏选择租户');
      return;
    }
    setCurrentCharacter(record);
    setModalVisible(true);
  };

  // 处理禁用人物角色（将删除改为禁用）
  const handleDisable = (id: number) => {
    const actionText = '禁用';
    confirm({
      title: `确认${actionText}`,
      icon: <ExclamationCircleOutlined />,
      content: `确定要${actionText}这个人物角色吗？禁用后可在回收站中恢复。`,
      okText: '确认',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          // 将人物角色禁用（设置active为0）
          await updateCharacter(id, { active: 0 }, selectedTenantId!);
          showSuccess(`${actionText}成功`);
          fetchCharacters();
        } catch (error: unknown) {
          showError(error, `${actionText}人物角色失败`);
        }
      },
    });
  };

  // 处理恢复人物角色
  const handleRestore = async (characterId: number) => {
    try {
      await updateCharacter(characterId, { active: 1 }, selectedTenantId!);
      showSuccess('恢复成功');
      fetchCharacters();
    } catch (error) {
      showError(error, '恢复人物角色失败');
    }
  };

  // 处理发布人物角色
  const handlePublish = async (characterId: number) => {
    try {
      await updateCharacter(characterId, { published: 1 }, selectedTenantId!);
      showSuccess('发布成功');
      fetchCharacters();
    } catch (error) {
      showError(error, '发布人物角色失败');
    }
  };

  // 处理撤销发布人物角色
  const handleUnpublish = async (characterId: number) => {
    try {
      await updateCharacter(characterId, { published: 0 }, selectedTenantId!);
      showSuccess('撤销发布成功');
      fetchCharacters();
    } catch (error) {
      showError(error, '撤销发布人物角色失败');
    }
  };

  // 处理编辑提示词
  const handleEditPrompt = async (record: CharacterResponse) => {
    try {
      setCharacterLoading(true);
      setDrawerVisible(true);
      
      // 调用API获取完整的人物角色信息
      const characterDetail = await getCharacter(record.id, selectedTenantId!);
      setCurrentCharacter(characterDetail);
    } catch (error) {
      showError(error, '获取人物角色详情失败');
      setDrawerVisible(false);
    } finally {
      setCharacterLoading(false);
    }
  };

  // 处理上传头像
  const handleUploadAvatar = (record: CharacterResponse) => {
    setCurrentCharacter(record);
    setAvatarUploaderVisible(true);
  };

  // 头像上传成功后的回调
  const handleAvatarUploadSuccess = () => {
    setAvatarUploaderVisible(false);
    fetchCharacters();
  };

  // 表单提交成功后的回调
  const handleFormSuccess = () => {
    setModalVisible(false);
    fetchCharacters();
  };

  // 处理重置刷新
  const handleRefresh = async () => {
    // 清空搜索框
    setSearchName('');
    setSearchNameValue('');

    // 清空资料搜索框
    setSearchProfile('');
    setSearchProfileValue('');

    // 清空备注搜索框
    setSearchNotes('');
    setSearchNotesValue('');

    // 清空发布状态过滤
    setSelectedPublishedStatus(undefined);

    // 重置排序为ID降序
    setSortOrders({
      id: 'desc',
      ctime: undefined
    });

    // 重置分页状态
    setPagination(prev => ({
      ...prev,
      current: 1,
      pageSize: 10,
    }));

    setTableKey(prevKey => prevKey + 1);
    
    // 数据会通过useEffect自动重新加载
  };

  // 处理表格排序变化
  const handleTableChange: TableProps<CharacterResponse>['onChange'] = (paginationConfig, _filters, sorter) => {
    if (paginationConfig) {
      setPagination(prev => ({
        ...prev,
        current: paginationConfig.current || 1,
        pageSize: paginationConfig.pageSize || 10,
      }));
    }

    interface SorterInfo {
      field?: string;
      order?: 'ascend' | 'descend';
    }

    const currentSorter = Array.isArray(sorter) ? sorter[0] : sorter;
    
    const newSortOrders: { [key: string]: "asc" | "desc" | undefined } = {
      id: undefined,
      ctime: undefined
    };

    if (currentSorter && (currentSorter as SorterInfo).field) {
      const field = (currentSorter as SorterInfo).field;
      const order = (currentSorter as SorterInfo).order;
      
      if (field === 'id') {
        if (order === 'ascend') {
          newSortOrders.id = 'asc';
        } else if (order === 'descend') {
          newSortOrders.id = 'desc';
        }
      } else if (field === 'ctime') {
        if (order === 'ascend') {
          newSortOrders.ctime = 'asc';
        } else if (order === 'descend') {
          newSortOrders.ctime = 'desc';
        }
      }
    }

    if (Object.values(newSortOrders).every(v => v === undefined)) {
      newSortOrders.id = 'desc';
    }

    setSortOrders(newSortOrders);
  };

  // 表格列定义
  const getColumns = () => {
    const baseColumns = [
      {
        title: 'ID',
        dataIndex: 'id',
        key: 'id',
        width: 80,
        sorter: true,
        sortOrder: sortOrders.id === 'asc' ? 'ascend' as SortOrder : sortOrders.id === 'desc' ? 'descend' as SortOrder : false as unknown as SortOrder,
      },
      {
        title: '头像',
        dataIndex: 'avatar',
        key: 'avatar',
        width: 80,
        render: (avatar: string | null, record: CharacterResponse) => (
          <div
            style={{ cursor: 'pointer' }}
            onClick={() => handleUploadAvatar(record)}
          >
            <Avatar
              size={40}
              src={avatar}
              icon={<UserOutlined />}
              shape="circle"
              style={{ backgroundColor: '#f0f0f0' }}
            />
          </div>
        ),
      },
      {
        title: '姓名',
        dataIndex: 'name',
        key: 'name',
        width: '20%',
        ellipsis: {
          showTitle: false,
        },
        render: (text: string, record: CharacterResponse) => (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Tooltip title={record.profile || '暂无人物资料'}>
              <ProfileOutlined style={{ marginRight: 6, color: '#1890ff', cursor: 'pointer' }} />
            </Tooltip>
            <Tooltip title={text}>
              {text}
            </Tooltip>
          </div>
        ),
      },
      {
        title: '性别',
        dataIndex: 'gender',
        key: 'gender',
        width: 80,
        render: (gender: number) => {
          const genderInfo = genderMap[gender] || { name: '未知', color: 'default' };
          return <Tag color={genderInfo.color}>{genderInfo.name}</Tag>;
        },
      },
      {
        title: '备注',
        dataIndex: 'notes',
        key: 'notes',
        width: '20%',
        ellipsis: {
          showTitle: false,
        },
        render: (text: string) => (
          <Tooltip title={text} placement="topLeft">
            {text || '-'}
          </Tooltip>
        ),
      },
      {
        title: '发布状态',
        dataIndex: 'published',
        key: 'published',
        width: 100,
        render: (published: number) => (
          <Tag color={published === 1 ? 'green' : 'default'}>
            {published === 1 ? '已发布' : '未发布'}
          </Tag>
        ),
      },
    ];

    // 根据是否为回收站模式，显示不同的操作按钮
    if (isRecycleBin) {
      // 回收站模式下的操作列
      baseColumns.push({
        title: '操作',
        key: 'action',
        width: 200,
        render: (_: unknown, record: CharacterResponse) => (
          <Space size="small">
            <Tooltip title="编辑">
              <Button
                type="primary"
                icon={<EditOutlined />}
                size="small"
                onClick={() => handleEdit(record)}
              />
            </Tooltip>
            <Tooltip title="恢复">
              <Button
                type="primary"
                style={{ backgroundColor: '#52c41a', borderColor: '#52c41a' }}
                icon={<UndoOutlined />}
                size="small"
                onClick={() => handleRestore(record.id)}
              />
            </Tooltip>
          </Space>
        ),
      } as never);
    } else {
      // 正常模式下的操作列
      baseColumns.push({
        title: '操作',
        key: 'action',
        width: 200,
        render: (_: unknown, record: CharacterResponse) => (
          <Space size="small">
            <Tooltip title="编辑">
              <Button
                type="primary"
                icon={<EditOutlined />}
                size="small"
                onClick={() => handleEdit(record)}
              />
            </Tooltip>
            <Tooltip title="提示词">
              <Button
                type="primary"
                style={{ backgroundColor: '#1890ff', borderColor: '#1890ff' }}
                icon={<BookOutlined />}
                size="small"
                onClick={() => handleEditPrompt(record)}
              />
            </Tooltip>
            {record.published === 1 ? (
              <Tooltip title="撤销发布">
                <Button
                  type="primary"
                  style={{ backgroundColor: '#faad14', borderColor: '#faad14' }}
                  icon={<RollbackOutlined />}
                  size="small"
                  onClick={() => handleUnpublish(record.id)}
                />
              </Tooltip>
            ) : (
              <Tooltip title="发布">
                <Button
                  type="primary"
                  style={{ backgroundColor: '#52c41a', borderColor: '#52c41a' }}
                  icon={<SendOutlined />}
                  size="small"
                  onClick={() => handlePublish(record.id)}
                />
              </Tooltip>
            )}
            <Tooltip title="禁用">
              <Button
                type="primary"
                style={{ backgroundColor: '#ff4d4f', borderColor: '#ff4d4f' }}
                icon={<StopOutlined />}
                size="small"
                onClick={() => handleDisable(record.id)}
              />
            </Tooltip>
          </Space>
        ),
      } as never);
    }

    return baseColumns;
  };

  return (
    <div>
      {/* 面包屑导航 */}
      <Breadcrumb 
        style={{ marginBottom: 16 }}
        items={[
          {
            title: (
              <Link to="/system/home">
                <HomeOutlined /> 主页
              </Link>
            ),
          },
          {
            title: '租户空间',
          },
          {
            title: '人物角色库',
          },
        ]}
      />

      <Card
        title={isRecycleBin ? "人物角色回收站" : "人物角色库"}
        extra={
          <Space>
            <Search
              placeholder="搜索姓名"
              allowClear
              onSearch={handleNameSearch}
              style={{ width: 160 }}
              ref={searchNameInputRef}
              value={searchNameValue}
              onChange={handleNameInputChange}
            />
            <Search
              placeholder="搜索资料"
              allowClear
              onSearch={handleProfileSearch}
              style={{ width: 160 }}
              ref={searchProfileInputRef}
              value={searchProfileValue}
              onChange={handleProfileInputChange}
            />
            <Search
              placeholder="搜索备注"
              allowClear
              onSearch={handleNotesSearch}
              style={{ width: 160 }}
              ref={searchNotesInputRef}
              value={searchNotesValue}
              onChange={handleNotesInputChange}
            />
            <Select
              placeholder="发布状态"
              allowClear
              style={{ width: 120 }}
              value={selectedPublishedStatus}
              onChange={handlePublishedStatusChange}
            >
              <Select.Option value={0}>未发布</Select.Option>
              <Select.Option value={1}>已发布</Select.Option>
            </Select>
            {/* 添加人物角色按钮只在非回收站模式下显示 */}
            {!isRecycleBin && selectedTenantId && (
              <Tooltip title="添加人物角色">
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAdd}
                />
              </Tooltip>
            )}
            <Tooltip title="重置刷新">
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
              />
            </Tooltip>
            {/* 回收站/返回按钮 */}
            {isRecycleBin ? (
              <Tooltip title="返回">
                <Button
                  type="primary"
                  icon={<RollbackOutlined />}
                  onClick={toggleRecycleBin}
                />
              </Tooltip>
            ) : (
              <Tooltip title="回收站">
                <Button
                  type="default"
                  icon={<DeleteFilled />}
                  onClick={toggleRecycleBin}
                />
              </Tooltip>
            )}
          </Space>
        }
      >
        <Table
          key={tableKey}
          columns={getColumns()}
          dataSource={characters}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          locale={{
            emptyText: selectedTenantId ? (isRecycleBin ? '回收站中没有人物角色' : '暂无人物角色数据') : '请先在顶部导航栏选择租户'
          }}
          onChange={handleTableChange}
          sortDirections={['ascend', 'descend', 'ascend']}
          showSorterTooltip={false}
        />
      </Card>

      {selectedTenantId && (
        <CharacterForm
          visible={modalVisible}
          onCancel={() => setModalVisible(false)}
          onSuccess={handleFormSuccess}
          character={currentCharacter}
          tenantId={selectedTenantId}
        />
      )}

      {/* 提示词编辑抽屉 */}
      {selectedTenantId && (
        <PromptDrawer
          visible={drawerVisible}
          onClose={() => {
            setDrawerVisible(false);
            setCurrentCharacter(null);
            setCharacterLoading(false);
          }}
          onSuccess={handleFormSuccess}
          character={currentCharacter}
          loading={characterLoading}
          tenantId={selectedTenantId}
        />
      )}

      {/* 头像上传对话框 */}
      {selectedTenantId && currentCharacter && (
        <AvatarUploader
          visible={avatarUploaderVisible}
          onCancel={() => setAvatarUploaderVisible(false)}
          onSuccess={handleAvatarUploadSuccess}
          characterId={currentCharacter.id}
          tenantId={selectedTenantId}
          currentAvatar={currentCharacter.avatar || undefined}
        />
      )}
    </div>
  );
};

export default CharacterManagement; 