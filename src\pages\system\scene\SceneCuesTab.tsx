import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { Row, Col, Card, Empty, Spin, Typography, Button, message, Tooltip, Modal, Space } from 'antd';
import { FileTextOutlined, CommentOutlined, ReloadOutlined, PlusOutlined, EditOutlined, DeleteOutlined, HolderOutlined, DragOutlined, SaveOutlined } from '@ant-design/icons';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragStartEvent,
  useDroppable,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
  useSortable,
} from '@dnd-kit/sortable';
import {
  CSS,
} from '@dnd-kit/utilities';
import { 
  getSceneCues, 
  getCueLines,
  getSceneCharacters,
  batchDeleteSceneCues,
  batchUpdateSceneCueOrder,
  deleteSceneLine,
  batchMoveSceneLines,
  batchUpdateSceneLineOrder,
  saveSceneScript,
  type SceneCueResponse,
  type SceneLineResponse,
  type SceneCharacterResponse,
  type SceneCueBatchOrderRequest,
  type SceneCueBatchDeleteRequest,
  type SceneLineBatchMoveRequest,
  type SceneLineBatchOrderRequest
} from '../../../services/system/scene';
import { getGlobalTenantInfo } from '../../../components/GlobalTenantSelector';
import { showError } from '../../../utils/errorHandler';
import CueFormDrawer from './CueFormDrawer';
import LineFormDrawer from './LineFormDrawer';

const { Text } = Typography;

interface SceneCuesTabProps {
  sceneId?: string;
}

// SceneCuesTab的ref接口
export interface SceneCuesTabRef {
  hasUnsavedChanges: () => boolean;
  saveContent: () => Promise<void>;
  resetToInitial: () => void;
}

// 生成剧情线索标题的辅助函数
const getCueTitle = (cue: SceneCueResponse, characters: SceneCharacterResponse[]): string => {
  const character = characters.find(c => c.cid === cue.cid);
  return character ? `${character.name}的发言` : `剧情线索${cue.id}`;
};

// 可放置的剧情线索列表项组件
interface DroppableCueItemProps {
  cue: SceneCueResponse;
  index: number;
  isSelected: boolean;
  isSortMode?: boolean;
  isDraggingLine?: boolean;
  selectedCueId?: number | null;
  characters: SceneCharacterResponse[];
  onSelect: (cue: SceneCueResponse) => void;
  onEdit: (cue: SceneCueResponse) => void;
  onDelete: (cue: SceneCueResponse) => void;
}

const DroppableCueItem: React.FC<DroppableCueItemProps> = ({ 
  cue, 
  index, 
  isSelected, 
  isSortMode = false,
  isDraggingLine = false,
  selectedCueId,
  characters,
  onSelect, 
  onEdit, 
  onDelete 
}) => {
  const {
    isOver,
    setNodeRef: setDroppableRef,
  } = useDroppable({
    id: `cue-${cue.id}`,
    disabled: isSortMode || !isDraggingLine, // 排序模式或非拖动台词时禁用
  });

  // 检查是否是拖拽到当前所在的剧情线索
  const isSameCue = selectedCueId === cue.id && isDraggingLine;
  const canDrop = isOver && isDraggingLine && !isSameCue;
  const cannotDrop = isOver && isDraggingLine && isSameCue;

  const style = {
    cursor: 'pointer',
    backgroundColor: isSelected ? '#e6f7ff' : (canDrop ? '#f0f9ff' : (cannotDrop ? '#fff2f0' : 'transparent')),
    border: isSelected ? '1px solid #1890ff' : (canDrop ? '2px dashed #1890ff' : (cannotDrop ? '2px dashed #ff4d4f' : '1px solid transparent')),
    borderRadius: '6px',
    padding: '12px',
    marginBottom: '8px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: '16px',
    transition: 'all 0.2s ease',
  };

  return (
    <div
      ref={setDroppableRef}
      style={style}
      onClick={() => onSelect(cue)}
      onMouseEnter={(e) => {
        if (!isSelected && !isOver) {
          e.currentTarget.style.backgroundColor = '#f5f5f5';
        }
      }}
      onMouseLeave={(e) => {
        if (!isSelected && !isOver) {
          e.currentTarget.style.backgroundColor = 'transparent';
        }
      }}
    >
      <div style={{ display: 'flex', alignItems: 'center', flex: 1, minWidth: 0 }}>
        {isSortMode && (
          <div 
            style={{ 
              marginRight: '12px', 
              cursor: 'grab',
              color: '#999',
              fontSize: '14px'
            }}
          >
            <HolderOutlined />
          </div>
        )}
        <div style={{ 
          backgroundColor: isSelected ? '#1890ff' : '#666',
          color: 'white',
          width: '20px',
          height: '20px',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '12px',
          fontWeight: 'bold',
          marginRight: '12px'
        }}>
          {index + 1}
        </div>
        <div style={{ 
          flex: 1, 
          minWidth: 0, 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'space-between',
          gap: '16px'
        }}>
          {/* 左侧内容区 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '6px', flex: 1, minWidth: 0 }}>
            <Text style={{ color: isSelected ? '#1890ff' : '#666', fontSize: '14px', flexShrink: 0 }}>
              当
            </Text>
            
            {/* 头像 */}
            {(() => {
              const character = characters.find(c => c.cid === cue.cid);
              return character?.avatar ? (
                <img 
                  src={character.avatar} 
                  alt={character.name}
                  style={{ 
                    width: '20px', 
                    height: '20px', 
                    borderRadius: '50%',
                    objectFit: 'cover',
                    flexShrink: 0
                  }}
                />
              ) : (
                <div style={{
                  width: '20px',
                  height: '20px',
                  borderRadius: '50%',
                  backgroundColor: '#ccc',
                  flexShrink: 0
                }} />
              );
            })()}
            
            {/* 姓名 */}
            {(() => {
              const character = characters.find(c => c.cid === cue.cid);
              return character ? (
                <span style={{ 
                  backgroundColor: character.played ? '#ff4d4f' : '#1890ff',
                  color: 'white',
                  padding: '2px 6px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  fontWeight: 'bold',
                  flexShrink: 0
                }}>
                  {character.name}
                </span>
              ) : (
                <span style={{ 
                  backgroundColor: '#999',
                  color: 'white',
                  padding: '2px 6px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  fontWeight: 'bold',
                  flexShrink: 0
                }}>
                  角色{cue.cid}
                </span>
              );
            })()}
            
            {/* 发言内容 */}
            <Tooltip title={`"${cue.content}"`} placement="topLeft">
              <Text 
                strong 
                style={{ 
                  color: isSelected ? '#1890ff' : '#333',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                  flex: 1,
                  minWidth: 0
                }}
              >
                {cue.content}
              </Text>
            </Tooltip>
          </div>
          
          {/* 右侧："时"字 */}
          <Text style={{ color: isSelected ? '#1890ff' : '#666', fontSize: '14px', flexShrink: 0 }}>
            时
          </Text>
          
          {/* 拖放提示 */}
          {canDrop && (
            <div style={{ 
              position: 'absolute',
              left: 0,
              right: 0,
              bottom: '-20px',
              fontSize: '12px', 
              color: '#1890ff' 
            }}>
              放置台词到此剧情线索
            </div>
          )}
          {cannotDrop && (
            <div style={{ 
              position: 'absolute',
              left: 0,
              right: 0,
              bottom: '-20px',
              fontSize: '12px', 
              color: '#ff4d4f' 
            }}>
              台词已在当前剧情线索中
            </div>
          )}
        </div>
      </div>
      <Space size="small">
        <Tooltip title={isSortMode ? "排序模式下不可编辑" : "编辑剧情线索"}>
          <Button
            type="text"
            icon={<EditOutlined />}
            size="small"
            disabled={isSortMode}
            onClick={(e) => {
              e.stopPropagation();
              onEdit(cue);
            }}
          />
        </Tooltip>
        <Tooltip title={isSortMode ? "排序模式下不可删除" : "删除剧情线索"}>
          <Button
            type="text"
            icon={<DeleteOutlined />}
            size="small"
            danger
            disabled={isSortMode}
            onClick={(e) => {
              e.stopPropagation();
              onDelete(cue);
            }}
          />
        </Tooltip>
      </Space>
    </div>
  );
};

// 可拖拽的剧情线索列表项组件
interface SortableCueItemProps {
  cue: SceneCueResponse;
  index: number;
  isSelected: boolean;
  isSortMode?: boolean;
  characters: SceneCharacterResponse[];
  onSelect: (cue: SceneCueResponse) => void;
  onEdit: (cue: SceneCueResponse) => void;
  onDelete: (cue: SceneCueResponse) => void;
}

const SortableCueItem: React.FC<SortableCueItemProps> = ({ 
  cue, 
  index, 
  isSelected, 
  isSortMode = false,
  characters,
  onSelect, 
  onEdit, 
  onDelete 
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: `cue-${cue.id}` });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    cursor: 'pointer',
    backgroundColor: isSelected ? '#e6f7ff' : 'transparent',
    border: isSelected ? '1px solid #1890ff' : '1px solid transparent',
    borderRadius: '6px',
    padding: '12px',
    marginBottom: '8px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: '16px',
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      onClick={() => onSelect(cue)}
      onMouseEnter={(e) => {
        if (!isSelected) {
          e.currentTarget.style.backgroundColor = '#f5f5f5';
        }
      }}
      onMouseLeave={(e) => {
        if (!isSelected) {
          e.currentTarget.style.backgroundColor = 'transparent';
        }
      }}
    >
      <div style={{ display: 'flex', alignItems: 'center', flex: 1, minWidth: 0 }}>
        {isSortMode && (
          <div 
            {...listeners}
            style={{ 
              marginRight: '12px', 
              cursor: 'grab',
              color: '#999',
              fontSize: '14px'
            }}
          >
            <HolderOutlined />
          </div>
        )}
        <div style={{ 
          backgroundColor: isSelected ? '#1890ff' : '#666',
          color: 'white',
          width: '20px',
          height: '20px',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '12px',
          fontWeight: 'bold',
          marginRight: '12px'
        }}>
          {index + 1}
        </div>
        <div style={{ 
          flex: 1, 
          minWidth: 0, 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'space-between',
          gap: '16px'
        }}>
          {/* 左侧内容区 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '6px', flex: 1, minWidth: 0 }}>
            <Text style={{ color: isSelected ? '#1890ff' : '#666', fontSize: '14px', flexShrink: 0 }}>
              当
            </Text>
            
            {/* 头像 */}
            {(() => {
              const character = characters.find(c => c.cid === cue.cid);
              return character?.avatar ? (
                <img 
                  src={character.avatar} 
                  alt={character.name}
                  style={{ 
                    width: '20px', 
                    height: '20px', 
                    borderRadius: '50%',
                    objectFit: 'cover',
                    flexShrink: 0
                  }}
                />
              ) : (
                <div style={{
                  width: '20px',
                  height: '20px',
                  borderRadius: '50%',
                  backgroundColor: '#ccc',
                  flexShrink: 0
                }} />
              );
            })()}
            
            {/* 姓名 */}
            {(() => {
              const character = characters.find(c => c.cid === cue.cid);
              return character ? (
                <span style={{ 
                  backgroundColor: character.played ? '#52c41a' : '#1890ff',
                  color: 'white',
                  padding: '2px 6px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  fontWeight: 'bold',
                  flexShrink: 0
                }}>
                  {character.name}
                </span>
              ) : (
                <span style={{ 
                  backgroundColor: '#999',
                  color: 'white',
                  padding: '2px 6px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  fontWeight: 'bold',
                  flexShrink: 0
                }}>
                  角色{cue.cid}
                </span>
              );
            })()}
            
            {/* 发言内容 */}
            <Tooltip title={`"${cue.content}"`} placement="topLeft">
              <Text 
                strong 
                style={{ 
                  color: isSelected ? '#1890ff' : '#333',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                  flex: 1,
                  minWidth: 0
                }}
              >
                "{cue.content}"
              </Text>
            </Tooltip>
          </div>
          
          {/* 右侧："时"字 */}
          <Text style={{ color: isSelected ? '#1890ff' : '#666', fontSize: '14px', flexShrink: 0 }}>
            时
          </Text>
        </div>
      </div>
      <Space size="small">
        <Tooltip title={isSortMode ? "排序模式下不可编辑" : "编辑剧情线索"}>
          <Button
            type="text"
            icon={<EditOutlined />}
            size="small"
            disabled={isSortMode}
            onClick={(e) => {
              e.stopPropagation();
              onEdit(cue);
            }}
          />
        </Tooltip>
        <Tooltip title={isSortMode ? "排序模式下不可删除" : "删除剧情线索"}>
          <Button
            type="text"
            icon={<DeleteOutlined />}
            size="small"
            danger
            disabled={isSortMode}
            onClick={(e) => {
              e.stopPropagation();
              onDelete(cue);
            }}
          />
        </Tooltip>
      </Space>
    </div>
  );
};

// 可拖拽的台词列表项组件
interface SortableLineItemProps {
  line: SceneLineResponse;
  index: number;
  isSelected: boolean;
  isSortMode?: boolean;
  characters: SceneCharacterResponse[];
  onSelect: (lineId: number, isMultiSelect: boolean) => void;
  onEdit: (line: SceneLineResponse) => void;
  onDelete: (line: SceneLineResponse) => void;
}

const SortableLineItem: React.FC<SortableLineItemProps> = ({ 
  line, 
  index, 
  isSelected, 
  isSortMode = false,
  characters,
  onSelect, 
  onEdit,
  onDelete 
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: `line-${line.id}` });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    cursor: isSortMode ? 'pointer' : (isSelected ? 'grab' : 'pointer'),
    backgroundColor: isSelected ? '#e6f7ff' : '#fafafa',
    border: isSelected ? '1px solid #1890ff' : '1px solid #f0f0f0',
    borderRadius: '6px',
    padding: '12px',
    marginBottom: '8px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: '16px',
  };

  const handleClick = (e: React.MouseEvent) => {
    // 排序模式下不允许选中操作
    if (isSortMode) return;
    
    const isMultiSelect = e.ctrlKey || e.metaKey;
    onSelect(line.id, isMultiSelect);
  };

  // 根据cid找到对应的角色
  const character = characters.find(c => c.cid === line.cid);

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...(!isSortMode ? listeners : {})}
      onClick={handleClick}
      onMouseEnter={(e) => {
        if (!isSelected) {
          e.currentTarget.style.backgroundColor = '#f5f5f5';
        }
      }}
      onMouseLeave={(e) => {
        if (!isSelected) {
          e.currentTarget.style.backgroundColor = '#fafafa';
        }
      }}
    >
      <div style={{ display: 'flex', alignItems: 'center', flex: 1, minWidth: 0 }}>
        {isSortMode && (
          <div 
            {...listeners}
            style={{ 
              marginRight: '12px', 
              cursor: 'grab',
              color: '#999',
              fontSize: '14px'
            }}
          >
            <HolderOutlined />
          </div>
        )}
        <div style={{ 
          backgroundColor: isSelected ? '#1890ff' : '#1890ff',
          color: 'white',
          width: '20px',
          height: '20px',
          borderRadius: '2px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '12px',
          fontWeight: 'bold',
          marginRight: '12px'
        }}>
          {index + 1}
        </div>
        <div style={{ 
          flex: 1, 
          minWidth: 0, 
          display: 'flex', 
          alignItems: 'center', 
          gap: '8px'
        }}>
          {/* 头像 */}
          {character?.avatar ? (
            <img 
              src={character.avatar} 
              alt={character.name}
              style={{ 
                width: '24px', 
                height: '24px', 
                borderRadius: '50%',
                objectFit: 'cover',
                flexShrink: 0
              }}
            />
          ) : (
            <div style={{
              width: '24px',
              height: '24px',
              borderRadius: '50%',
              backgroundColor: '#ccc',
              flexShrink: 0
            }} />
          )}
          
          {/* 姓名 */}
          {(() => {
            const character = characters.find(c => c.cid === line.cid);
            return character ? (
              <span style={{ 
                backgroundColor: character.played ? '#52c41a' : '#1890ff',
                color: 'white',
                padding: '2px 6px',
                borderRadius: '4px',
                fontSize: '12px',
                fontWeight: 'bold',
                flexShrink: 0
              }}>
                {character.name}
              </span>
            ) : (
              <span style={{ 
                backgroundColor: '#999',
                color: 'white',
                padding: '2px 6px',
                borderRadius: '4px',
                fontSize: '12px',
                fontWeight: 'bold',
                flexShrink: 0
              }}>
                角色{line.cid}
              </span>
            );
          })()}
          
          {/* 发言主题 */}
          <Tooltip title={line.pv_topic || '未设置发言主题'} placement="topLeft">
            <Text 
              strong={isSelected} 
              style={{ 
                color: isSelected ? '#1890ff' : undefined,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                flex: 1,
                minWidth: 0
              }}
            >
                            {line.pv_topic || '未设置发言主题'}
            </Text>
          </Tooltip>
        </div>
      </div>
      <Space size="small">
        <Tooltip title={isSortMode ? "排序模式下不可编辑" : "编辑台词"}>
          <Button
            type="text"
            icon={<EditOutlined />}
            size="small"
            disabled={isSortMode}
            onClick={(e) => {
              e.stopPropagation();
              onEdit(line);
            }}
          />
        </Tooltip>
        <Tooltip title={isSortMode ? "排序模式下不可删除" : "删除台词"}>
          <Button
            type="text"
            icon={<DeleteOutlined />}
            size="small"
            danger
            disabled={isSortMode}
            onClick={(e) => {
              e.stopPropagation();
              onDelete(line);
            }}
          />
        </Tooltip>
      </Space>
    </div>
  );
};

const SceneCuesTab = forwardRef<SceneCuesTabRef, SceneCuesTabProps>(({ sceneId }, ref) => {
  const [cues, setCues] = useState<SceneCueResponse[]>([]);
  const [lines, setLines] = useState<SceneLineResponse[]>([]);
  const [characters, setCharacters] = useState<SceneCharacterResponse[]>([]);
  const [selectedCueId, setSelectedCueId] = useState<number | null>(null);
  const [selectedCueName, setSelectedCueName] = useState<string>('');
  const [cuesLoading, setCuesLoading] = useState(false);
  const [linesLoading, setLinesLoading] = useState(false);
  
  // 台词多选状态
  const [selectedLineIds, setSelectedLineIds] = useState<number[]>([]);
  
  // 排序模式相关状态
  const [isCueSortMode, setIsCueSortMode] = useState(false);
  const [isLineSortMode, setIsLineSortMode] = useState(false);
  
  // 拖动状态管理
  const [isDraggingLine, setIsDraggingLine] = useState(false);
  
  // 更改状态跟踪
  const [hasChanges, setHasChanges] = useState(false);
  
  // Drawer 状态管理
  const [cueDrawerVisible, setCueDrawerVisible] = useState(false);
  const [cueDrawerMode, setCueDrawerMode] = useState<'create' | 'edit'>('create');
  const [editingCue, setEditingCue] = useState<SceneCueResponse | undefined>();
  
  const [lineDrawerVisible, setLineDrawerVisible] = useState(false);
  const [lineDrawerMode, setLineDrawerMode] = useState<'create' | 'edit'>('create');
  const [editingLine, setEditingLine] = useState<SceneLineResponse | undefined>();

  // 拖拽排序传感器
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // 获取当前租户ID
  const getCurrentTenantId = (): number | null => {
    const tenantInfo = getGlobalTenantInfo();
    return tenantInfo?.id || null;
  };



  // 切换剧情线索排序模式
  const toggleCueSortMode = () => {
    if (!isCueSortMode && !cues.length) {
      message.warning('没有可排序的剧情线索');
      return;
    }
    setIsCueSortMode(!isCueSortMode);
  };

  // 切换台词排序模式
  const toggleLineSortMode = () => {
    if (!isLineSortMode) {
      if (!selectedCueId) {
        message.warning('请先选择一个剧情线索');
        return;
      }
      if (!lines.length) {
        message.warning('没有可排序的台词');
        return;
      }
    }
    setIsLineSortMode(!isLineSortMode);
  };

  // 获取场景角色列表（用于缓存）
  const fetchCharacters = async () => {
    if (!sceneId) return;
    
    const tenantId = getCurrentTenantId();
    if (!tenantId) {
      message.error('请先选择租户');
      return;
    }

    try {
      const response = await getSceneCharacters(parseInt(sceneId), tenantId);
      setCharacters(response);
    } catch (error) {
      console.warn('获取场景角色失败:', error);
      setCharacters([]);
    }
  };

  // 获取剧情线索列表
  const fetchCues = async () => {
    if (!sceneId) return;
    
    const tenantId = getCurrentTenantId();
    if (!tenantId) {
      message.error('请先选择租户');
      return;
    }

    setCuesLoading(true);
    try {
      const response = await getSceneCues(parseInt(sceneId), tenantId);
      setCues(response);
      
      // 如果有剧情线索数据且没有选中的剧情线索，自动选中第一个
      if (response.length > 0 && !selectedCueId) {
        setSelectedCueId(response[0].id);
        setSelectedCueName(getCueTitle(response[0], characters));
      }
    } catch (error) {
      showError(error, '获取剧情线索列表失败');
    } finally {
      setCuesLoading(false);
    }
  };

  // 获取台词列表
  const fetchLines = async (cueId: number) => {
    if (!sceneId) return;
    
    const tenantId = getCurrentTenantId();
    if (!tenantId) {
      message.error('请先选择租户');
      return;
    }

    setLinesLoading(true);
    try {
      const response = await getCueLines(cueId, tenantId);
      setLines(response);
    } catch (error) {
      showError(error, '获取台词列表失败');
    } finally {
      setLinesLoading(false);
    }
  };

  // 处理剧情线索选择
  const handleCueSelect = (cue: SceneCueResponse) => {
    if (selectedCueId === cue.id) {
      // 已选中，什么都不做
      return;
    }
    setSelectedCueId(cue.id);
    setSelectedCueName(getCueTitle(cue, characters));
    setLines([]); // 清空当前台词列表
  };

  // 刷新数据
  const handleRefresh = () => {
    fetchCues();
    if (selectedCueId) {
      fetchLines(selectedCueId);
    }
  };

  // 保存场景脚本
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const handleSaveScript = async () => {
    if (!sceneId) return;
    
    const tenantId = getCurrentTenantId();
    if (!tenantId) {
      message.error('请先选择租户');
      return;
    }

    try {
      await saveSceneScript(parseInt(sceneId), tenantId);
      setHasChanges(false);
      
      // 延迟显示成功消息以避免 React 18 并发模式警告
      setTimeout(() => {
        message.success('场景脚本保存成功');
      }, 0);
    } catch (error) {
      showError(error, '保存场景脚本失败');
    }
  };

  // 处理台词选择
  const handleLineSelect = (lineId: number, isMultiSelect: boolean) => {
    if (isMultiSelect) {
      // 多选模式：Ctrl/Cmd + 点击，切换选中状态
      setSelectedLineIds(prev => {
        if (prev.includes(lineId)) {
          return prev.filter(id => id !== lineId);
        } else {
          return [...prev, lineId];
        }
      });
    } else {
      // 普通点击：如果已选中则取消选中，否则选中
      setSelectedLineIds(prev => {
        if (prev.includes(lineId)) {
          return prev.filter(id => id !== lineId);
        } else {
          return [lineId];
        }
      });
    }
  };

  // 处理拖拽开始
  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    if (active.id.toString().startsWith('line-') && !isLineSortMode) {
      setIsDraggingLine(true);
    }
  };

  // 处理拖拽结束
  const handleDragEnd = async (event: DragEndEvent) => {
    // 重置拖拽状态
    setIsDraggingLine(false);
    const { active, over } = event;

    if (!active.id || !over?.id || !sceneId) return;

    const tenantId = getCurrentTenantId();
    if (!tenantId) return;

    // 解析拖拽ID，提取类型和真实ID
    const parseId = (id: string | number) => {
      const idStr = id.toString();
      if (idStr.startsWith('cue-')) {
        return { type: 'cue', id: parseInt(idStr.replace('cue-', '')) };
      } else if (idStr.startsWith('line-')) {
        return { type: 'line', id: parseInt(idStr.replace('line-', '')) };
      }
      return { type: 'unknown', id: 0 };
    };

    const activeInfo = parseId(active.id);
    const overInfo = parseId(over.id);

    // 检查是否是剧情线索拖拽
    if (activeInfo.type === 'cue' && overInfo.type === 'cue') {
      // 剧情线索之间的拖拽排序（只在剧情线索排序模式下执行）
      if (isCueSortMode && activeInfo.id !== overInfo.id) {
        const oldIndex = cues.findIndex((cue) => cue.id === activeInfo.id);
        const newIndex = cues.findIndex((cue) => cue.id === overInfo.id);

        const newCues = arrayMove(cues, oldIndex, newIndex);
        setCues(newCues);
        
        // 立即调用API更新排序
        try {
          const orderData: SceneCueBatchOrderRequest = {
            tenant_id: tenantId,
            sid: parseInt(sceneId),
            cues: newCues.map((cue, index) => ({
              id: cue.id,
              priority: index + 1
            }))
          };
          
          await batchUpdateSceneCueOrder(orderData);
          
          // 标记为已更改
          setHasChanges(true);
          
          // 延迟显示成功消息以避免 React 18 并发模式警告
          setTimeout(() => {
            message.success('剧情线索排序更新成功');
          }, 0);
        } catch (error) {
          // 如果API调用失败，恢复原始顺序
          setCues(cues);
          showError(error, '剧情线索排序更新失败');
        }
      }
    } else if (activeInfo.type === 'line') {
      // 台词拖拽处理：区分移动操作和排序操作
      const activeLine = lines.find(l => l.id === activeInfo.id);
      
      if (activeLine) {
        // 拖拽到剧情线索上 = 移动操作（非排序模式下允许）
        if (overInfo.type === 'cue' && !isLineSortMode) {
          const targetCue = cues.find(cue => cue.id === overInfo.id);
          if (!targetCue) return;
          
          const targetCueId = targetCue.id;
          
          // 检查是否拖拽到当前剧情线索
          if (targetCueId === selectedCueId) {
            message.info('台词已在当前剧情线索中，无需移动');
            return;
          }
          
          // 获取要移动的台词ID列表（支持批量移动选中的台词）
          let lineIdsToMove: number[];
          if (selectedLineIds.includes(activeLine.id)) {
            // 如果拖拽的台词在选中列表中，移动所有选中的台词
            lineIdsToMove = selectedLineIds;
          } else {
            // 否则只移动当前拖拽的台词
            lineIdsToMove = [activeLine.id];
          }
          
          // 执行跨剧情线索移动操作
          try {
            const moveData: SceneLineBatchMoveRequest = {
              tenant_id: tenantId,
              target_cueid: targetCueId,
              line_ids: lineIdsToMove
            };
            
            const result = await batchMoveSceneLines(moveData);
            
            // 清空选中状态
            setSelectedLineIds([]);
            
            // 标记为已更改
            setHasChanges(true);
            
            // 延迟显示成功消息以避免 React 18 并发模式警告
            setTimeout(() => {
              message.success(`🔄 成功移动 ${result.success_count} 个台词到"${getCueTitle(targetCue, characters)}"`);
            }, 0);
            
            // 重新获取当前剧情线索的台词列表
            if (selectedCueId) {
              await fetchLines(selectedCueId);
            }
          } catch (error) {
            showError(error, '移动台词失败');
          }
        } 
        // 拖拽到台词上 = 排序操作（只在台词排序模式下执行）
        else if (overInfo.type === 'line' && isLineSortMode) {
          const overLine = lines.find(l => l.id === overInfo.id);
          if (overLine && activeInfo.id !== overInfo.id) {
            const oldIndex = lines.findIndex((l) => l.id === activeInfo.id);
            const newIndex = lines.findIndex((l) => l.id === overInfo.id);
            
            const newLines = arrayMove(lines, oldIndex, newIndex);
            setLines(newLines);
            
            // 立即调用API更新排序
            try {
              const orderData: SceneLineBatchOrderRequest = {
                tenant_id: tenantId,
                lines: newLines.map((line, index) => ({
                  id: line.id,
                  priority: index + 1
                }))
              };
              
              await batchUpdateSceneLineOrder(orderData);
              
              // 标记为已更改
              setHasChanges(true);
              
              // 延迟显示成功消息以避免 React 18 并发模式警告
              setTimeout(() => {
                message.success('📝 台词排序更新成功');
              }, 0);
            } catch (error) {
              // 如果API调用失败，恢复原始顺序
              setLines(lines);
              showError(error, '台词排序更新失败');
            }
          }
        }
      }
    }
  };

  // 删除剧情线索（占位函数）
  const handleDeleteCue = (cue: SceneCueResponse) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除剧情线索"${getCueTitle(cue, characters)}"吗？此操作不可撤销。`,
      okText: '确定',
      cancelText: '取消',
      okType: 'danger',
      onOk: async () => {
        try {
          const tenantId = getCurrentTenantId();
          if (!sceneId || !tenantId) return;
          
          const deleteData: SceneCueBatchDeleteRequest = {
            tenant_id: tenantId,
            sid: parseInt(sceneId),
            cue_ids: [cue.id]
          };
          
          await batchDeleteSceneCues(deleteData);
          
          // 如果删除的是当前选中的剧情线索，清空选中状态
          if (selectedCueId === cue.id) {
            setSelectedCueId(null);
            setSelectedCueName('');
            setLines([]);
          }
          
          // 重新获取剧情线索列表
          await fetchCues();
          
          // 标记为已更改
          setHasChanges(true);
          
          // 延迟显示成功消息以避免 React 18 并发模式警告
          setTimeout(() => {
            message.success('剧情线索删除成功');
          }, 0);
        } catch (error) {
          showError(error, '删除剧情线索失败');
        }
      }
    });
  };

  // 编辑剧情线索
  const handleEditCue = (cue: SceneCueResponse) => {
    setCueDrawerMode('edit');
    setEditingCue(cue);
    setCueDrawerVisible(true);
  };

  // 编辑台词
  const handleEditLine = (line: SceneLineResponse) => {
    setLineDrawerMode('edit');
    setEditingLine(line);
    setLineDrawerVisible(true);
  };

  // 删除台词
  const handleDeleteLine = (line: SceneLineResponse) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要从"${selectedCueName}"中删除台词"${line.pv_topic || '未设置发言主题'}"吗？此操作不可撤销。`,
      okText: '确定',
      cancelText: '取消',
      okType: 'danger',
      onOk: async () => {
        try {
          const tenantId = getCurrentTenantId();
          if (!tenantId || !selectedCueId) return;
          
          await deleteSceneLine(line.id, tenantId);
          
          // 重新获取台词列表
          await fetchLines(selectedCueId);
          
          // 标记为已更改
          setHasChanges(true);
          
          // 延迟显示成功消息以避免 React 18 并发模式警告
          setTimeout(() => {
            message.success('台词删除成功');
          }, 0);
        } catch (error) {
          showError(error, '删除台词失败');
        }
      }
    });
  };

  // 添加剧情线索
  const handleAddCue = () => {
    setCueDrawerMode('create');
    setEditingCue(undefined);
    setCueDrawerVisible(true);
  };

  // 添加台词
  const handleAddLine = () => {
    if (!selectedCueId) {
      message.warning('请先选择一个剧情线索');
      return;
    }
    setLineDrawerMode('create');
    setEditingLine(undefined);
    setLineDrawerVisible(true);
  };

  // Drawer 回调函数
  const handleCueDrawerClose = () => {
    setCueDrawerVisible(false);
    setEditingCue(undefined);
  };

  const handleCueDrawerSuccess = () => {
    fetchCues();
    setHasChanges(true);
  };

  const handleLineDrawerClose = () => {
    setLineDrawerVisible(false);
    setEditingLine(undefined);
  };

  const handleLineDrawerSuccess = () => {
    if (selectedCueId) {
      fetchLines(selectedCueId);
    }
    setHasChanges(true);
  };

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    hasUnsavedChanges: () => hasChanges,
    saveContent: async () => {
      await handleSaveScript();
    },
    resetToInitial: () => {
      setHasChanges(false);
    }
  }), [hasChanges, handleSaveScript]);

  // 初始化加载数据
  useEffect(() => {
    fetchCharacters();
    fetchCues();
  }, [sceneId]);

  // 当选中的剧情线索改变时，获取台词列表
  useEffect(() => {
    if (selectedCueId) {
      fetchLines(selectedCueId);
      // 清空台词选中状态
      setSelectedLineIds([]);
    }
  }, [selectedCueId]);

  // 监听租户切换事件
  useEffect(() => {
    const handleTenantChange = () => {
      if (hasChanges) {
        const confirmed = window.confirm('您有未保存的更改，是否先保存？');
        if (confirmed) {
          handleSaveScript();
        }
      }
      setCues([]);
      setLines([]);
      setCharacters([]);
      setSelectedCueId(null);
      setSelectedCueName('');
      setHasChanges(false);
      fetchCharacters();
      fetchCues();
    };

    window.addEventListener('globalTenantChanged', handleTenantChange);
    return () => {
      window.removeEventListener('globalTenantChanged', handleTenantChange);
    };
  }, [sceneId, hasChanges, handleSaveScript]);

  // 监听页面离开事件
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasChanges) {
        e.preventDefault();
        e.returnValue = '您有未保存的更改，确定要离开吗？';
        return '您有未保存的更改，确定要离开吗？';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [hasChanges]);

  if (!sceneId) {
    return (
      <div style={{ 
        height: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '40px'
      }}>
        <Empty
          description="场景ID不可用"
          style={{ color: '#999' }}
        />
      </div>
    );
  }

  const currentTenantId = getCurrentTenantId();
  if (!currentTenantId) {
    return (
      <div style={{ 
        height: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '40px'
      }}>
        <Empty
          description="请先在顶部导航栏选择租户"
          style={{ color: '#999' }}
        />
      </div>
    );
  }

  return (
    <div style={{ height: '100%', padding: '0px' }}>
      <DndContext 
        sensors={sensors} 
        collisionDetection={closestCenter} 
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
      >
        <Row gutter={16} style={{ height: '100%' }}>
          {/* 左侧分栏：剧情线索列表 */}
          <Col span={10} style={{ height: '100%' }}>
            <Card
              title={
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <span>
                    <FileTextOutlined style={{ marginRight: 8 }} />
                    {isCueSortMode ? '剧情排序' : '剧情'}
                  </span>
                  <div style={{ display: 'flex', gap: '4px' }}>
                    <Tooltip title="添加剧情线索">
                      <Button
                        type="primary"
                        icon={<PlusOutlined />}
                        onClick={handleAddCue}
                        disabled={isCueSortMode}
                        size="small"
                      />
                    </Tooltip>
                    <Tooltip title="刷新列表">
                      <Button
                        type="primary"
                        icon={<ReloadOutlined />}
                        onClick={handleRefresh}
                        loading={cuesLoading}
                        disabled={isCueSortMode}
                        size="small"
                      />
                    </Tooltip>
                    <Tooltip title="保存场景脚本">
                      <Button
                        type="primary"
                        icon={<SaveOutlined />}
                        style={hasChanges ? { backgroundColor: '#ff4d4f', borderColor: '#ff4d4f' } : {}}
                        onClick={handleSaveScript}
                        disabled={isCueSortMode}
                        size="small"
                      />
                    </Tooltip>
                    <Tooltip title={isCueSortMode ? "退出排序" : "调整排序"}>
                      <Button
                        type={isCueSortMode ? "primary" : "text"}
                        icon={<DragOutlined />}
                        onClick={toggleCueSortMode}
                        size="small"
                      />
                    </Tooltip>
                  </div>
                </div>
              }
              style={{ height: '100%' }}
              styles={{ 
                body: {
                  height: 'calc(100% - 57px)',
                  overflow: 'hidden',
                  padding: '12px'
                }
              }}
            >
              <Spin spinning={cuesLoading}>
                {cues.length === 0 ? (
                  <Empty
                    description="暂无剧情线索数据"
                    style={{ marginTop: '60px' }}
                  />
                ) : (
                  isCueSortMode ? (
                    <SortableContext 
                      items={cues.map(cue => `cue-${cue.id}`)} 
                      strategy={verticalListSortingStrategy}
                    >
                      <div>
                        {cues.map((cue, index) => (
                          <SortableCueItem
                            key={cue.id}
                            cue={cue}
                            index={index}
                            isSelected={selectedCueId === cue.id}
                            isSortMode={isCueSortMode}
                            characters={characters}
                            onSelect={handleCueSelect}
                            onEdit={handleEditCue}
                            onDelete={handleDeleteCue}
                          />
                        ))}
                      </div>
                    </SortableContext>
                  ) : (
                    <div>
                      {cues.map((cue, index) => (
                        <DroppableCueItem
                          key={cue.id}
                          cue={cue}
                          index={index}
                          isSelected={selectedCueId === cue.id}
                          isSortMode={isCueSortMode}
                          isDraggingLine={isDraggingLine}
                          selectedCueId={selectedCueId}
                          characters={characters}
                          onSelect={handleCueSelect}
                          onEdit={handleEditCue}
                          onDelete={handleDeleteCue}
                        />
                      ))}
                    </div>
                  )
                )}
              </Spin>
            </Card>
          </Col>

          {/* 右侧分栏：台词列表 */}
          <Col span={14} style={{ height: '100%' }}>
            <Card
              title={
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <span>
                    <CommentOutlined style={{ marginRight: 8 }} />
                    {isLineSortMode ? "台词排序" : '台词'
                    }
                  </span>
                  <div style={{ display: 'flex', gap: '4px' }}>
                    {selectedLineIds.length > 0 && !isLineSortMode && (
                      <Tooltip title={`已选中 ${selectedLineIds.length} 个台词`}>
                        <Button
                          type="text"
                          size="small"
                          style={{ color: '#1890ff' }}
                        >
                          已选中 {selectedLineIds.length} 项
                        </Button>
                      </Tooltip>
                    )}
                    <Tooltip title="添加台词">
                      <Button
                        type="primary"
                        icon={<PlusOutlined />}
                        onClick={handleAddLine}
                        disabled={!selectedCueId || isLineSortMode}
                        size="small"
                      />
                    </Tooltip>
                    <Tooltip title={isLineSortMode ? "退出排序" : "调整排序"}>
                      <Button
                        type={isLineSortMode ? "primary" : "text"}
                        icon={<DragOutlined />}
                        onClick={toggleLineSortMode}
                        disabled={!selectedCueId}
                        size="small"
                      />
                    </Tooltip>
                  </div>
                </div>
              }
              style={{ height: '100%' }}
              styles={{ 
                body: {
                  height: 'calc(100% - 57px)',
                  overflow: 'auto',
                  padding: '12px'
                }
              }}
            >
              <Spin spinning={linesLoading}>
                {!selectedCueId ? (
                  <Empty
                    description="请选择左侧的剧情线索查看对应的台词列表"
                    style={{ marginTop: '60px' }}
                  />
                ) : lines.length === 0 ? (
                  <Empty
                    description="该剧情线索暂无台词数据"
                    style={{ marginTop: '60px' }}
                  />
                ) : (
                  <div>
                    <div style={{ marginBottom: '12px', fontSize: '12px', color: '#666' }}>
                      {isLineSortMode 
                        ? '排序模式：可拖拽台词可调整顺序。' 
                        : '提示：可拖拽台词到左侧剧情线索可移动，按住 Ctrl/Cmd 键可多选。'
                      }
                    </div>
                    <SortableContext 
                      items={lines.map(l => `line-${l.id}`)} 
                      strategy={verticalListSortingStrategy}
                    >
                      <div>
                        {lines.map((line, index) => (
                          <SortableLineItem
                            key={line.id}
                            line={line}
                            index={index}
                            isSelected={selectedLineIds.includes(line.id)}
                            isSortMode={isLineSortMode}
                            characters={characters}
                            onSelect={handleLineSelect}
                            onEdit={handleEditLine}
                            onDelete={handleDeleteLine}
                          />
                        ))}
                      </div>
                    </SortableContext>
                  </div>
                )}
              </Spin>
            </Card>
          </Col>
        </Row>
      </DndContext>
      
      {/* 剧情线索表单抽屉 */}
      <CueFormDrawer
        visible={cueDrawerVisible}
        mode={cueDrawerMode}
        sceneId={sceneId}
        cue={editingCue}
        characters={characters}
        onClose={handleCueDrawerClose}
        onSuccess={handleCueDrawerSuccess}
      />
      
      {/* 台词表单抽屉 */}
      <LineFormDrawer
        visible={lineDrawerVisible}
        mode={lineDrawerMode}
        sceneId={sceneId}
        cueId={selectedCueId || undefined}
        line={editingLine}
        onClose={handleLineDrawerClose}
        onSuccess={handleLineDrawerSuccess}
      />
    </div>
  );
});

SceneCuesTab.displayName = 'SceneCuesTab';

export default SceneCuesTab; 