import { get } from './api';
import { saveTeacherInfo } from './exercise';

// 理论模块接口
export interface ModuleResponse {
  name: string;
}

// 理论框架接口
export interface FrameworkResponse {
  name: string;
  logo?: string | null;
  module_list: ModuleResponse[];
}

// 作答指南接口
export interface GuideResponse {
  title: string;
  details: string;
}

// 问题响应接口
export interface QuestionResponse {
  id: number;
  title: string;
  bgtext?: string | null;
  bgvideo?: string | null;
  draft?: string | null;
  answer?: string | null;
  comment?: string | null;
  framework_list?: FrameworkResponse[];
  guide_list?: GuideResponse[];
}

// 单元响应接口
export interface UnitResponse {
  id: number;
  name: string;
  bgtext?: string | null;
  bgvideo?: string | null;
  question_list?: QuestionResponse[];
}

// 作业单基本信息响应接口
export interface WorksheetBasicResponse {
  title: string;
  pic?: string | null;
  intro?: string | null;
  duration?: number | null;
  bgtext?: string | null;
  bgvideo?: string | null;
  report?: string | null;
  btime?: string | null;
  stime?: string | null;
  utime?: string | null;
  status: number;
  eid: number;
  elid?: number | null;
  tid?: number | null;
  tname?: string | null;
  tavatar?: string | null;
}

// 作业单单元列表响应接口
export interface WorksheetUnitsResponse {
  unit_list: UnitResponse[];
}

// 作业单详情响应接口（保留兼容性）
export interface WorksheetDetailResponse {
  title: string;
  pic?: string | null;
  intro?: string | null;
  duration?: number | null;
  bgtext?: string | null;
  bgvideo?: string | null;
  report?: string | null;
  btime?: string | null;
  stime?: string | null;
  utime?: string | null;
  status: number;
  unit_list: UnitResponse[];
  eid: number;
  elid?: number | null;
}

// 单元问题响应接口
export interface UnitQuestionsResponse {
  question_list: QuestionResponse[];
}

// 练习作业单响应接口
export interface ExerciseWorksheetResponse {
  title: string;
  type: number;
  pic?: string | null;
  intro?: string | null;
  duration?: number | null;
  bgtext?: string | null;
  bgvideo?: string | null;
  report?: string | null;
  btime?: string | null;
  stime?: string | null;
  utime?: string | null;
  status: number; // 练习状态（0：未开始；1：练习中；2：已提交）
  unit_list: UnitResponse[];
}

/**
 * 获取练习作业单详情
 * @param exerciseId 练习ID
 * @param classId 班级ID
 * @returns Promise<ExerciseWorksheetResponse> 作业单详情
 */
export const getExerciseWorksheet = async (
  exerciseId: number,
  classId: number
): Promise<ExerciseWorksheetResponse> => {
  try {
    const response = await get<ExerciseWorksheetResponse>(`/exercise/${exerciseId}/worksheet`,{ cid: classId });
    return response;
  } catch (error) {
    console.error('获取作业单详情失败:', error);
    throw error;
  }
};

/**
 * 获取作业单基本信息
 * @param worksheetId 作业单ID
 * @param classId 班级ID
 * @returns Promise<WorksheetBasicResponse> 作业单基本信息
 */
export const getWorksheetBasic = async (
  worksheetId: number,
  classId: number
): Promise<WorksheetBasicResponse> => {
  try {
    const response = await get<WorksheetBasicResponse>(`/worksheet/${worksheetId}`, { class_id: classId });
    
    // 如果返回了老师信息，异步保存到本地存储（包含头像缓存）
    if (response.tid && response.tname) {
      // 不等待头像缓存完成，避免阻塞主流程
      saveTeacherInfo({
        tid: response.tid,
        tname: response.tname,
        tavatar: response.tavatar
      }).catch(error => {
        console.error('缓存老师信息失败:', error);
      });
    }
    
    return response;
  } catch (error) {
    console.error('获取作业单基本信息失败:', error);
    throw error;
  }
};

/**
 * 获取作业单单元列表
 * @param worksheetId 作业单ID
 * @returns Promise<WorksheetUnitsResponse> 作业单单元列表
 */
export const getWorksheetUnits = async (
  worksheetId: number
): Promise<WorksheetUnitsResponse> => {
  try {
    const response = await get<WorksheetUnitsResponse>(`/worksheet/${worksheetId}/units`);
    return response;
  } catch (error) {
    console.error('获取作业单单元列表失败:', error);
    throw error;
  }
};

/**
 * 并行获取作业单基本信息和单元列表
 * @param worksheetId 作业单ID
 * @param classId 班级ID
 * @returns Promise<{basic: WorksheetBasicResponse, units: WorksheetUnitsResponse}>
 */
export const getWorksheetData = async (
  worksheetId: number,
  classId: number
): Promise<{basic: WorksheetBasicResponse, units: WorksheetUnitsResponse}> => {
  try {
    const [basic, units] = await Promise.all([
      getWorksheetBasic(worksheetId, classId),
      getWorksheetUnits(worksheetId)
    ]);
    
    return { basic, units };
  } catch (error) {
    console.error('获取作业单数据失败:', error);
    throw error;
  }
};

/**
 * 获取作业单详情（兼容旧版本API）
 * @param worksheetId 作业单ID
 * @param classId 班级ID
 * @returns Promise<WorksheetDetailResponse> 作业单详情
 */
export const getWorksheetDetail = async (
  worksheetId: number,
  classId: number
): Promise<WorksheetDetailResponse> => {
  try {
    const { basic, units } = await getWorksheetData(worksheetId, classId);
    
    // 合并基本信息和单元列表，保持向后兼容
    const detail: WorksheetDetailResponse = {
      title: basic.title,
      pic: basic.pic,
      intro: basic.intro,
      duration: basic.duration,
      bgtext: basic.bgtext,
      bgvideo: basic.bgvideo,
      report: basic.report,
      btime: basic.btime,
      stime: basic.stime,
      utime: basic.utime,
      status: basic.status,
      eid: basic.eid,
      elid: basic.elid,
      unit_list: units.unit_list
    };
    
    return detail;
  } catch (error) {
    console.error('获取作业单详情失败:', error);
    throw error;
  }
};

/**
 * 获取作业单中单元下的所有问题信息
 * @param worksheetId 作业单ID
 * @param unitId 单元ID
 * @param elid 练习情况ID
 * @returns Promise<UnitQuestionsResponse> 单元问题列表
 */
export const getUnitQuestions = async (
  worksheetId: number,
  unitId: number,
  elid: number
): Promise<UnitQuestionsResponse> => {
  try {
    const response = await get<UnitQuestionsResponse>(`/worksheet/${worksheetId}/unit/${unitId}/questions`, { elid });
    return response;
  } catch (error) {
    console.error('获取单元问题列表失败:', error);
    throw error;
  }
};

// AI点评请求接口
export interface QuestionCommentRequest {
  elid: number;
  qid: number;
  answer: string;
}

// 重新练习请求接口
export interface QuestionRetryRequest {
  elid: number;
  qid: number;
}

// 问题草稿项接口
export interface QuestionDraftItem {
  qid: number;
  draft: string;
}

// 批量更新问题草稿请求接口
export interface QuestionDraftBatchRequest {
  elid: number;
  list: QuestionDraftItem[];
}

/**
 * 获取AI老师对问题作答的流式点评
 * @param request 点评请求信息
 * @param callbacks 回调函数对象
 * @returns Promise<void>
 */
export const getQuestionComment = async (
  request: QuestionCommentRequest,
  callbacks: {
    onChunk: (chunk: string) => void;
    onComplete: () => void;
    onError: (error: Error) => void;
  }
): Promise<void> => {
  try {
    const { user } = await import('../utils/authStore').then(m => m.useAuthStore.getState());
    
    const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/worksheet/question/comment`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${user?.token}`,
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('无法获取响应流');
    }

    const decoder = new TextDecoder();
    let buffer = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          break;
        }

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6).trim();
            if (data === '[DONE]') {
              callbacks.onComplete();
              return;
            }
            if (data) {
              callbacks.onChunk(data);
            }
          }
        }
      }
      
      // 处理剩余的buffer
      if (buffer.trim()) {
        callbacks.onChunk(buffer);
      }
      
      callbacks.onComplete();
    } finally {
      reader.releaseLock();
    }
  } catch (error) {
    console.error('获取AI点评失败:', error);
    callbacks.onError(error instanceof Error ? error : new Error('获取AI点评失败'));
  }
};

/**
 * 重新练习问题
 * @param request 重新练习请求信息
 * @returns Promise<{message: string}> 重新练习响应
 */
export const retryQuestion = async (
  request: QuestionRetryRequest
): Promise<{message: string}> => {
  try {
    const { post } = await import('./api');
    const response = await post<{message: string}>('/worksheet/question/retry', request);
    return response;
  } catch (error) {
    console.error('重新练习失败:', error);
    throw error;
  }
};

/**
 * 批量更新问题作答草稿
 * @param request 批量更新请求信息
 * @returns Promise<{message: string}> 批量更新响应
 */
export const batchUpdateQuestionDrafts = async (
  request: QuestionDraftBatchRequest
): Promise<{message: string}> => {
  try {
    const { put } = await import('./api');
    const response = await put<{message: string}>('/worksheet/question/answers', request);
    return response;
  } catch (error) {
    console.error('批量更新草稿失败:', error);
    throw error;
  }
};

/**
 * 保存单个问题草稿
 * @param elid 练习情况ID
 * @param qid 问题ID
 * @param draft 草稿内容
 * @returns Promise<{message: string}> 保存响应
 */
export const saveSingleQuestionDraft = async (
  elid: number,
  qid: number,
  draft: string
): Promise<{message: string}> => {
  const request: QuestionDraftBatchRequest = {
    elid,
    list: [{ qid, draft }]
  };
  return batchUpdateQuestionDrafts(request);
};