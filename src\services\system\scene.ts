import { get, post, put, del } from '../api';

// 场景相关类型定义
export interface SceneCreate {
  tenant_id: number;
  title: string;
  intro?: string;
  duration?: number;
  version?: string;
  notes?: string;
  active?: number;
  published?: number;
}

export interface SceneUpdate {
  title?: string;
  intro?: string;
  duration?: number;
  version?: string;
  notes?: string;
  active?: number;
  published?: number;
  pv_scripts?: string;
  bgtext?: string;
  pic?: string;
}

export interface SceneResponse {
  id: number;
  tenant_id: number;
  eid: number;
  title: string;
  intro?: string;
  duration?: number;
  version?: string;
  notes?: string;
  active: number;
  published: number;
  pv_scripts?: string;
  bgtext?: string;
  pic?: string;
  ctime: string;
}

export interface SceneListResponse {
  total: number;
  items: SceneResponse[];
}

export interface GetScenesParams {
  tenant_id: number;
  skip?: number;
  limit?: number;
  title?: string;
  published?: number;
  active?: number;
  sort_by?: string;
  sort_order?: string;
  start_time?: string;
  end_time?: string;
}

// 场景人物角色相关类型定义
export interface SceneCharacterResponse {
  id: number;
  cid: number;
  name: string;
  gender: string;
  avatar?: string;
  profile?: string;
  played: number;
}

export interface SceneCharacterCreate {
  tenant_id: number;
  sid: number;
  cid: number;
  played?: number;
  priority?: number;
}

export interface SceneCharacterUpdate {
  tenant_id: number;
  played?: number;
  priority?: number;
}

export interface SceneCharacterOrderItem {
  id: number;
  priority: number;
}

export interface SceneCharacterBatchOrderRequest {
  tenant_id: number;
  sid: number;
  characters: SceneCharacterOrderItem[];
}

export interface SceneCharacterBatchOrderResponse {
  success_count: number;
  total_count: number;
  characters: SceneCharacterResponse[];
}

export interface SceneCharacterBatchDeleteRequest {
  tenant_id: number;
  sid: number;
  character_ids: number[];
}

export interface SceneCharacterBatchDeleteResponse {
  success_count: number;
  deleted_ids: number[];
}

// 场景指南相关类型定义
export interface SceneGuideResponse {
  id: number;
  sid: number;
  tenant_id: number;
  title: string;
  details: string;
  priority: number;
  ctime: string;
}

export interface SceneGuideCreate {
  sid: number;
  tenant_id: number;
  title: string;
  details: string;
  priority?: number;
}

export interface SceneGuideUpdate {
  title?: string;
  details?: string;
  priority?: number;
}

export interface SceneGuideListResponse {
  total: number;
  items: SceneGuideResponse[];
}

export interface SceneGuideOrderItem {
  id: number;
  priority: number;
}

export interface SceneGuideBatchOrderRequest {
  tenant_id: number;
  guides: SceneGuideOrderItem[];
}

export interface SceneGuideBatchOrderResponse {
  success_count: number;
  total_count: number;
  guides: SceneGuideResponse[];
}

// 场景剧情线索相关类型定义
export interface SceneCueResponse {
  id: number;
  tenant_id: number;
  sid: number;
  cid: number;
  content: string;
  serial: number;
  priority: number;
  active: number;
  ctime: string;
}

export interface SceneCueCreate {
  tenant_id: number;
  sid: number;
  cid: number;
  content: string;
  serial: number;
}

export interface SceneCueUpdate {
  content?: string;
  serial?: number;
}

export interface SceneCueOrderItem {
  id: number;
  priority: number;
}

export interface SceneCueBatchOrderRequest {
  tenant_id: number;
  sid: number;
  cues: SceneCueOrderItem[];
}

export interface SceneCueBatchOrderResponse {
  success_count: number;
  total_count: number;
  cues: SceneCueResponse[];
}

export interface SceneCueBatchDeleteRequest {
  tenant_id: number;
  sid: number;
  cue_ids: number[];
}

export interface SceneCueBatchDeleteResponse {
  success_count: number;
  deleted_ids: number[];
}

// 场景台词相关类型定义
export interface SceneLineResponse {
  id: number;
  tenant_id: number;
  cueid: number;
  cid: number;
  pv_topic?: string;
  pv_ability?: string;
  pv_restriction?: string;
  ctime: string;
}

export interface SceneLineCreate {
  tenant_id: number;
  cueid: number;
  cid: number;
  pv_topic?: string;
  pv_ability?: string;
  pv_restriction?: string;
}

export interface SceneLineUpdate {
  cid?: number;
  pv_topic?: string;
  pv_ability?: string;
  pv_restriction?: string;
}

export interface SceneLineOrderItem {
  id: number;
  priority: number;
}

export interface SceneLineBatchOrderRequest {
  tenant_id: number;
  lines: SceneLineOrderItem[];
}

export interface SceneLineBatchOrderResponse {
  success_count: number;
  total_count: number;
  lines: SceneLineResponse[];
}

export interface SceneLineBatchMoveRequest {
  tenant_id: number;
  target_cueid: number;
  line_ids: number[];
}

export interface SceneLineBatchMoveResponse {
  success_count: number;
}

export interface SceneLineBatchDeleteRequest {
  tenant_id: number;
  cue_id: number;
  line_ids: number[];
}

export interface SceneLineBatchDeleteResponse {
  success_count: number;
  deleted_ids: number[];
}

export interface PicUploadURL {
  upload_url: string;
  file_path: string;
  file_url: string;
  expires_in: number;
}

export interface PicDeleteRequest {
  file_path: string;
}

// 获取场景列表
export const getScenes = async (params: GetScenesParams): Promise<SceneListResponse> => {
  const response = await get<SceneListResponse>('/sys/scene', params);
  return response;
};

// 创建场景
export const createScene = async (data: SceneCreate): Promise<SceneResponse> => {
  const response = await post<SceneResponse>('/sys/scene', data);
  return response;
};

// 获取场景信息
export const getScene = async (id: number): Promise<SceneResponse> => {
  const response = await get<SceneResponse>(`/sys/scene/${id}`);
  return response;
};

// 更新场景
export const updateScene = async (id: number, data: SceneUpdate): Promise<SceneResponse> => {
  const response = await put<SceneResponse>(`/sys/scene/${id}`, data);
  return response;
};

// 删除场景
export const deleteScene = async (id: number): Promise<{ message: string }> => {
  const response = await del<{ message: string }>(`/sys/scene/${id}`);
  return response;
};

// 场景人物角色相关API
export const getSceneCharacters = async (sceneId: number, tenantId: number): Promise<SceneCharacterResponse[]> => {
  const response = await get<SceneCharacterResponse[]>(`/sys/scene/${sceneId}/characters`, { tenant_id: tenantId });
  return response;
};

export const createSceneCharacter = async (data: SceneCharacterCreate): Promise<SceneCharacterResponse> => {
  const response = await post<SceneCharacterResponse>('/sys/scene/character', data);
  return response;
};

export const updateSceneCharacter = async (id: number, data: SceneCharacterUpdate): Promise<SceneCharacterResponse> => {
  const response = await put<SceneCharacterResponse>(`/sys/scene/character/${id}`, data);
  return response;
};

export const batchUpdateSceneCharacterOrder = async (data: SceneCharacterBatchOrderRequest): Promise<SceneCharacterBatchOrderResponse> => {
  const response = await put<SceneCharacterBatchOrderResponse>('/sys/scene/characters/batch/order', data);
  return response;
};

export const batchDeleteSceneCharacters = async (data: SceneCharacterBatchDeleteRequest): Promise<SceneCharacterBatchDeleteResponse> => {
  const response = await del<SceneCharacterBatchDeleteResponse>('/sys/scene/characters/batch', { data });
  return response;
};

// 场景指南相关API
export const getSceneGuides = async (sceneId: number, tenantId: number, params?: {
  skip?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: string;
}): Promise<SceneGuideListResponse> => {
  const response = await get<SceneGuideListResponse>(`/sys/scene/${sceneId}/guides`, {
    tenant_id: tenantId,
    ...params
  });
  return response;
};

export const createSceneGuide = async (data: SceneGuideCreate): Promise<SceneGuideResponse> => {
  const response = await post<SceneGuideResponse>('/sys/scene/guides', data);
  return response;
};

export const updateSceneGuide = async (id: number, data: SceneGuideUpdate, tenantId: number): Promise<SceneGuideResponse> => {
  const response = await put<SceneGuideResponse>(`/sys/scene/guides/${id}`, data, { params: { tenant_id: tenantId } });
  return response;
};

export const deleteSceneGuide = async (id: number, tenantId: number): Promise<void> => {
  await del(`/sys/scene/guides/${id}`, { params: { tenant_id: tenantId } });
};

export const batchUpdateSceneGuideOrder = async (data: SceneGuideBatchOrderRequest): Promise<SceneGuideBatchOrderResponse> => {
  const response = await put<SceneGuideBatchOrderResponse>('/sys/scene/guides/batch/order', data);
  return response;
};

// 场景剧情线索相关API
export const getSceneCues = async (sceneId: number, tenantId: number): Promise<SceneCueResponse[]> => {
  const response = await get<SceneCueResponse[]>(`/sys/scene/${sceneId}/cues`, { tenant_id: tenantId });
  return response;
};

export const createSceneCue = async (data: SceneCueCreate): Promise<SceneCueResponse> => {
  const response = await post<SceneCueResponse>('/sys/scene/cues', data);
  return response;
};

export const updateSceneCue = async (id: number, data: SceneCueUpdate, tenantId: number): Promise<SceneCueResponse> => {
  const response = await put<SceneCueResponse>(`/sys/scene/cues/${id}`, data, { params: { tenant_id: tenantId } });
  return response;
};

export const deleteSceneCue = async (id: number, tenantId: number): Promise<void> => {
  await del(`/sys/scene/cues/${id}`, { params: { tenant_id: tenantId } });
};

export const batchUpdateSceneCueOrder = async (data: SceneCueBatchOrderRequest): Promise<SceneCueBatchOrderResponse> => {
  const response = await put<SceneCueBatchOrderResponse>('/sys/scene/cues/batch/order', data);
  return response;
};

export const batchDeleteSceneCues = async (data: SceneCueBatchDeleteRequest): Promise<SceneCueBatchDeleteResponse> => {
  const response = await del<SceneCueBatchDeleteResponse>('/sys/scene/cues/batch', { data });
  return response;
};

// 场景台词相关API
export const getCueLines = async (cueId: number, tenantId: number): Promise<SceneLineResponse[]> => {
  const response = await get<SceneLineResponse[]>(`/sys/scene/cues/${cueId}/lines`, { tenant_id: tenantId });
  return response;
};

export const createSceneLine = async (data: SceneLineCreate): Promise<SceneLineResponse> => {
  const response = await post<SceneLineResponse>('/sys/scene/lines', data);
  return response;
};

export const updateSceneLine = async (id: number, data: SceneLineUpdate, tenantId: number): Promise<SceneLineResponse> => {
  const response = await put<SceneLineResponse>(`/sys/scene/lines/${id}`, data, { params: { tenant_id: tenantId } });
  return response;
};

export const deleteSceneLine = async (id: number, tenantId: number): Promise<void> => {
  await del(`/sys/scene/lines/${id}`, { params: { tenant_id: tenantId } });
};

export const batchUpdateSceneLineOrder = async (data: SceneLineBatchOrderRequest): Promise<SceneLineBatchOrderResponse> => {
  const response = await put<SceneLineBatchOrderResponse>('/sys/scene/lines/batch/order', data);
  return response;
};

export const batchMoveSceneLines = async (data: SceneLineBatchMoveRequest): Promise<SceneLineBatchMoveResponse> => {
  const response = await put<SceneLineBatchMoveResponse>('/sys/scene/lines/batch/move', data);
  return response;
};

export const batchDeleteSceneLines = async (data: SceneLineBatchDeleteRequest): Promise<SceneLineBatchDeleteResponse> => {
  const response = await del<SceneLineBatchDeleteResponse>('/sys/scene/lines/batch', { data });
  return response;
};

// 保存场景脚本
export const saveSceneScript = async (sceneId: number, tenantId: number): Promise<SceneResponse> => {
  const response = await put<SceneResponse>(`/sys/scene/${sceneId}/script`, {}, { params: { tenant_id: tenantId } });
  return response;
};

// 场景图片上传相关API (使用exercise的上传接口)
export const getPicUploadUrl = async (tenant_id: number, file_name: string): Promise<PicUploadURL> => {
  return get('/sys/exercise/pic/upload-url', { 
    tenant_id, 
    file_name 
  });
};

export const deletePicFile = async (data: PicDeleteRequest): Promise<void> => {
  return del('/sys/exercise/pic', { data });
};

// Exercise响应类型定义
export interface ExerciseResponse {
  id: number;
  tenant_id: number;
  title: string;
  pic?: string;
  [key: string]: unknown;
}

// 更新练习的pic字段（通过scene的eid）
export const updateExercisePic = async (exerciseId: number, tenantId: number, pic: string): Promise<ExerciseResponse> => {
  const response = await put<ExerciseResponse>(`/sys/exercise/${exerciseId}?tenant_id=${tenantId}`, { pic });
  return response;
}; 