import { get, post, put, del } from '../api';

// 练习相关类型定义
export interface ExerciseCreate {
  tenant_id: number;
  title: string;
  intro?: string;
  type: number;
  pic?: string;
  content?: string;
  active?: number;
  published?: number;
}

export interface ExerciseUpdate {
  title?: string;
  intro?: string;
  type?: number;
  pic?: string;
  content?: string;
  active?: number;
  published?: number;
}

export interface ExerciseResponse {
  id: number;
  tenant_id: number;
  title: string;
  intro?: string;
  type: number;
  pic?: string;
  content?: string;
  notes?: string; // 备注字段
  active: number;
  published: number;
  depend?: string;
  ctime: string;
  mtime: string;
}

export interface ExerciseListResponse {
  total: number;
  items: ExerciseResponse[];
}

export interface GetExercisesParams {
  tenant_id: number;
  skip?: number;
  limit?: number;
  active?: number;
  published?: number;
  type?: number;
  sort_by?: string;
  sort_order?: string;
}

export interface PicUploadURL {
  upload_url: string;
  file_path: string;
  file_url: string;
  expires_in: number;
}

export interface PicDeleteRequest {
  file_path: string;
}

// API 接口调用
export const getExercises = async (params: GetExercisesParams): Promise<ExerciseResponse[]> => {
  return get('/sys/exercise', params);
};

export const getExercise = async (exerciseId: number, tenantId: number): Promise<ExerciseResponse> => {
  return get(`/sys/exercise/${exerciseId}`, { tenant_id: tenantId });
};

export const createExercise = async (data: ExerciseCreate): Promise<ExerciseResponse> => {
  return post('/sys/exercise', data);
};

export const updateExercise = async (exerciseId: number, tenantId: number, data: ExerciseUpdate): Promise<ExerciseResponse> => {
  return put(`/sys/exercise/${exerciseId}`, data, { params: { tenant_id: tenantId } });
};

export const getPicUploadUrl = async (tenant_id: number, file_name: string): Promise<PicUploadURL> => {
  return get('/sys/exercise/pic/upload-url', { 
    tenant_id, 
    file_name 
  });
};

export const deletePicFile = async (data: PicDeleteRequest): Promise<void> => {
  return del('/sys/exercise/pic', { data });
}; 