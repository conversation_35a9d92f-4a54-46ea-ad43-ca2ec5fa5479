import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider, App as AntdApp } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import TrainingCamp from './pages/TrainingCamp';
import ClassExercises from './pages/ClassExercises';
import Worksheet from './pages/Worksheet';
import Scene from './pages/Scene';
import Profile from './pages/Profile';
import ProtectedRoute from './components/ProtectedRoute';
import { useAuthStore } from './utils/authStore';
import './styles/App.css';

function App() {
  const { isAuthenticated } = useAuthStore();

  return (
    <ConfigProvider
      locale={zhCN}
      theme={{
        token: {
          colorPrimary: '#000000', // 设置主色调为黑色
        },
        components: {
          Spin: {
            colorPrimary: '#000000', // Spin组件主色调为黑色
            colorText: '#000000', // 文字颜色为黑色
          },
        },
      }}
    >
      <AntdApp>
        <Router>
        <Routes>
          {/* 登录页面路由 */}
          <Route
            path="/login"
            element={
              isAuthenticated ? <Navigate to="/dashboard" replace /> : <Login />
            }
          />

          {/* 受保护的仪表板路由 */}
          <Route
            path="/dashboard"
            element={
              <ProtectedRoute>
                <Dashboard />
              </ProtectedRoute>
            }
          />

          {/* 训练营路由 */}
          <Route
            path="/training-camp"
            element={
              <ProtectedRoute>
                <TrainingCamp />
              </ProtectedRoute>
            }
          />

          {/* 班级练习路由 */}
          <Route
            path="/class/:id/exercises"
            element={
              <ProtectedRoute>
                <ClassExercises />
              </ProtectedRoute>
            }
          />

          {/* 作业单详情路由 */}
          <Route
            path="/worksheet/:exerciseId/:classId"
            element={
              <ProtectedRoute>
                <Worksheet />
              </ProtectedRoute>
            }
          />

          {/* 场景练习路由 */}
          <Route
            path="/scene/:exerciseId/:classId"
            element={
              <ProtectedRoute>
                <Scene />
              </ProtectedRoute>
            }
          />

          {/* 个人信息路由 */}
          <Route
            path="/profile"
            element={
              <ProtectedRoute>
                <Profile />
              </ProtectedRoute>
            }
          />


          {/* 默认路由重定向 */}
          <Route
            path="/"
            element={
              <Navigate to={isAuthenticated ? "/dashboard" : "/login"} replace />
            }
          />

          {/* 404 路由 */}
          <Route
            path="*"
            element={
              <Navigate to={isAuthenticated ? "/dashboard" : "/login"} replace />
            }
          />
        </Routes>
        </Router>
      </AntdApp>
    </ConfigProvider>
  );
}

export default App;
