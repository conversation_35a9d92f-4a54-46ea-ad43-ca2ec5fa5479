import React, { useState, useEffect, useRef } from 'react';
import { Row, Col, Card, Empty, Spin, Typography, Button, message, Tooltip, Modal, Input, Tag, Avatar, List } from 'antd';
import { TeamOutlined, EditOutlined, DeleteOutlined, HolderOutlined, ReloadOutlined, DragOutlined, ContactsOutlined, UserOutlined, ProfileOutlined, RollbackOutlined, RobotOutlined } from '@ant-design/icons';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
  useSortable,
} from '@dnd-kit/sortable';
import {
  CSS,
} from '@dnd-kit/utilities';
import { 
  getSceneCharacters, 
  batchUpdateSceneCharacterOrder,
  batchDeleteSceneCharacters,
  createScene<PERSON>hara<PERSON>,
  updateS<PERSON><PERSON>haracter,
  type SceneCharacterResponse,
  type SceneCharacterBatchOrderRequest,
  type SceneCharacterBatchDeleteRequest,
  type SceneCharacterCreate,
  type SceneCharacterUpdate
} from '../../../services/system/scene';
import { 
  getCharacters,
  type CharacterResponse 
} from '../../../services/system/character';
import { getGlobalTenantInfo } from '../../../components/GlobalTenantSelector';
import { showError } from '../../../utils/errorHandler';

const { Text } = Typography;
const { Search } = Input;

// 性别映射
const genderMap: Record<number, { name: string; color: string }> = {
  0: { name: '未知', color: 'default' },
  1: { name: '男', color: 'blue' },
  2: { name: '女', color: 'pink' },
};

interface SceneCharactersTabProps {
  sceneId?: string;
}

// 可拖拽的本场景人物角色项组件
interface SortableCharacterItemProps {
  character: SceneCharacterResponse;
  index: number;
  isSortMode?: boolean;
  isEditMode?: boolean;
  onDelete: (character: SceneCharacterResponse) => void;
  onTogglePlayed: (character: SceneCharacterResponse) => void;
}

const SortableCharacterItem: React.FC<SortableCharacterItemProps> = ({ 
  character, 
  index, 
  isSortMode = false,
  isEditMode = false,
  onDelete,
  onTogglePlayed 
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: `character-${character.id}` });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition: transition || 'all 0.2s',
    opacity: isDragging ? 0.5 : 1,
    backgroundColor: 'transparent',
    border: '1px solid #f0f0f0',
    borderRadius: '6px',
    padding: '12px',
    marginBottom: '8px',
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      onMouseEnter={(e) => {
        e.currentTarget.style.backgroundColor = '#f5f5f5';
        e.currentTarget.style.borderColor = '#d9d9d9';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.backgroundColor = 'transparent';
        e.currentTarget.style.borderColor = '#f0f0f0';
      }}
    >
      <div style={{ display: 'flex', alignItems: 'flex-start', gap: '12px' }}>
        {isSortMode && (
          <div 
            {...listeners}
            style={{ 
              cursor: 'grab',
              color: '#999',
              fontSize: '14px',
              marginTop: '16px'
            }}
          >
            <HolderOutlined />
          </div>
        )}
        
        <div style={{ 
          backgroundColor: '#666',
          color: 'white',
          width: '20px',
          height: '20px',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '12px',
          fontWeight: 'bold',
          marginTop: '14px',
          flexShrink: 0
        }}>
          {index + 1}
        </div>

        <Avatar
          size={48}
          src={character.avatar}
          icon={<UserOutlined />}
          shape="circle"
          style={{ backgroundColor: '#f0f0f0', flexShrink: 0 }}
        />
        
        <div style={{ flex: 1, minWidth: 0 }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '4px' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px', flex: 1, minWidth: 0 }}>
              {character.profile && (
                <Tooltip title={character.profile}>
                  <ProfileOutlined style={{ color: '#1890ff', fontSize: '14px', flexShrink: 0 }} />
                </Tooltip>
              )}
              <Text strong>
                {character.name}
              </Text>
              <Tag 
                color={genderMap[Number(character.gender)]?.color || 'default'} 
                style={{ 
                  margin: 0,
                  fontSize: '12px',
                  height: '20px',
                  lineHeight: '18px',
                  padding: '0 6px',
                  borderRadius: '10px',
                  flexShrink: 0
                }}
              >
                {genderMap[Number(character.gender)]?.name || '未知'}
              </Tag>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                              <Tooltip title={character.played === 1 ? "学员扮演" : "机器人"}>
                  <Button
                    type="text"
                    icon={
                      character.played === 1 ? <UserOutlined /> : <RobotOutlined />
                    }
                    size="small"
                    style={{ 
                      color: character.played === 1 ? '#52c41a' : '#1890ff',
                      cursor: isEditMode ? 'pointer' : 'default'
                    }}
                    disabled={!isEditMode}
                    onClick={(e) => {
                      e.stopPropagation();
                      if (isEditMode) {
                        onTogglePlayed(character);
                      }
                    }}
                  />
                </Tooltip>
              {isEditMode && (
                <Tooltip title="删除人物">
                  <Button
                    type="text"
                    icon={<DeleteOutlined />}
                    size="small"
                    danger
                    onClick={(e) => {
                      e.stopPropagation();
                      onDelete(character);
                    }}
                  />
                </Tooltip>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const SceneCharactersTab: React.FC<SceneCharactersTabProps> = ({ sceneId }) => {
  const [characters, setCharacters] = useState<SceneCharacterResponse[]>([]);
  const [charactersLoading, setCharactersLoading] = useState(false);
  
  // 排序模式相关状态
  const [isCharacterSortMode, setIsCharacterSortMode] = useState(false);
  
  // 编辑模式相关状态
  const [isEditMode, setIsEditMode] = useState(false);
  
  // 人物角色库相关状态
  const [characterLibrary, setCharacterLibrary] = useState<CharacterResponse[]>([]);
  const [libraryLoading, setLibraryLoading] = useState(false);
  const [searchName, setSearchName] = useState('');
  const [searchNameValue, setSearchNameValue] = useState('');
  const [searchProfile, setSearchProfile] = useState('');
  const [searchProfileValue, setSearchProfileValue] = useState('');
  const [searchNotes, setSearchNotes] = useState('');
  const [searchNotesValue, setSearchNotesValue] = useState('');
  
  // 跟踪已选中的人物角色ID列表（本场景中的人物）
  const [selectedCharacterIds, setSelectedCharacterIds] = useState<Set<number>>(new Set());
  
  // 创建对搜索框的引用
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const searchNameInputRef = useRef<any>(null);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const searchProfileInputRef = useRef<any>(null);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const searchNotesInputRef = useRef<any>(null);
  
  // 创建对组件容器的引用
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const containerRef = useRef<any>(null);
  
  // 拖拽排序传感器
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // 获取当前租户ID
  const getCurrentTenantId = (): number | null => {
    const tenantInfo = getGlobalTenantInfo();
    return tenantInfo?.id || null;
  };

  // 切换人物排序模式
  const toggleCharacterSortMode = () => {
    if (!isCharacterSortMode && !characters.length) {
      message.warning('没有可排序的人物');
      return;
    }
    setIsCharacterSortMode(!isCharacterSortMode);
    // 退出排序模式时，也要退出编辑模式
    if (isCharacterSortMode) {
      setIsEditMode(false);
    }
  };

  // 切换编辑模式
  const toggleEditMode = () => {
    setIsEditMode(!isEditMode);
    // 进入编辑模式时，退出排序模式
    if (!isEditMode) {
      setIsCharacterSortMode(false);
    }
  };

  // 获取本场景人物角色
  const fetchCharacters = async () => {
    if (!sceneId) return;
    
    const tenantId = getCurrentTenantId();
    if (!tenantId) {
      message.error('请先选择租户');
      return;
    }

    setCharactersLoading(true);
    try {
      const response = await getSceneCharacters(parseInt(sceneId), tenantId);
      setCharacters(response);
      // 更新已选中的人物角色ID列表
      const selectedIds = new Set(response.map(character => character.cid));
      setSelectedCharacterIds(selectedIds);
    } catch (error) {
      showError(error, '获取本场景人物角色失败');
    } finally {
      setCharactersLoading(false);
    }
  };

  // 获取人物角色库
  const fetchCharacterLibrary = async () => {
    const tenantId = getCurrentTenantId();
    if (!tenantId) {
      setCharacterLibrary([]);
      return;
    }

    setLibraryLoading(true);
    try {
      const response = await getCharacters(tenantId, {
        active: 1, // 只获取有效的人物角色
        published: 1, // 只获取已发布的人物角色
        name: searchName || undefined,
        profile: searchProfile || undefined,
        notes: searchNotes || undefined,
        limit: 100, // 设置一个合理的限制
        skip: 0
      });
      setCharacterLibrary(response.items);
    } catch (error) {
      showError(error, '获取人物角色库失败');
    } finally {
      setLibraryLoading(false);
    }
  };

  // 处理姓名搜索
  const handleNameSearch = (value: string) => {
    if (!isEditMode) return;
    setSearchName(value);
    setSearchNameValue(value);
  };

  // 处理姓名搜索输入框值变化
  const handleNameInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!isEditMode) return;
    setSearchNameValue(e.target.value);
  };

  // 处理资料搜索
  const handleProfileSearch = (value: string) => {
    if (!isEditMode) return;
    setSearchProfile(value);
    setSearchProfileValue(value);
  };

  // 处理资料搜索输入框值变化
  const handleProfileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!isEditMode) return;
    setSearchProfileValue(e.target.value);
  };

  // 处理备注搜索
  const handleNotesSearch = (value: string) => {
    if (!isEditMode) return;
    setSearchNotes(value);
    setSearchNotesValue(value);
  };

  // 处理备注搜索输入框值变化
  const handleNotesInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!isEditMode) return;
    setSearchNotesValue(e.target.value);
  };

  // 重置人物角色库搜索
  const handleResetLibrarySearch = () => {
    if (!isEditMode) return;
    setSearchName('');
    setSearchNameValue('');
    setSearchProfile('');
    setSearchProfileValue('');
    setSearchNotes('');
    setSearchNotesValue('');
  };

  // 处理人物角色库点击 - 添加或删除场景-人物关系
  const handleLibraryCharacterClick = async (character: CharacterResponse) => {
    if (!isEditMode || !sceneId) return;
    
    const tenantId = getCurrentTenantId();
    if (!tenantId) {
      message.error('请先选择租户');
      return;
    }

    const isSelected = selectedCharacterIds.has(character.id);
    
    try {
      if (isSelected) {
        // 删除场景-人物关系
        // 找到对应的场景人物关系ID
        const sceneCharacter = characters.find(sc => sc.cid === character.id);
        if (sceneCharacter) {
          const deleteData: SceneCharacterBatchDeleteRequest = {
            tenant_id: tenantId,
            sid: parseInt(sceneId),
            character_ids: [sceneCharacter.id]
          };
          
          await batchDeleteSceneCharacters(deleteData);
          message.success('已从场景中移除人物');
          
          // 更新选中状态
          const newSelectedIds = new Set(selectedCharacterIds);
          newSelectedIds.delete(character.id);
          setSelectedCharacterIds(newSelectedIds);
          
          // 重新获取本场景人物角色
          await fetchCharacters();
        }
      } else {
        // 创建场景-人物关系
        const createData: SceneCharacterCreate = {
          tenant_id: tenantId,
          sid: parseInt(sceneId),
          cid: character.id,
          played: 0, // 默认不是学员扮演
          priority: characters.length + 1 // 放在最后
        };
        
        await createSceneCharacter(createData);
        message.success('已添加人物到场景');
        
        // 更新选中状态
        const newSelectedIds = new Set(selectedCharacterIds);
        newSelectedIds.add(character.id);
        setSelectedCharacterIds(newSelectedIds);
        
        // 重新获取本场景人物角色
        await fetchCharacters();
      }
    } catch (error) {
      showError(error, isSelected ? '移除人物失败' : '添加人物失败');
    }
  };



  // 刷新数据
  const handleRefresh = () => {
    fetchCharacters();
  };

  // 处理拖拽结束
  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    if (!active.id || !over?.id || !sceneId) return;

    const tenantId = getCurrentTenantId();
    if (!tenantId) return;

    // 解析拖拽ID，提取类型和真实ID
    const parseId = (id: string | number) => {
      const idStr = id.toString();
      if (idStr.startsWith('character-')) {
        return { type: 'character', id: parseInt(idStr.replace('character-', '')) };
      }
      return { type: 'unknown', id: 0 };
    };

    const activeInfo = parseId(active.id);
    const overInfo = parseId(over.id);

    // 检查是否是人物拖拽
    if (activeInfo.type === 'character' && overInfo.type === 'character') {
      // 人物之间的拖拽排序（只在人物排序模式下执行）
      if (isCharacterSortMode && activeInfo.id !== overInfo.id) {
        const oldIndex = characters.findIndex((character) => character.id === activeInfo.id);
        const newIndex = characters.findIndex((character) => character.id === overInfo.id);

        const newCharacters = arrayMove(characters, oldIndex, newIndex);
        setCharacters(newCharacters);
        
        // 立即调用API更新排序
        try {
          const orderData: SceneCharacterBatchOrderRequest = {
            tenant_id: tenantId,
            sid: parseInt(sceneId),
            characters: newCharacters.map((character, index) => ({
              id: character.id,
              priority: index + 1
            }))
          };
          
          await batchUpdateSceneCharacterOrder(orderData);
          message.success('人物排序更新成功');
        } catch (error) {
          // 如果API调用失败，恢复原始顺序
          setCharacters(characters);
          showError(error, '人物排序更新失败');
        }
      }
    }
  };



  // 切换人物是否扮演状态
  const handleToggleCharacterPlayed = async (character: SceneCharacterResponse) => {
    if (!sceneId) return;
    
    const tenantId = getCurrentTenantId();
    if (!tenantId) {
      message.error('请先选择租户');
      return;
    }

    try {
      const newPlayed = character.played === 1 ? 0 : 1;
      const updateData: SceneCharacterUpdate = {
        tenant_id: tenantId,
        played: newPlayed
      };
      
      await updateSceneCharacter(character.id, updateData);
      message.success(`已切换为${newPlayed === 1 ? '学员扮演' : '机器人扮演'}`);
      
      // 重新获取本场景人物角色
      await fetchCharacters();
    } catch (error) {
      showError(error, '切换扮演状态失败');
    }
  };

  // 删除人物
  const handleDeleteCharacter = (character: SceneCharacterResponse) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要从场景中删除人物"${character.name}"吗？此操作不可撤销。`,
      okText: '确定',
      cancelText: '取消',
      okType: 'danger',
      onOk: async () => {
        try {
          const tenantId = getCurrentTenantId();
          if (!sceneId || !tenantId) return;
          
          const deleteData: SceneCharacterBatchDeleteRequest = {
            tenant_id: tenantId,
            sid: parseInt(sceneId),
            character_ids: [character.id]
          };
          
          await batchDeleteSceneCharacters(deleteData);
          message.success('人物删除成功');
          
          // 重新获取本场景人物角色
          await fetchCharacters();
        } catch (error) {
          showError(error, '删除人物失败');
        }
      }
    });
  };



  // 初始化加载数据
  useEffect(() => {
    fetchCharacters();
    // 重置搜索条件
    setSearchName('');
    setSearchProfile('');
    setSearchNotes('');
    setSearchNameValue('');
    setSearchProfileValue('');
    setSearchNotesValue('');
  }, [sceneId]);

  // 监听人物角色库搜索参数变化
  useEffect(() => {
    fetchCharacterLibrary();
  }, [searchName, searchProfile, searchNotes]);

  // 监听租户切换事件
  useEffect(() => {
    const handleTenantChange = () => {
      setCharacters([]);
      setCharacterLibrary([]);
      fetchCharacters();
      fetchCharacterLibrary();
    };

    window.addEventListener('globalTenantChanged', handleTenantChange);
    return () => {
      window.removeEventListener('globalTenantChanged', handleTenantChange);
    };
  }, [sceneId]);

  // 监听ESC键退出编辑状态
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isEditMode) {
        toggleEditMode();
      }
    };

    const container = containerRef.current;
    if (container) {
      container.addEventListener('keydown', handleKeyDown);
      // 确保容器能接收键盘事件
      container.focus();
      return () => {
        container.removeEventListener('keydown', handleKeyDown);
      };
    }
  }, [isEditMode]);

  // 组件挂载时设置焦点
  useEffect(() => {
    const container = containerRef.current;
    if (container) {
      container.focus();
    }
  }, []);

  if (!sceneId) {
    return (
      <div style={{ 
        height: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '40px'
      }}>
        <Empty
          description="场景ID不可用"
          style={{ color: '#999' }}
        />
      </div>
    );
  }

  const currentTenantId = getCurrentTenantId();
  if (!currentTenantId) {
    return (
      <div style={{ 
        height: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '40px'
      }}>
        <Empty
          description="请先在顶部导航栏选择租户"
          style={{ color: '#999' }}
        />
      </div>
    );
  }

  return (
    <div 
      ref={containerRef}
      tabIndex={-1}
      style={{ height: '100%', padding: '0px', outline: 'none' }}
    >
      <DndContext 
        sensors={sensors} 
        collisionDetection={closestCenter} 
        onDragEnd={handleDragEnd}
      >
        <Row gutter={16} style={{ height: '100%' }}>
          {/* 左侧分栏：本场景人物角色 */}
          <Col span={8} style={{ height: '100%' }}>
            <Card
              title={
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <span>
                    <TeamOutlined style={{ marginRight: 8 }} />
                    {isCharacterSortMode ? '人物排序' : isEditMode ? '人物编辑' : '本场景人物角色'}
                  </span>
                  <div style={{ display: 'flex', gap: '4px' }}>
                    <Tooltip title="刷新列表">
                      <Button
                        type="primary"
                        icon={<ReloadOutlined />}
                        onClick={handleRefresh}
                        loading={charactersLoading}
                        disabled={isCharacterSortMode || isEditMode}
                        size="small"
                      />
                    </Tooltip>
                    <Tooltip title={isEditMode ? "退出编辑" : "编辑"}>
                      <Button
                        type={isEditMode ? "primary" : "text"}
                        icon={isEditMode ? <RollbackOutlined /> : <EditOutlined />}
                        onClick={toggleEditMode}
                        disabled={isCharacterSortMode}
                        size="small"
                      />
                    </Tooltip>
                    <Tooltip title={isCharacterSortMode ? "退出排序" : "调整排序"}>
                      <Button
                        type={isCharacterSortMode ? "primary" : "text"}
                        icon={<DragOutlined />}
                        onClick={toggleCharacterSortMode}
                        disabled={isEditMode}
                        size="small"
                      />
                    </Tooltip>
                  </div>
                </div>
              }
              style={{ height: '100%' }}
              styles={{ 
                body: {
                  height: 'calc(100% - 57px)',
                  overflow: 'hidden',
                  padding: '12px'
                }
              }}
            >
              <Spin spinning={charactersLoading}>
                {characters.length === 0 ? (
                  <Empty
                    description="暂无人物数据"
                    style={{ marginTop: '60px' }}
                  />
                ) : (
                  <SortableContext 
                    items={characters.map(character => `character-${character.id}`)} 
                    strategy={verticalListSortingStrategy}
                  >
                    <div>
                      {characters.map((character, index) => (
                        <SortableCharacterItem
                          key={character.id}
                          character={character}
                          index={index}
                          isSortMode={isCharacterSortMode}
                          isEditMode={isEditMode}
                          onDelete={handleDeleteCharacter}
                          onTogglePlayed={handleToggleCharacterPlayed}
                        />
                      ))}
                    </div>
                  </SortableContext>
                )}
              </Spin>
            </Card>
          </Col>

          {/* 右侧分栏：人物角色库 */}
          <Col span={16} style={{ height: '100%' }}>
            <Card
              title={
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <span>
                    <ContactsOutlined style={{ marginRight: 8 }} />
                    人物角色库
                    {!isEditMode && (
                      <span style={{ fontSize: '12px', color: '#999', marginLeft: '8px' }}>
                        （请先点击"编辑"按钮）
                      </span>
                    )}
                    {isEditMode && (
                      <span style={{ fontSize: '12px', color: '#999', marginLeft: '8px' }}>
                       （请输入条件缩小搜索范围）
                      </span>
                    )}
                  </span>
                  <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                    <Search
                      placeholder="搜索姓名"
                      allowClear
                      onSearch={handleNameSearch}
                      style={{ width: 120 }}
                      ref={searchNameInputRef}
                      value={searchNameValue}
                      onChange={handleNameInputChange}
                      size="small"
                      disabled={!isEditMode}
                    />
                    <Search
                      placeholder="搜索资料"
                      allowClear
                      onSearch={handleProfileSearch}
                      style={{ width: 120 }}
                      ref={searchProfileInputRef}
                      value={searchProfileValue}
                      onChange={handleProfileInputChange}
                      size="small"
                      disabled={!isEditMode}
                    />
                    <Search
                      placeholder="搜索备注"
                      allowClear
                      onSearch={handleNotesSearch}
                      style={{ width: 120 }}
                      ref={searchNotesInputRef}
                      value={searchNotesValue}
                      onChange={handleNotesInputChange}
                      size="small"
                      disabled={!isEditMode}
                    />
                    <Tooltip title="重置搜索">
                      <Button
                        type="primary"
                        icon={<ReloadOutlined />}
                        onClick={handleResetLibrarySearch}
                        size="small"
                        disabled={!isEditMode}
                      />
                    </Tooltip>
                  </div>
                </div>
              }
              style={{ 
                height: '100%',
                opacity: isEditMode ? 1 : 0.6
              }}
              styles={{ 
                body: {
                  height: 'calc(100% - 57px)',
                  overflow: 'auto',
                  padding: '12px'
                }
              }}
            >
              <Spin spinning={libraryLoading}>
                {!isEditMode ? (
                  <Empty
                    description={
                      <div>
                        <div>人物角色库暂不可用</div>
                        <div style={{ fontSize: '12px', color: '#999', marginTop: '8px' }}>
                          请先点击左上角的"编辑"按钮进入编辑模式
                        </div>
                      </div>
                    }
                    style={{ marginTop: '60px' }}
                  />
                ) : characterLibrary.length === 0 ? (
                  <Empty
                    description="暂无符合条件的人物角色"
                    style={{ marginTop: '60px' }}
                  />
                ) : (
                  <List
                    dataSource={characterLibrary}
                    renderItem={(character) => {
                      const isSelected = selectedCharacterIds.has(character.id);
                      return (
                        <List.Item
                          style={{
                            padding: '12px',
                            border: isSelected ? '1px solid #1890ff' : '1px solid #f0f0f0',
                            borderRadius: '6px',
                            marginBottom: '8px',
                            cursor: isEditMode ? 'pointer' : 'not-allowed',
                            transition: 'all 0.2s',
                            backgroundColor: isSelected ? '#e6f7ff' : 'transparent',
                            opacity: isEditMode ? 1 : 0.6
                          }}
                          onMouseEnter={(e) => {
                            if (isEditMode && !isSelected) {
                              e.currentTarget.style.backgroundColor = '#f5f5f5';
                              e.currentTarget.style.borderColor = '#d9d9d9';
                            }
                          }}
                          onMouseLeave={(e) => {
                            if (isEditMode && !isSelected) {
                              e.currentTarget.style.backgroundColor = 'transparent';
                              e.currentTarget.style.borderColor = '#f0f0f0';
                            }
                          }}
                          onClick={() => {
                            if (isEditMode) {
                              handleLibraryCharacterClick(character);
                            }
                          }}
                        >
                        <List.Item.Meta
                          avatar={
                            <Avatar
                              size={48}
                              src={character.avatar}
                              icon={<UserOutlined />}
                              shape="circle"
                              style={{ backgroundColor: '#f0f0f0' }}
                            />
                          }
                          title={
                            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                                {character.profile && (
                                  <Tooltip title={character.profile}>
                                    <ProfileOutlined style={{ color: '#1890ff', fontSize: '14px' }} />
                                  </Tooltip>
                                )}
                                <Text strong>{character.name}</Text>
                                <Tag 
                                  color={genderMap[Number(character.gender)]?.color || 'default'} 
                                  style={{ 
                                    margin: 0,
                                    fontSize: '12px',
                                    height: '20px',
                                    lineHeight: '18px',
                                    padding: '0 6px',
                                    borderRadius: '10px'
                                  }}
                                >
                                  {genderMap[Number(character.gender)]?.name || '未知'}
                                </Tag>
                              </div>
                              {isSelected && (
                                <Tag color="success" style={{ margin: 0, fontSize: '12px' }}>
                                  已选
                                </Tag>
                              )}
                            </div>
                          }
                          description={
                            <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginTop: '4px' }}>
                              <div style={{ fontSize: '12px', color: '#999' }}>
                                <Text type="secondary">备注：{character.notes || '（暂无）'}</Text>
                              </div>
                            </div>
                          }
                        />
                        </List.Item>
                      );
                    }}
                  />
                )}
              </Spin>
            </Card>
          </Col>
        </Row>
      </DndContext>
    </div>
  );
};

export default SceneCharactersTab; 