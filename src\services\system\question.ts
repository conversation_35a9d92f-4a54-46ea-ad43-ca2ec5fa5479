// 问题管理相关API接口
import { get, post, put, del } from '../api';

// 问题响应类型
export interface QuestionResponse {
  id: number;
  tenant_id: number;
  title: string;
  bgtext?: string; // 背景文字
  bgvideo?: string; // 背景视频URL
  notes?: string; // 备注
  pv_skills?: string; // 点评能力（提示词变量，后台用）
  pv_rules?: string; // 点评细则（提示词变量，后台用）
  pv_formats?: string; // 点评格式（提示词变量，后台用）
  active: number;
  ctime: string;
}

// 创建问题请求类型
export interface QuestionCreate {
  tenant_id: number;
  title: string;
  bgtext?: string; // 背景文字
  notes?: string; // 备注
}

// 更新问题请求类型
export interface QuestionUpdate {
  title?: string;
  bgtext?: string; // 背景文字
  bgvideo?: string; // 背景视频URL
  notes?: string; // 备注
  pv_skills?: string; // 点评能力
  pv_rules?: string; // 点评细则
  pv_formats?: string; // 点评格式
  active?: number;
}

// 获取问题列表请求参数
export interface QuestionListParams {
  tenant_id: number;
  skip?: number;
  limit?: number;
  active?: number; // 按问题状态筛选，可选值为0(禁用)或1(启用)
  sort_by?: 'id' | 'ctime';
  sort_order?: 'asc' | 'desc';
  subject_id?: number; // 按关联的主题ID筛选
  start_time?: string; // 创建时间的开始时间，格式为ISO 8601
  end_time?: string; // 创建时间的结束时间，格式为ISO 8601
  title?: string; // 按标题关键字模糊搜索
  notes?: string; // 按备注关键字模糊搜索
}

// 问题列表响应类型
export interface QuestionListResponse {
  items: QuestionResponse[];
  total: number;
}

// 获取问题列表
export const getQuestions = async (tenantId: number, params?: Omit<QuestionListParams, 'tenant_id'>): Promise<QuestionListResponse> => {
  const queryParams = {
    tenant_id: tenantId,
    ...params
  };
  return await get<QuestionListResponse>('/sys/question', queryParams);
};

// 获取单个问题
export const getQuestion = async (questionId: number): Promise<QuestionResponse> => {
  return await get<QuestionResponse>(`/sys/question/${questionId}`);
};

// 创建问题
export const createQuestion = async (data: QuestionCreate): Promise<QuestionResponse> => {
  return await post<QuestionResponse>('/sys/question', data);
};

// 更新问题
export const updateQuestion = async (questionId: number, data: QuestionUpdate): Promise<QuestionResponse> => {
  return await put<QuestionResponse>(`/sys/question/${questionId}`, data);
};

// 删除问题（软删除，通过设置active为0实现）
export const deleteQuestion = async (questionId: number): Promise<QuestionResponse> => {
  return await updateQuestion(questionId, { active: 0 });
};

// 问题指南相关类型和接口
export interface QuestionGuideResponse {
  id: number;
  tenant_id: number;
  qid: number;
  title: string;
  details: string;
  priority: number;
}

export interface QuestionGuideCreate {
  tenant_id: number;
  qid: number;
  title: string;
  details: string;
}

export interface QuestionGuideUpdate {
  title?: string;
  details?: string;
}

export interface QuestionGuideListResponse {
  total: number;
  items: QuestionGuideResponse[];
}

export interface QuestionGuideBatchOrderRequest {
  tenant_id: number;
  guides: { id: number; priority: number }[];
}

export interface QuestionGuideBatchOrderResponse {
  success_count: number;
  total_count: number;
  updated_guides: QuestionGuideResponse[];
}

// 获取问题指南列表
export const getQuestionGuides = async (params: {
  tenant_id: number;
  question_id?: number;
  skip?: number;
  limit?: number;
  sort_by?: 'priority';
  sort_order?: 'asc' | 'desc';
}): Promise<QuestionGuideListResponse> => {
  return await get<QuestionGuideListResponse>('/sys/question/guides', params);
};

// 创建问题指南
export const createQuestionGuide = async (data: QuestionGuideCreate): Promise<QuestionGuideResponse> => {
  return await post<QuestionGuideResponse>('/sys/question/guides', data);
};

// 更新问题指南
export const updateQuestionGuide = async (guideId: number, tenantId: number, data: QuestionGuideUpdate): Promise<QuestionGuideResponse> => {
  return await put<QuestionGuideResponse>(`/sys/question/guides/${guideId}?tenant_id=${tenantId}`, data);
};

// 删除问题指南
export const deleteQuestionGuide = async (guideId: number, tenantId: number): Promise<void> => {
  return await del<void>(`/sys/question/guides/${guideId}?tenant_id=${tenantId}`);
};

// 批量调整问题指南顺序
export const batchUpdateQuestionGuideOrder = async (data: QuestionGuideBatchOrderRequest): Promise<QuestionGuideBatchOrderResponse> => {
  return await put<QuestionGuideBatchOrderResponse>('/sys/question/guides/batch/order', data);
};

// 问题-模块关系相关类型和接口
export interface QuestionModuleResponse {
  id: number;
  tenant_id: number;
  qid: number;
  mid: number;
  module?: {
    id: number;
    name: string;
  };
}

export interface QuestionModuleCreate {
  tenant_id: number;
  qid: number;
  mid: number;
}

export interface QuestionModuleListResponse {
  total: number;
  items: QuestionModuleResponse[];
}

export interface QuestionModuleBatchCreate {
  items: QuestionModuleCreate[];
}

export interface QuestionModuleBatchDelete {
  tenant_id: number;
  relation_ids?: number[];
  question_id?: number;
}

export interface QuestionModuleBatchResponse {
  success_count: number;
  message: string;
}

// 获取问题-模块关系列表
export const getQuestionModules = async (params: {
  tenant_id: number;
  question_id?: number;
  skip?: number;
  limit?: number;
}): Promise<QuestionModuleListResponse> => {
  return await get<QuestionModuleListResponse>('/sys/question/modules', params);
};

// 批量创建问题-模块关系
export const batchCreateQuestionModules = async (data: QuestionModuleBatchCreate): Promise<QuestionModuleBatchResponse> => {
  return await post<QuestionModuleBatchResponse>('/sys/question/modules/batch', data);
};

// 批量删除问题-模块关系
export const batchDeleteQuestionModules = async (data: QuestionModuleBatchDelete): Promise<QuestionModuleBatchResponse> => {
  return await del<QuestionModuleBatchResponse>('/sys/question/modules/batch', { data });
};

// 问题-主题关系相关类型和接口
export interface QuestionSubjectResponse {
  id: number;
  tenant_id: number;
  qid: number;
  sid: number;
  subject?: {
    id: number;
    name: string;
  };
}

export interface QuestionSubjectCreate {
  tenant_id: number;
  qid: number;
  sid: number;
}

export interface QuestionSubjectListResponse {
  total: number;
  items: QuestionSubjectResponse[];
}

export interface QuestionSubjectBatchCreate {
  items: QuestionSubjectCreate[];
}

export interface QuestionSubjectBatchDelete {
  tenant_id: number;
  relation_ids?: number[];
  question_id?: number;
}

export interface QuestionSubjectBatchResponse {
  success_count: number;
  message: string;
}

// 获取问题-主题关系列表
export const getQuestionSubjects = async (params: {
  tenant_id: number;
  question_id?: number;
  skip?: number;
  limit?: number;
}): Promise<QuestionSubjectListResponse> => {
  return await get<QuestionSubjectListResponse>('/sys/question/subjects', params);
};

// 批量创建问题-主题关系
export const batchCreateQuestionSubjects = async (data: QuestionSubjectBatchCreate): Promise<QuestionSubjectBatchResponse> => {
  return await post<QuestionSubjectBatchResponse>('/sys/question/subjects/batch', data);
};

// 批量删除问题-主题关系
export const batchDeleteQuestionSubjects = async (data: QuestionSubjectBatchDelete): Promise<QuestionSubjectBatchResponse> => {
  return await del<QuestionSubjectBatchResponse>('/sys/question/subjects/batch', { data });
};

// 问题标签关系响应类型（用于获取问题的所有模块和主题）
export interface QuestionRelationResponse {
  modules: QuestionModuleResponse[];
  subjects: QuestionSubjectResponse[];
  module_total: number;
  subject_total: number;
}

// 批量更新问题模块关系请求类型
export interface QuestionModuleBatchUpdate {
  tenant_id: number;
  question_id: number;
  module_ids: number[];
}

// 批量更新问题主题关系请求类型
export interface QuestionSubjectBatchUpdate {
  tenant_id: number;
  question_id: number;
  subject_ids: number[];
}

// 问题模块更新响应类型
export interface QuestionModuleUpdateResponse {
  success: boolean;
  message: string;
}

// 问题主题更新响应类型  
export interface QuestionSubjectUpdateResponse {
  success: boolean;
  message: string;
}

// 获取问题关联的理论模块和主题
export const getQuestionTags = async (params: {
  tenant_id: number;
  question_id: number;
}): Promise<QuestionRelationResponse> => {
  return await get<QuestionRelationResponse>('/sys/question/tags', params);
};

// 批量更新问题-理论模块关系
export const batchUpdateQuestionModules = async (data: QuestionModuleBatchUpdate): Promise<QuestionModuleUpdateResponse> => {
  return await put<QuestionModuleUpdateResponse>('/sys/question/modules/batch', data);
};

// 批量更新问题-主题关系
export const batchUpdateQuestionSubjects = async (data: QuestionSubjectBatchUpdate): Promise<QuestionSubjectUpdateResponse> => {
  return await put<QuestionSubjectUpdateResponse>('/sys/question/subjects/batch', data);
}; 