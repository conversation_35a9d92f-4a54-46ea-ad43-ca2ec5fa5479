import { get, post, put, del } from '../api';

// 班级响应数据类型
export interface ClazzResponse {
  id: number;
  tenant_id: number;
  name: string;
  description?: string;
  pic?: string;
  notes?: string;
  btime: string; // 开始时间，格式为 date-time
  etime: string; // 结束时间，格式为 date-time
  ctime: string; // 创建时间，格式为 date-time
  active: number; // 是否有效（0：失效；1：有效）
}

// 班级创建请求类型
export interface ClazzCreate {
  tenant_id: number;
  name: string;
  description?: string;
  pic?: string;
  btime: string; // 开始时间，格式为 date-time
  etime: string; // 结束时间，格式为 date-time
  notes?: string;
}

// 班级更新请求类型
export interface ClazzUpdate {
  name?: string;
  description?: string;
  pic?: string;
  notes?: string;
  btime?: string; // 开始时间，格式为 date-time
  etime?: string; // 结束时间，格式为 date-time
  active?: number; // 是否有效（0：失效；1：有效）
}

// 获取班级列表请求参数
export interface ClazzListParams {
  tenant_id: number; // 租户ID，必填查询参数
  skip?: number; // 跳过的记录数，用于分页，默认为0
  limit?: number; // 每页返回的记录数，默认为100，范围为1-100
  active?: number; // 班级状态筛选，可选值为0(禁用)或1(启用)
  sort_by?: 'id' | 'ctime'; // 排序字段
  sort_order?: 'asc' | 'desc'; // 排序方式
  name?: string; // 按班级名称搜索，支持模糊匹配
  notes?: string; // 按备注搜索，支持模糊匹配
  start_time?: string; // 时间窗口开始时间，格式为 date-time
  end_time?: string; // 时间窗口结束时间，格式为 date-time
}

// 班级列表响应类型
export interface ClazzListResponse {
  items: ClazzResponse[];
  total: number;
}

// 图片上传URL响应类型
export interface PicUploadURL {
  upload_url: string;
  file_path: string;
  file_url: string;
  expires_at: string;
}

// 图片删除请求类型
export interface PicDeleteRequest {
  file_path: string; // 要删除的文件OSS signed URL
}

// 删除响应类型
export interface DeleteResponse {
  message: string;
}

// 获取班级列表
export const getClazzes = (params: ClazzListParams): Promise<ClazzListResponse> => {
  return get('/sys/clazz', params);
};

// 获取单个班级信息
export const getClazz = (clazzId: number, tenantId: number): Promise<ClazzResponse> => {
  return get(`/sys/clazz/${clazzId}`, { tenant_id: tenantId });
};

// 创建班级
export const createClazz = (clazz: ClazzCreate): Promise<ClazzResponse> => {
  return post('/sys/clazz', clazz);
};

// 更新班级
export const updateClazz = (clazzId: number, clazz: ClazzUpdate, tenantId: number): Promise<ClazzResponse> => {
  return put(`/sys/clazz/${clazzId}?tenant_id=${tenantId}`, clazz);
};

// 删除班级
export const deleteClazz = (clazzId: number, tenantId: number): Promise<DeleteResponse> => {
  return del(`/sys/clazz/${clazzId}?tenant_id=${tenantId}`);
};

// 获取图片上传URL
export const getPicUploadUrl = (tenantId: number, fileName: string): Promise<PicUploadURL> => {
  return get('/sys/clazz/pic/upload-url', { tenant_id: tenantId, file_name: fileName });
};

// 删除图片文件
export const deletePicFile = (filePath: string): Promise<Record<string, unknown>> => {
  return del('/sys/clazz/pic', { data: { file_path: filePath } });
};

// 班级练习响应数据类型 - 根据最新 OpenAPI 文档更新
export interface ClassExerciseWithExerciseInfoResponse {
  id: number; // 关系ID
  tenant_id: number; // 租户ID
  cid: number; // 班级ID
  eid: number; // 练习ID
  tid?: number | null; // 老师ID
  depend: number; // 依赖关系（0：不依赖；1：依赖前一个练习）
  priority: number; // 优先级，数字越小优先级越高
  active: number; // 是否有效（0：失效；1：有效）
  title: string; // 练习标题
  type: number; // 练习类型（1：作业单；2：模拟场景）
  pic?: string; // 练习图片
  intro?: string; // 练习简介
  tname?: string | null; // 老师姓名
  tavatar?: string | null; // 老师头像
}

// 保持向后兼容性
export interface ClazzExerciseResponse extends ClassExerciseWithExerciseInfoResponse {
  notes?: string; // 练习备注
}

// 班级练习创建请求类型 - 根据最新 OpenAPI 文档更新
export interface ClassExerciseCreate {
  tenant_id: number;
  cid: number; // 班级ID
  eid: number; // 练习ID
  tid?: number | null; // 老师ID
  depend?: number; // 依赖关系（0：不依赖；1：依赖前一个练习）
  priority?: number; // 优先级
}

// 保持向后兼容性
export type ClazzExerciseCreate = ClassExerciseCreate;

// 班级练习更新请求类型 - 根据最新 OpenAPI 文档更新
export interface ClassExerciseUpdate {
  tid?: number | null; // 老师ID
  depend?: number; // 依赖关系（0：不依赖；1：依赖前一个练习）
  priority?: number; // 优先级
  active?: number; // 是否有效（0：失效；1：有效）
}

// 保持向后兼容性
export type ClazzExerciseUpdate = ClassExerciseUpdate;

// 班级练习列表响应类型 - 根据最新 OpenAPI 文档更新
export interface ClassExerciseListResponse {
  items: ClassExerciseWithExerciseInfoResponse[];
  total: number;
}

// 保持向后兼容性
export type ClazzExerciseListResponse = ClassExerciseListResponse;

// 班级练习批量排序请求类型 - 根据最新 OpenAPI 文档更新
export interface ClassExerciseBatchOrderRequest {
  tenant_id: number;
  class_id: number;
  class_exercises: Array<{
    id: number; // 班级练习关系ID
    priority: number; // 新的优先级
  }>;
}

// 保持向后兼容性
export type ClazzExerciseBatchOrderRequest = ClassExerciseBatchOrderRequest;

// 班级练习批量排序响应类型 - 根据最新 OpenAPI 文档
export interface ClassExerciseBatchOrderResponse {
  success_count: number; // 成功更新的数量
  total_count: number; // 总数量
  updated_class_exercises: ClassExerciseWithExerciseInfoResponse[]; // 更新后的班级练习关系列表
}

// 班级练习批量设置老师请求类型 - 根据最新 OpenAPI 文档
export interface ClassExerciseBatchSetTeacherRequest {
  tenant_id: number;
  class_id: number; // 班级ID，必填
  teacher_id?: number | null; // 老师ID，如果为null则清空所有练习的老师
}

// 班级练习批量设置老师响应类型 - 根据最新 OpenAPI 文档
export interface ClassExerciseBatchSetTeacherResponse {
  success_count: number; // 成功更新的数量
  total_count: number; // 总数量
  message: string; // 操作结果信息
}

// 班级计划导入请求类型 - 根据最新 OpenAPI 文档更新
export interface ClassImportPlanRequest {
  tenant_id: number;
  class_id: number;
  plan_id: number;
  teacher_id?: number; // 可选，将分配给所有导入的练习
}

// 班级计划导入响应类型 - 根据最新 OpenAPI 文档
export interface ClassImportPlanResponse {
  success_count: number; // 成功导入的练习数量
  total_count: number; // 总数量
  imported_class_exercises: ClassExerciseWithExerciseInfoResponse[]; // 导入的班级练习关系列表
  message: string; // 导入结果信息
}

// 获取班级练习列表 - 更新返回类型
export const getClazzExercises = (clazzId: number, tenantId: number): Promise<ClassExerciseListResponse> => {
  return get(`/sys/clazz/${clazzId}/exercises`, { tenant_id: tenantId });
};

// 添加练习到班级 - 更新返回类型
export const createClazzExercise = (clazzId: number, clazzExercise: ClassExerciseCreate): Promise<ClassExerciseWithExerciseInfoResponse> => {
  return post(`/sys/clazz/${clazzId}/exercises`, clazzExercise);
};

// 更新班级练习 - 更新返回类型
export const updateClazzExercise = (clazzId: number, clazzExerciseId: number, clazzExercise: ClassExerciseUpdate): Promise<ClassExerciseWithExerciseInfoResponse> => {
  return put(`/sys/clazz/${clazzId}/exercises/${clazzExerciseId}`, clazzExercise);
};

// 删除班级练习 - 保持不变
export const deleteClazzExercise = (clazzExerciseId: number): Promise<DeleteResponse> => {
  return del(`/sys/clazz/exercises/${clazzExerciseId}`);
};

// 批量更新班级练习排序 - 更新返回类型
export const batchUpdateClazzExerciseOrder = (orderData: ClassExerciseBatchOrderRequest): Promise<ClassExerciseBatchOrderResponse> => {
  return put('/sys/clazz/exercises/batch/order', orderData);
};

// 批量导入练习计划到班级 - 使用最新的接口定义
export const batchImportClazzExercisesFromPlan = (clazzId: number, importData: ClassImportPlanRequest): Promise<ClassImportPlanResponse> => {
  return post(`/sys/clazz/${clazzId}/exercises/batch`, importData);
};

// 批量设置班级练习的老师 - 根据最新 OpenAPI 文档
export const batchSetClazzExerciseTeacher = (teacherData: ClassExerciseBatchSetTeacherRequest): Promise<ClassExerciseBatchSetTeacherResponse> => {
  return put('/sys/clazz/exercises/batch/teacher', teacherData);
};

// 班级学员响应数据类型 - 根据最新 OpenAPI 文档
export interface ClassStudentWithStudentInfoResponse {
  id: number; // 关系ID
  tenant_id: number; // 租户ID
  cid: number; // 班级ID
  sid: number; // 学员ID
  priority: number; // 优先级，数字越小优先级越高
  active: number; // 是否有效（0：失效；1：有效）
  name: string; // 学员姓名
  gender: number; // 性别
  notes?: string; // 学员备注
}

// 班级学员创建请求类型 - 根据最新 OpenAPI 文档
export interface ClassStudentCreate {
  tenant_id: number;
  cid: number; // 班级ID
  sid: number; // 学员ID
  priority?: number; // 优先级
}

// 班级学员更新请求类型 - 根据最新 OpenAPI 文档
export interface ClassStudentUpdate {
  priority?: number; // 优先级
  active?: number; // 是否有效（0：失效；1：有效）
}

// 班级学员列表响应类型 - 根据最新 OpenAPI 文档
export interface ClassStudentListResponse {
  items: ClassStudentWithStudentInfoResponse[];
  total: number;
}

// 班级学员批量创建请求类型 - 根据最新 OpenAPI 文档
export interface ClassStudentBatchCreateRequest {
  tenant_id: number;
  class_id: number;
  student_ids: number[]; // 学员ID列表
}

// 班级学员批量创建响应类型 - 根据最新 OpenAPI 文档
export interface ClassStudentBatchCreateResponse {
  success_count: number; // 成功创建的数量
  total_count: number; // 总数量
  message: string; // 结果信息
}

// 班级学员批量删除请求类型 - 根据最新 OpenAPI 文档
export interface ClassStudentBatchDeleteRequest {
  tenant_id: number;
  class_id: number;
  class_student_ids: number[]; // 班级学员关系ID列表
}

// 班级学员批量删除响应类型 - 根据最新 OpenAPI 文档
export interface ClassStudentBatchDeleteResponse {
  success_count: number; // 成功删除的数量
  deleted_ids: number[]; // 删除的关系ID列表
}

// 班级学员批量排序请求类型 - 根据最新 OpenAPI 文档
export interface ClassStudentBatchOrderRequest {
  tenant_id: number;
  class_id: number;
  class_students: Array<{
    id: number; // 班级学员关系ID
    priority: number; // 新的优先级
  }>;
}

// 班级学员批量排序响应类型 - 根据最新 OpenAPI 文档
export interface ClassStudentBatchOrderResponse {
  success_count: number; // 成功更新的数量
  total_count: number; // 总数量
  updated_class_students: ClassStudentWithStudentInfoResponse[]; // 更新后的班级学员关系列表
}

// 获取班级学员列表
export const getClazzStudents = (clazzId: number, tenantId: number, params?: { skip?: number; limit?: number; active?: number }): Promise<ClassStudentListResponse> => {
  const queryParams = {
    tenant_id: tenantId,
    ...params
  };
  return get(`/sys/clazz/${clazzId}/students`, queryParams);
};

// 添加学员到班级
export const createClazzStudent = (clazzId: number, clazzStudent: ClassStudentCreate): Promise<ClassStudentWithStudentInfoResponse> => {
  return post(`/sys/clazz/${clazzId}/students`, clazzStudent);
};

// 更新班级学员
export const updateClazzStudent = (clazzId: number, clazzStudentId: number, clazzStudent: ClassStudentUpdate): Promise<ClassStudentWithStudentInfoResponse> => {
  return put(`/sys/clazz/${clazzId}/students/${clazzStudentId}`, clazzStudent);
};

// 批量创建班级学员
export const batchCreateClazzStudents = (createData: ClassStudentBatchCreateRequest): Promise<ClassStudentBatchCreateResponse> => {
  return post('/sys/clazz/students/batch', createData);
};

// 批量删除班级学员
export const batchDeleteClazzStudents = (deleteData: ClassStudentBatchDeleteRequest): Promise<ClassStudentBatchDeleteResponse> => {
  return del('/sys/clazz/students/batch', { data: deleteData });
};

// 批量更新班级学员排序
export const batchUpdateClazzStudentOrder = (orderData: ClassStudentBatchOrderRequest): Promise<ClassStudentBatchOrderResponse> => {
  return put('/sys/clazz/students/batch/order', orderData);
}; 