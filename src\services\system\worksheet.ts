import { get, post, put, del } from '../api';

// 工作表相关类型定义
export interface WorksheetCreate {
  tenant_id: number;
  title: string;
  intro?: string;
  bgtext?: string;
  duration?: number;
  version?: string;
  notes?: string;
  content?: string;
  type?: number;
  subject_id?: number;
  published?: number;
  active?: number;
}

export interface WorksheetUpdate {
  title?: string;
  intro?: string;
  bgtext?: string;
  duration?: number;
  version?: string;
  notes?: string;
  content?: string;
  type?: number;
  subject_id?: number;
  published?: number;
  active?: number;
  pic?: string;
}

export interface WorksheetResponse {
  id: number;
  tenant_id: number;
  eid: number;
  title: string;
  intro?: string;
  bgtext?: string;
  duration?: number;
  version?: string;
  notes?: string;
  content?: string;
  type?: number;
  subject_id?: number;
  published: number;
  ctime: string;
  active: number;
  pic?: string;
}

export interface WorksheetListResponse {
  total: number;
  items: WorksheetResponse[];
}

export interface GetWorksheetsParams {
  tenant_id: number;
  skip?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: string;
  title?: string;
  active?: number;
  published?: number;
  start_time?: string;
  end_time?: string;
}

// 获取工作表列表
export const getWorksheets = async (params: GetWorksheetsParams): Promise<WorksheetListResponse> => {
  const response = await get<WorksheetListResponse>('/sys/worksheet', params);
  return response;
};

// 创建工作表
export const createWorksheet = async (data: WorksheetCreate): Promise<WorksheetResponse> => {
  const response = await post<WorksheetResponse>('/sys/worksheet', data);
  return response;
};

// 获取工作表信息
export const getWorksheet = async (id: number): Promise<WorksheetResponse> => {
  const response = await get<WorksheetResponse>(`/sys/worksheet/${id}`);
  return response;
};

// 更新工作表
export const updateWorksheet = async (id: number, data: WorksheetUpdate): Promise<WorksheetResponse> => {
  const response = await put<WorksheetResponse>(`/sys/worksheet/${id}`, data);
  return response;
};

// 删除工作表
export const deleteWorksheet = async (id: number): Promise<{ message: string }> => {
  const response = await del<{ message: string }>(`/sys/worksheet/${id}`);
  return response;
};

// 单元相关类型定义
export interface WorksheetUnitResponse {
  id: number;
  name: string;
  bgtext?: string;
  bgvideo?: string;
  priority?: number;
}

export interface WorksheetUnitQuestionResponse {
  id: number;
  qid: number;
  title: string;
  notes?: string;
}

// 获取工作表的单元列表
export const getWorksheetUnits = async (worksheetId: number, tenantId: number): Promise<WorksheetUnitResponse[]> => {
  const response = await get<WorksheetUnitResponse[]>(`/sys/worksheet/${worksheetId}/units`, { tenant_id: tenantId });
  return response;
};

// 获取单元的问题列表
export const getWorksheetUnitQuestions = async (worksheetId: number, unitId: number, tenantId: number): Promise<WorksheetUnitQuestionResponse[]> => {
  const response = await get<WorksheetUnitQuestionResponse[]>(`/sys/worksheet/${worksheetId}/unit/${unitId}/questions`, { tenant_id: tenantId });
  return response;
};

// 获取工作表单元详情
export const getWorksheetUnit = async (worksheetId: number, unitId: number): Promise<UnitResponse> => {
  const response = await get<UnitResponse>(`/sys/worksheet/${worksheetId}/units/${unitId}`);
  return response;
};

// 单元创建类型定义
export interface UnitCreate {
  tenant_id: number;
  wid: number;
  name: string;
  bgtext?: string;
  bgvideo?: string;
  priority?: number;
}

export interface UnitResponse {
  id: number;
  tenant_id: number;
  wid: number;
  name: string;
  bgtext?: string;
  bgvideo?: string;
  priority: number;
}

// 创建工作表单元
export const createWorksheetUnit = async (data: UnitCreate): Promise<UnitResponse> => {
  const response = await post<UnitResponse>('/sys/worksheet/unit', data);
  return response;
};

// 单元更新类型定义
export interface UnitUpdate {
  name?: string;
  bgtext?: string;
  bgvideo?: string;
  priority?: number;
}

// 单元批量排序类型定义
export interface UnitOrderItem {
  id: number;
  priority: number;
}

export interface UnitBatchOrderRequest {
  tenant_id: number;
  worksheet_id: number;
  units: UnitOrderItem[];
}

export interface UnitBatchOrderResponse {
  success_count: number;
  total_count: number;
  updated_units: UnitResponse[];
}

// 更新工作表单元
export const updateWorksheetUnit = async (worksheetId: number, unitId: number, data: UnitUpdate): Promise<UnitResponse> => {
  const response = await put<UnitResponse>(`/sys/worksheet/${worksheetId}/units/${unitId}`, data);
  return response;
};

// 批量更新单元顺序
export const batchUpdateUnitOrder = async (data: UnitBatchOrderRequest): Promise<UnitBatchOrderResponse> => {
  const response = await put<UnitBatchOrderResponse>('/sys/worksheet/units/batch/order', data);
  return response;
};

// 单元批量删除类型定义
export interface UnitBatchDeleteRequest {
  tenant_id: number;
  worksheet_id: number;
  unit_ids: number[];
}

export interface UnitBatchDeleteResponse {
  success_count: number;
  total_count: number;
  deleted_unit_ids: number[];
}

// 批量删除工作表单元
export const batchDeleteUnits = async (data: UnitBatchDeleteRequest): Promise<UnitBatchDeleteResponse> => {
  const response = await del<UnitBatchDeleteResponse>('/sys/worksheet/units/batch', { data });
  return response;
};

// 删除工作表单元（兼容性保留）
export const deleteWorksheetUnit = async (worksheetId: number, unitId: number): Promise<{ message: string }> => {
  const response = await del<{ message: string }>(`/sys/worksheet/${worksheetId}/units/${unitId}`);
  return response;
};

// 单元问题关联类型定义
export interface UnitQuestionCreate {
  tenant_id: number;
  wid: number;
  uid: number;
  qid: number;
  notes?: string;
}

export interface UnitQuestionBatchCreate {
  tenant_id: number;
  wid: number;
  uid: number;
  questions: {
    qid: number;
    notes?: string;
  }[];
}

export interface UnitQuestionBatchResponse {
  success_count: number;
  total_count: number;
  message: string;
}

// 批量添加问题到单元
export const batchAddQuestionsToUnit = async (data: UnitQuestionBatchCreate): Promise<UnitQuestionBatchResponse> => {
  const response = await post<UnitQuestionBatchResponse>('/sys/worksheet/unit/question', data);
  return response;
};

// 单元问题批量更新类型定义（新增）
export interface UnitQuestionItem {
  id: number;
  priority?: number;
}

export interface UnitQuestionsUpdateRequest {
  tenant_id: number;
  worksheet_id: number;
  unit_id: number;
  questions: UnitQuestionItem[];
}

export interface UnitQuestionsUpdateResponse {
  success: boolean;
  added_count: number;
  updated_count: number;
  deleted_count: number;
  total_count: number;
  message: string;
}

// 批量更新单元问题（新增）
export const updateUnitQuestions = async (data: UnitQuestionsUpdateRequest): Promise<UnitQuestionsUpdateResponse> => {
  const response = await put<UnitQuestionsUpdateResponse>('/sys/worksheet/unit/questions', data);
  return response;
};

// 工作表问题构成关系批量删除类型定义（新增）
export interface WorksheetAsmBatchDeleteRequest {
  tenant_id: number;
  worksheet_id: number;
  unit_id?: number;
  asm_ids: number[];
}

export interface WorksheetAsmBatchDeleteResponse {
  success_count: number;
  total_count: number;
  deleted_asm_ids: number[];
  message: string;
}

// 批量删除工作表问题构成关系（新增）
export const batchDeleteWorksheetAsms = async (data: WorksheetAsmBatchDeleteRequest): Promise<WorksheetAsmBatchDeleteResponse> => {
  const response = await del<WorksheetAsmBatchDeleteResponse>('/sys/worksheet/unit/questions/batch', { data });
  return response;
};

// 工作表问题构成关系批量移动类型定义（新增）
export interface WorksheetAsmBatchMoveRequest {
  tenant_id: number;
  worksheet_id: number;
  target_unit_id: number;
  asm_ids: number[];
}

export interface WorksheetAsmBatchMoveResponse {
  success_count: number;
  total_count: number;
  moved_asm_ids: number[];
  message: string;
}

// 批量移动工作表问题构成关系（新增）
export const batchMoveWorksheetAsms = async (data: WorksheetAsmBatchMoveRequest): Promise<WorksheetAsmBatchMoveResponse> => {
  const response = await put<WorksheetAsmBatchMoveResponse>('/sys/worksheet/unit/questions/batch/move', data);
  return response;
};

// 工作表问题构成关系批量排序类型定义（新增）
export interface WorksheetAsmOrderItem {
  id: number;
  priority: number;
}

export interface WorksheetAsmBatchOrderRequest {
  tenant_id: number;
  worksheet_id: number;
  unit_id?: number;
  items: WorksheetAsmOrderItem[];
}

export interface WorksheetAsmBatchOrderResponse {
  success_count: number;
  total_count: number;
  updated_items: WorksheetAsmOrderItem[];
  message: string;
}

// 批量调整工作表问题构成关系顺序（新增）
export const batchUpdateWorksheetAsmOrder = async (data: WorksheetAsmBatchOrderRequest): Promise<WorksheetAsmBatchOrderResponse> => {
  const response = await put<WorksheetAsmBatchOrderResponse>('/sys/worksheet/unit/questions/batch/order', data);
  return response;
};

// 图片上传相关类型定义
export interface PicUploadURL {
  upload_url: string;
  file_path: string;
  file_url: string;
  expires_at: string;
}

export interface PicDeleteRequest {
  file_path: string;
}

// 获取图片上传URL
export const getPicUploadUrl = async (tenantId: number, fileName: string): Promise<PicUploadURL> => {
  const response = await get<PicUploadURL>('/sys/exercise/pic/upload-url', {
    tenant_id: tenantId,
    file_name: fileName
  });
  return response;
};

// 删除图片文件
export const deletePicFile = async (data: PicDeleteRequest): Promise<void> => {
  await del<void>('/sys/exercise/pic', { data });
};

// Exercise响应类型定义
export interface ExerciseResponse {
  id: number;
  tenant_id: number;
  title: string;
  pic?: string;
  [key: string]: unknown;
}

// 更新练习的pic字段（通过worksheet的eid）
export const updateExercisePic = async (exerciseId: number, tenantId: number, pic: string): Promise<ExerciseResponse> => {
  const response = await put<ExerciseResponse>(`/sys/exercise/${exerciseId}?tenant_id=${tenantId}`, { pic });
  return response;
}; 