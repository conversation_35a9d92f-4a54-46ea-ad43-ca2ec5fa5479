import React, { useState, useEffect, useCallback, useRef } from 'react';
import { 
  Button, 
  Spin, 
  message,
  Tooltip,
  Card,
  Modal,
  Tag,
  Select,
  Empty,
  Input,
  Row,
  Col,
  Typography,
  AutoComplete,
  Avatar
} from 'antd';
import { 
  EditOutlined,
  ReloadOutlined,
  DragOutlined,
  DeleteOutlined,
  Exclamation<PERSON>ircleOutlined,
  RollbackOutlined,
  BookOutlined,
  DatabaseOutlined,
  InfoCircleOutlined,
  LinkOutlined,
  DisconnectOutlined,
  HolderOutlined,
  CommentOutlined,
  ImportOutlined,
  UserSwitchOutlined,
  UserOutlined
} from '@ant-design/icons';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
  useSortable,
} from '@dnd-kit/sortable';
import {
  CSS,
} from '@dnd-kit/utilities';

import { 
  getClazzExercises,
  createClazzExercise,
  updateClazzExercise,
  deleteClazzExercise,
  batchUpdateClazzExerciseOrder,
  batchImportClazzExercisesFromPlan,
  batchSetClazzExerciseTeacher,
  type ClassExerciseWithExerciseInfoResponse,
  type ClassExerciseCreate,
  type ClassExerciseUpdate,
  type ClassExerciseBatchOrderRequest,
  type ClassImportPlanRequest,
  type ClassExerciseBatchSetTeacherRequest
} from '../../../services/system/clazz';
import { getExercises, type ExerciseResponse } from '../../../services/system/exercise';
import { getPlans, type GetPlansParams, type PlanResponse } from '../../../services/system/plan';
import { getTeachers, type TeacherResponse } from '../../../services/system/teacher';
import { showError } from '../../../utils/errorHandler';
import { getGlobalTenantInfo } from '../../../components/GlobalTenantSelector';

const { Text } = Typography;
const { Search } = Input;
const { Option } = Select;
const { confirm } = Modal;

// 可拖拽的班级练习项组件
interface SortableClazzExerciseItemProps {
  clazzExercise: ClassExerciseWithExerciseInfoResponse;
  index: number;
  isSortMode?: boolean;
  isEditMode?: boolean;
  onDelete: (clazzExerciseId: number) => void;
  onToggleDepend: (clazzExercise: ClassExerciseWithExerciseInfoResponse) => void;
  getExerciseTypeTag: (type: number) => JSX.Element;
  onTeacherSelect: (clazzExercise: ClassExerciseWithExerciseInfoResponse) => void;
}

const SortableClazzExerciseItem: React.FC<SortableClazzExerciseItemProps> = ({
  clazzExercise,
  index,
  isSortMode = false,
  isEditMode = false,
  onDelete,
  onToggleDepend,
  getExerciseTypeTag,
  onTeacherSelect
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: `exercise-${clazzExercise.id}` });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition: transition || 'all 0.2s',
    opacity: isDragging ? 0.5 : 1,
    backgroundColor: 'transparent',
    border: '1px solid #f0f0f0',
    borderRadius: '6px',
    padding: '12px',
    marginBottom: '8px',
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      onMouseEnter={(e) => {
        e.currentTarget.style.backgroundColor = '#f5f5f5';
        e.currentTarget.style.borderColor = '#d9d9d9';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.backgroundColor = 'transparent';
        e.currentTarget.style.borderColor = '#f0f0f0';
      }}
    >
      <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
        {isSortMode && (
          <div 
            {...listeners}
            style={{ 
              cursor: 'grab',
              color: '#999',
              fontSize: '14px'
            }}
          >
            <HolderOutlined />
          </div>
        )}

        {/* 序号 */}
        <div style={{ 
          backgroundColor: '#666',
          color: 'white',
          width: '20px',
          height: '20px',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '12px',
          fontWeight: 'bold',
          flexShrink: 0
        }}>
          {index + 1}
        </div>

        {/* 图片 */}
        <div style={{ 
          width: '48px', 
          height: '48px', 
          borderRadius: '4px',
          overflow: 'hidden',
          backgroundColor: '#f5f5f5',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexShrink: 0
        }}>
          {clazzExercise.pic ? (
            <img 
              src={clazzExercise.pic} 
              alt={clazzExercise.title}
              style={{ 
                width: '100%', 
                height: '100%', 
                objectFit: 'cover' 
              }}
              onError={(e) => {
                e.currentTarget.style.display = 'none';
                e.currentTarget.parentElement!.innerHTML = '<div style="width: 100%; height: 100%; background: #f0f0f0; display: flex; align-items: center; justify-content: center; color: #ccc; font-size: 12px;">无图</div>';
              }}
            />
          ) : (
            <div style={{ color: '#ccc', fontSize: '12px' }}>无图</div>
          )}
        </div>

        {/* 标题和类型 */}
        <div style={{ 
          flex: 1, 
          minWidth: 0,
          height: '48px',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between'
        }}>
          {/* 标题行：标题 + info图标 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
            <Text strong style={{ 
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              fontSize: '14px',
              lineHeight: '20px'
            }}>
              {clazzExercise.title}
            </Text>
            {/* 信息图标（悬停显示简介） */}
            {clazzExercise.intro && (
              <Tooltip title={clazzExercise.intro} placement="top">
                <InfoCircleOutlined 
                  style={{ 
                    color: '#999', 
                    fontSize: '12px',
                    flexShrink: 0
                  }} 
                />
              </Tooltip>
            )}
          </div>
          {/* 类型标签行：底部对齐 */}
          <div style={{ alignSelf: 'flex-start' }}>
            {getExerciseTypeTag(clazzExercise.type)}
          </div>
        </div>

        {/* 依赖图标（靠右） */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
          {/* 老师头像 */}
          <Tooltip title={clazzExercise.tname || '暂无'}>
            <Avatar
              src={clazzExercise.tavatar || undefined}
              icon={(!clazzExercise.tavatar || clazzExercise.tavatar.trim() === '') ? <UserOutlined /> : undefined}
              size={48}
              style={{ 
                cursor: isEditMode ? 'pointer' : 'default',
                backgroundColor: (!clazzExercise.tavatar || clazzExercise.tavatar.trim() === '') ? '#d9d9d9' : undefined,
                color: (!clazzExercise.tavatar || clazzExercise.tavatar.trim() === '') ? '#999' : undefined
              }}
              onClick={() => {
                if (isEditMode) {
                  onTeacherSelect(clazzExercise);
                }
              }}
            />
          </Tooltip>
          
          <Tooltip title={
            isEditMode 
              ? `点击${clazzExercise.depend === 1 ? '取消' : '设置'}依赖` 
              : (clazzExercise.depend === 1 ? '依赖前一个练习' : '不依赖')
          }>
            <div
              style={{ 
                cursor: isEditMode ? 'pointer' : 'default',
                padding: '4px',
                borderRadius: '4px',
                transition: 'background-color 0.2s',
              }}
              onMouseEnter={(e) => {
                if (isEditMode) {
                  e.currentTarget.style.backgroundColor = '#f5f5f5';
                }
              }}
              onMouseLeave={(e) => {
                if (isEditMode) {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }
              }}
              onClick={() => {
                if (isEditMode) {
                  onToggleDepend(clazzExercise);
                }
              }}
            >
              {clazzExercise.depend === 1 ? (
                <LinkOutlined style={{ color: '#1677ff', fontSize: '14px' }} />
              ) : (
                <DisconnectOutlined style={{ color: '#ff4d4f', fontSize: '14px' }} />
              )}
            </div>
          </Tooltip>
          
          {/* 编辑状态下的删除按钮 */}
          {isEditMode && (
            <Tooltip title="删除">
              <Button
                type="text"
                icon={<DeleteOutlined />}
                size="small"
                danger
                onClick={() => onDelete(clazzExercise.id)}
                style={{ marginLeft: '4px' }}
              />
            </Tooltip>
          )}
        </div>
      </div>
    </div>
  );
};

interface ClazzExercisesTabProps {
  clazzId: number;
}

const ClazzExercisesTab: React.FC<ClazzExercisesTabProps> = ({ clazzId }) => {
  // 拖拽排序传感器
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // 班级练习相关状态
  const [clazzExercises, setClazzExercises] = useState<ClassExerciseWithExerciseInfoResponse[]>([]);
  const [exercisesLoading, setExercisesLoading] = useState(false);
  const [availableExercises, setAvailableExercises] = useState<ExerciseResponse[]>([]);
  const [selectedTenantId, setSelectedTenantId] = useState<number | undefined>(undefined);
  
  // 排序模式相关状态
  const [isSortMode, setIsSortMode] = useState(false);
  
  // 编辑模式相关状态
  const [isEditMode, setIsEditMode] = useState(false);
  
  // 练习库搜索相关状态
  const [libraryLoading, setLibraryLoading] = useState(false);
  const [searchType, setSearchType] = useState<number | undefined>(undefined); // 默认全部
  const [searchTitle, setSearchTitle] = useState('');
  const [searchTitleValue, setSearchTitleValue] = useState('');
  const [searchNotes, setSearchNotes] = useState('');
  const [searchNotesValue, setSearchNotesValue] = useState('');

  // 跟踪已选中的练习ID列表（本班级中的练习）
  const [selectedExerciseIds, setSelectedExerciseIds] = useState<Set<number>>(new Set());

  // 导入相关状态
  const [isImportModalVisible, setIsImportModalVisible] = useState(false);
  const [planOptions, setPlanOptions] = useState<Array<{value: string, label: React.ReactNode, id: number}>>([]);
  const [plansLoading, setPlansLoading] = useState(false);
  const [selectedPlanId, setSelectedPlanId] = useState<number | undefined>(undefined);
  const [planSearchValue, setPlanSearchValue] = useState('');
  const [importLoading, setImportLoading] = useState(false);

  // 老师选择相关状态
  const [isTeacherSelectModalVisible, setIsTeacherSelectModalVisible] = useState(false);
  const [teacherOptions, setTeacherOptions] = useState<Array<{value: string, label: React.ReactNode, id: number}>>([]);
  const [teachersLoading, setTeachersLoading] = useState(false);
  const [selectedTeacherId, setSelectedTeacherId] = useState<number | undefined>(undefined);
  const [teacherSearchValue, setTeacherSearchValue] = useState('');
  const [teacherSelectLoading, setTeacherSelectLoading] = useState(false);
  const [currentSelectingExercise, setCurrentSelectingExercise] = useState<ClassExerciseWithExerciseInfoResponse | null>(null);

  // 批量设置老师相关状态
  const [isBatchTeacherModalVisible, setIsBatchTeacherModalVisible] = useState(false);
  const [batchTeacherOptions, setBatchTeacherOptions] = useState<Array<{value: string, label: React.ReactNode, id: number}>>([]);
  const [batchTeachersLoading, setBatchTeachersLoading] = useState(false);
  const [batchSelectedTeacherId, setBatchSelectedTeacherId] = useState<number | undefined>(undefined);
  const [batchTeacherSearchValue, setBatchTeacherSearchValue] = useState('');
  const [batchTeacherLoading, setBatchTeacherLoading] = useState(false);

  // 创建对搜索框的引用
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const searchTitleInputRef = useRef<any>(null);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const searchNotesInputRef = useRef<any>(null);

  // 防抖处理的timer引用
  const planSearchTimerRef = useRef<NodeJS.Timeout | null>(null);
  const teacherSearchTimerRef = useRef<NodeJS.Timeout | null>(null);
  const batchTeacherSearchTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 获取租户信息
  const loadGlobalTenant = useCallback(() => {
    const tenantInfo = getGlobalTenantInfo();
    if (tenantInfo) {
      setSelectedTenantId(tenantInfo.id);
      return tenantInfo;
    } else {
      setSelectedTenantId(undefined);
      return null;
    }
  }, []);

  // 监听全局租户变化
  useEffect(() => {
    loadGlobalTenant();

    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'globalTenant') {
        loadGlobalTenant();
      }
    };

    const handleGlobalTenantChange = (e: Event) => {
      const customEvent = e as CustomEvent;
      const { id } = customEvent.detail;
      if (id) {
        setSelectedTenantId(id);
      } else {
        setSelectedTenantId(undefined);
      }
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('globalTenantChanged', handleGlobalTenantChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('globalTenantChanged', handleGlobalTenantChange);
    };
  }, [loadGlobalTenant]);

  // 获取班级练习列表
  const fetchClazzExercises = useCallback(async () => {
    if (!clazzId || !selectedTenantId) return;
    
    try {
      setExercisesLoading(true);
      const response = await getClazzExercises(clazzId, selectedTenantId);
      setClazzExercises(response.items);
      // 更新已选中的练习ID列表
      const selectedIds = new Set(response.items.map(item => item.eid));
      setSelectedExerciseIds(selectedIds);
    } catch (error) {
      showError(error, '获取班级练习失败');
    } finally {
      setExercisesLoading(false);
    }
  }, [clazzId, selectedTenantId]);

  // 获取练习库
  const fetchExerciseLibrary = useCallback(async () => {
    if (!selectedTenantId) {
      setAvailableExercises([]);
      return;
    }

    setLibraryLoading(true);
    try {
      const exercises = await getExercises({
        tenant_id: selectedTenantId,
        active: 1,
        published: 1,
        type: searchType, // 可选类型筛选，undefined表示全部
        limit: 100,
        skip: 0
      });
      
      // 手动过滤标题和备注
      let filteredExercises = exercises;
      if (searchTitle) {
        filteredExercises = filteredExercises.filter(exercise => 
          exercise.title.toLowerCase().includes(searchTitle.toLowerCase())
        );
      }
      if (searchNotes) {
        filteredExercises = filteredExercises.filter(exercise => 
          exercise.notes?.toLowerCase().includes(searchNotes.toLowerCase())
        );
      }
      
      setAvailableExercises(filteredExercises);
    } catch (error) {
      showError(error, '获取练习库失败');
    } finally {
      setLibraryLoading(false);
    }
  }, [selectedTenantId, searchType, searchTitle, searchNotes]);

  // 搜索练习计划列表
  const searchPlans = useCallback(async (searchValue: string) => {
    if (!selectedTenantId || !searchValue.trim()) {
      setPlanOptions([]);
      return;
    }

    setPlansLoading(true);
    try {
      const params: GetPlansParams = {
        tenant_id: selectedTenantId,
        active: 1,
        limit: 10, // 最多10个结果
        skip: 0,
        name: searchValue.trim()
      };
      
      const response = await getPlans(params);
      const options = response.items.map((plan: PlanResponse) => ({
        value: plan.name,
        id: plan.id,
        label: (
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <div style={{ 
              width: '24px', 
              height: '24px', 
              borderRadius: '4px',
              overflow: 'hidden',
              backgroundColor: '#f5f5f5',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              flexShrink: 0
            }}>
              {plan.pic ? (
                <img 
                  src={plan.pic} 
                  alt={plan.name}
                  style={{ 
                    width: '100%', 
                    height: '100%', 
                    objectFit: 'cover' 
                  }}
                />
              ) : (
                <div style={{ color: '#ccc', fontSize: '10px' }}>无图</div>
              )}
            </div>
            <span>{plan.name}</span>
          </div>
        )
      }));
      setPlanOptions(options);
    } catch (error) {
      showError(error, '搜索练习计划失败');
      setPlanOptions([]);
    } finally {
      setPlansLoading(false);
    }
  }, [selectedTenantId]);

  // 搜索老师列表
  const searchTeachers = useCallback(async (searchValue: string) => {
    if (!selectedTenantId || !searchValue.trim()) {
      setTeacherOptions([]);
      return;
    }

    setTeachersLoading(true);
    try {
      const response = await getTeachers(selectedTenantId, {
        name: searchValue.trim(),
        limit: 10,
        skip: 0
      });
      
      const options = response.items.map((teacher: TeacherResponse) => ({
        value: teacher.name,
        id: teacher.id,
        label: (
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <Avatar 
              src={teacher.avatar || undefined} 
              icon={(!teacher.avatar || teacher.avatar.trim() === '') ? <UserOutlined /> : undefined}
              size={24}
              style={{ 
                backgroundColor: (!teacher.avatar || teacher.avatar.trim() === '') ? '#d9d9d9' : undefined,
                color: (!teacher.avatar || teacher.avatar.trim() === '') ? '#999' : undefined
              }}
            />
            <span>{teacher.name}</span>
          </div>
        )
      }));
      setTeacherOptions(options);
    } catch (error) {
      showError(error, '搜索老师失败');
      setTeacherOptions([]);
    } finally {
      setTeachersLoading(false);
    }
  }, [selectedTenantId]);

  // 搜索老师列表（批量）
  const searchBatchTeachers = useCallback(async (searchValue: string) => {
    if (!selectedTenantId || !searchValue.trim()) {
      setBatchTeacherOptions([]);
      return;
    }

    setBatchTeachersLoading(true);
    try {
      const response = await getTeachers(selectedTenantId, {
        name: searchValue.trim(),
        limit: 10,
        skip: 0
      });
      
      const options = response.items.map((teacher: TeacherResponse) => ({
        value: teacher.name,
        id: teacher.id,
        label: (
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <Avatar 
              src={teacher.avatar || undefined} 
              icon={(!teacher.avatar || teacher.avatar.trim() === '') ? <UserOutlined /> : undefined}
              size={24}
              style={{ 
                backgroundColor: (!teacher.avatar || teacher.avatar.trim() === '') ? '#d9d9d9' : undefined,
                color: (!teacher.avatar || teacher.avatar.trim() === '') ? '#999' : undefined
              }}
            />
            <span>{teacher.name}</span>
          </div>
        )
      }));
      setBatchTeacherOptions(options);
    } catch (error) {
      showError(error, '搜索老师失败');
      setBatchTeacherOptions([]);
    } finally {
      setBatchTeachersLoading(false);
    }
  }, [selectedTenantId]);

  // 切换排序模式
  const toggleSortMode = () => {
    if (!isSortMode && !clazzExercises.length) {
      message.warning('没有可排序的练习');
      return;
    }
    setIsSortMode(!isSortMode);
    // 退出排序模式时，也要退出编辑模式
    if (isSortMode) {
      setIsEditMode(false);
    }
  };

  // 切换编辑模式
  const toggleEditMode = () => {
    setIsEditMode(!isEditMode);
    // 进入编辑模式时，退出排序模式
    if (!isEditMode) {
      setIsSortMode(false);
    }
  };

  // 处理拖拽结束
  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    if (!active.id || !over?.id || !clazzId || !selectedTenantId) return;

    // 解析拖拽ID，提取真实ID
    const parseId = (id: string | number) => {
      const idStr = id.toString();
      if (idStr.startsWith('exercise-')) {
        return parseInt(idStr.replace('exercise-', ''));
      }
      return 0;
    };

    const activeId = parseId(active.id);
    const overId = parseId(over.id);

    // 检查是否是练习拖拽
    if (isSortMode && activeId !== overId && activeId > 0 && overId > 0) {
      const oldIndex = clazzExercises.findIndex((exercise) => exercise.id === activeId);
      const newIndex = clazzExercises.findIndex((exercise) => exercise.id === overId);

      if (oldIndex === -1 || newIndex === -1) return;

      const newClazzExercises = arrayMove(clazzExercises, oldIndex, newIndex);
      setClazzExercises(newClazzExercises);
      
      // 立即调用API更新排序
      try {
        const orderData: ClassExerciseBatchOrderRequest = {
          tenant_id: selectedTenantId,
          class_id: clazzId,
          class_exercises: newClazzExercises.map((exercise, index) => ({
            id: exercise.id,
            priority: index + 1
          }))
        };
        
        await batchUpdateClazzExerciseOrder(orderData);
        message.success('练习排序更新成功');
      } catch (error) {
        // 如果API调用失败，恢复原始顺序
        setClazzExercises(clazzExercises);
        showError(error, '练习排序更新失败');
      }
    }
  };

  // 删除练习
  const handleDeleteExercise = (clazzExerciseId: number) => {
    confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: '确定要删除这个练习吗？删除后不可恢复。',
      okText: '确认',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await deleteClazzExercise(clazzExerciseId);
          message.success('删除成功');
          fetchClazzExercises();
        } catch (error) {
          showError(error, '删除练习失败');
        }
      },
    });
  };

  // 切换依赖状态
  const handleToggleDepend = async (clazzExercise: ClassExerciseWithExerciseInfoResponse) => {
    if (!clazzId) return;
    
    try {
      const newDepend = clazzExercise.depend === 1 ? 0 : 1;
      const updateData: ClassExerciseUpdate = {
        depend: newDepend
      };
      await updateClazzExercise(clazzId, clazzExercise.id, updateData);
      message.success(`已${newDepend === 1 ? '设置' : '取消'}依赖`);
      fetchClazzExercises();
    } catch (error) {
      showError(error, '更新依赖状态失败');
    }
  };

  // 刷新练习列表
  const handleRefreshExercises = () => {
    fetchClazzExercises();
  };

  // 打开导入弹窗
  const handleOpenImportModal = () => {
    setIsImportModalVisible(true);
    setSelectedPlanId(undefined);
    setPlanSearchValue('');
    setPlanOptions([]);
  };

  // 关闭导入弹窗
  const handleCloseImportModal = () => {
    setIsImportModalVisible(false);
    setSelectedPlanId(undefined);
    setPlanSearchValue('');
    setPlanOptions([]);
  };

  // 处理计划搜索
  const handlePlanSearch = (value: string) => {
    setPlanSearchValue(value);
    
    // 清除之前的防抖timer
    if (planSearchTimerRef.current) {
      clearTimeout(planSearchTimerRef.current);
    }
    
    // 设置新的防抖timer
    planSearchTimerRef.current = setTimeout(() => {
      if (value.trim()) {
        searchPlans(value);
      } else {
        setPlanOptions([]);
      }
    }, 500); // 500ms防抖延迟
  };

  // 处理计划选择
  const handlePlanSelect = (value: string, option: {value: string, label: React.ReactNode, id: number}) => {
    setSelectedPlanId(option.id);
    setPlanSearchValue(value);
  };

  // 处理老师选择弹窗
  const handleTeacherSelect = (clazzExercise: ClassExerciseWithExerciseInfoResponse) => {
    setCurrentSelectingExercise(clazzExercise);
    setSelectedTeacherId(clazzExercise.tid || undefined);
    // 如果当前练习已有老师，设置搜索框显示老师名称
    if (clazzExercise.tname) {
      setTeacherSearchValue(clazzExercise.tname);
    } else {
      setTeacherSearchValue('');
    }
    setTeacherOptions([]);
    setIsTeacherSelectModalVisible(true);
  };

  // 关闭老师选择弹窗
  const handleCloseTeacherModal = () => {
    setIsTeacherSelectModalVisible(false);
    setCurrentSelectingExercise(null);
    setSelectedTeacherId(undefined);
    setTeacherSearchValue('');
    setTeacherOptions([]);
  };

  // 处理老师搜索
  const handleTeacherSearch = (value: string | undefined) => {
    setTeacherSearchValue(value || '');
    
    // 如果搜索框被清空，同时重置选择的老师ID
    if (!value || !value.trim()) {
      setSelectedTeacherId(undefined);
    }
    
    // 清除之前的防抖timer
    if (teacherSearchTimerRef.current) {
      clearTimeout(teacherSearchTimerRef.current);
    }
    
    // 设置新的防抖timer
    teacherSearchTimerRef.current = setTimeout(() => {
      if (value && value.trim()) {
        searchTeachers(value);
      } else {
        setTeacherOptions([]);
      }
    }, 500); // 500ms防抖延迟
  };

  // 处理老师搜索框变化（包括清空操作）
  const handleTeacherSearchChange = (value: string | undefined) => {
    setTeacherSearchValue(value || '');
    
    // 如果搜索框被清空，同时重置选择的老师ID
    if (!value || !value.trim()) {
      setSelectedTeacherId(undefined);
    }
  };

  // 处理老师选择确认
  const handleTeacherSelectConfirm = (value: string, option: {value: string, label: React.ReactNode, id: number}) => {
    setSelectedTeacherId(option.id);
    setTeacherSearchValue(value);
  };

  // 执行老师设置
  const handleSetTeacher = async () => {
    if (!currentSelectingExercise || !clazzId) {
      message.error('请选择练习');
      return;
    }

    setTeacherSelectLoading(true);
    try {
      const updateData: ClassExerciseUpdate = {
        tid: selectedTeacherId || null
      };
      
      await updateClazzExercise(clazzId, currentSelectingExercise.id, updateData);
      message.success(selectedTeacherId ? '设置老师成功' : '清空老师成功');
      
      // 关闭弹窗并刷新列表
      handleCloseTeacherModal();
      fetchClazzExercises();
    } catch (error) {
      showError(error, '设置老师失败');
    } finally {
      setTeacherSelectLoading(false);
    }
  };

  // 打开批量设置老师弹窗
  const handleOpenBatchTeacherModal = () => {
    setBatchSelectedTeacherId(undefined);
    setBatchTeacherSearchValue('');
    setBatchTeacherOptions([]);
    setIsBatchTeacherModalVisible(true);
  };

  // 关闭批量设置老师弹窗
  const handleCloseBatchTeacherModal = () => {
    setIsBatchTeacherModalVisible(false);
    setBatchSelectedTeacherId(undefined);
    setBatchTeacherSearchValue('');
    setBatchTeacherOptions([]);
  };

  // 处理批量老师搜索
  const handleBatchTeacherSearch = (value: string | undefined) => {
    setBatchTeacherSearchValue(value || '');
    
    // 如果搜索框被清空，同时重置选择的老师ID
    if (!value || !value.trim()) {
      setBatchSelectedTeacherId(undefined);
    }
    
    // 清除之前的防抖timer
    if (batchTeacherSearchTimerRef.current) {
      clearTimeout(batchTeacherSearchTimerRef.current);
    }
    
    // 设置新的防抖timer
    batchTeacherSearchTimerRef.current = setTimeout(() => {
      if (value && value.trim()) {
        searchBatchTeachers(value);
      } else {
        setBatchTeacherOptions([]);
      }
    }, 500); // 500ms防抖延迟
  };

  // 处理批量老师搜索框变化（包括清空操作）
  const handleBatchTeacherSearchChange = (value: string | undefined) => {
    setBatchTeacherSearchValue(value || '');
    
    // 如果搜索框被清空，同时重置选择的老师ID
    if (!value || !value.trim()) {
      setBatchSelectedTeacherId(undefined);
    }
  };

  // 处理批量老师选择确认
  const handleBatchTeacherSelectConfirm = (value: string, option: {value: string, label: React.ReactNode, id: number}) => {
    setBatchSelectedTeacherId(option.id);
    setBatchTeacherSearchValue(value);
  };

  // 执行批量设置老师
  const handleBatchSetTeacher = async () => {
    if (!clazzId || !selectedTenantId) {
      message.error('缺少必要参数');
      return;
    }

    setBatchTeacherLoading(true);
    try {
      const batchData: ClassExerciseBatchSetTeacherRequest = {
        tenant_id: selectedTenantId,
        class_id: clazzId,
        teacher_id: batchSelectedTeacherId || null
      };
      
      const response = await batchSetClazzExerciseTeacher(batchData);
      message.success(`${response.message}，成功更新 ${response.success_count} 个练习`);
      
      // 关闭弹窗并刷新列表
      handleCloseBatchTeacherModal();
      fetchClazzExercises();
    } catch (error) {
      showError(error, '批量设置老师失败');
    } finally {
      setBatchTeacherLoading(false);
    }
  };

  // 执行导入
  const handleImportFromPlan = async () => {
    if (!selectedPlanId || !clazzId || !selectedTenantId) {
      message.error('请选择要导入的练习计划');
      return;
    }

    setImportLoading(true);
    try {
      // 构建批量导入数据
      const importData: ClassImportPlanRequest = {
        tenant_id: selectedTenantId,
        class_id: clazzId,
        plan_id: selectedPlanId
      };
      
      const response = await batchImportClazzExercisesFromPlan(clazzId, importData);
      message.success(`成功导入 ${response.success_count} 个练习`);
      
      // 关闭弹窗并刷新列表
      handleCloseImportModal();
      fetchClazzExercises();
    } catch (error) {
      showError(error, '导入练习计划失败');
    } finally {
      setImportLoading(false);
    }
  };

  // 处理标题搜索
  const handleTitleSearch = (value: string) => {
    if (!isEditMode) return;
    setSearchTitle(value);
    setSearchTitleValue(value);
  };

  // 处理标题搜索输入框值变化
  const handleTitleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!isEditMode) return;
    setSearchTitleValue(e.target.value);
  };

  // 处理备注搜索
  const handleNotesSearch = (value: string) => {
    if (!isEditMode) return;
    setSearchNotes(value);
    setSearchNotesValue(value);
  };

  // 处理备注搜索输入框值变化
  const handleNotesInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!isEditMode) return;
    setSearchNotesValue(e.target.value);
  };

  // 重置练习库搜索
  const handleResetLibrarySearch = () => {
    if (!isEditMode) return;
    setSearchType(undefined);
    setSearchTitle('');
    setSearchTitleValue('');
    setSearchNotes('');
    setSearchNotesValue('');
  };

  // 处理练习库点击 - 添加或删除班级-练习关系
  const handleLibraryExerciseClick = async (exercise: ExerciseResponse) => {
    if (!isEditMode || !clazzId) return;
    
    if (!selectedTenantId) {
      message.error('请先在顶部导航栏选择租户');
      return;
    }

    const isSelected = selectedExerciseIds.has(exercise.id);
    
    try {
      if (isSelected) {
        // 删除班级-练习关系
        const clazzExercise = clazzExercises.find(ce => ce.eid === exercise.id);
        if (clazzExercise) {
          await deleteClazzExercise(clazzExercise.id);
          message.success('已从班级中移除练习');
          
          // 更新选中状态
          const newSelectedIds = new Set(selectedExerciseIds);
          newSelectedIds.delete(exercise.id);
          setSelectedExerciseIds(newSelectedIds);
          
          // 重新获取本班级练习
          await fetchClazzExercises();
        }
      } else {
        // 创建班级-练习关系
        const createData: ClassExerciseCreate = {
          tenant_id: selectedTenantId,
          cid: clazzId,
          eid: exercise.id,
          priority: clazzExercises.length + 1
        };
        
        await createClazzExercise(clazzId, createData);
        message.success('已添加练习到班级');
        
        // 更新选中状态
        const newSelectedIds = new Set(selectedExerciseIds);
        newSelectedIds.add(exercise.id);
        setSelectedExerciseIds(newSelectedIds);
        
        // 重新获取本班级练习
        await fetchClazzExercises();
      }
    } catch (error) {
      showError(error, isSelected ? '移除练习失败' : '添加练习失败');
    }
  };

  // 获取练习类型标签
  const getExerciseTypeTag = (type: number) => {
    const typeMap: { [key: number]: { name: string; color: string } } = {
      1: { name: '作业单', color: 'blue' },
      2: { name: '模拟场景', color: 'pink' },
    };
    const typeInfo = typeMap[type] || { name: '未知类型', color: 'default' };
    return <Tag color={typeInfo.color}>{typeInfo.name}</Tag>;
  };

  useEffect(() => {
    if (selectedTenantId && clazzId) {
      fetchClazzExercises();
    }
  }, [fetchClazzExercises, selectedTenantId, clazzId]);

  // 监听练习库搜索参数变化
  useEffect(() => {
    if (isEditMode) {
      fetchExerciseLibrary();
    }
  }, [fetchExerciseLibrary, isEditMode, searchType, searchTitle, searchNotes]);

  // 监听ESC键退出编辑状态
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isEditMode) {
        setIsEditMode(false);
        message.info('已退出编辑模式');
      }
    };

    // 添加键盘事件监听器
    document.addEventListener('keydown', handleKeyDown);

    // 清理函数
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isEditMode]);

  // 清理防抖timer，防止内存泄漏
  useEffect(() => {
    return () => {
      if (planSearchTimerRef.current) {
        clearTimeout(planSearchTimerRef.current);
      }
      if (teacherSearchTimerRef.current) {
        clearTimeout(teacherSearchTimerRef.current);
      }
      if (batchTeacherSearchTimerRef.current) {
        clearTimeout(batchTeacherSearchTimerRef.current);
      }
    };
  }, []);

  // 不需要在弹窗打开时自动搜索计划，移除此useEffect

  return (
    <Row gutter={16} style={{ margin: '0px', height: '400px' }}>
      {/* 左侧分栏：本班级练习 */}
      <Col span={10} style={{ height: '100%' }}>
        <Card
          title={
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <span>
                <BookOutlined style={{ marginRight: 8 }} />
                {isSortMode ? '练习题排序' : isEditMode ? '练习题编辑' : '本班级练习'}
              </span>
              <div style={{ display: 'flex', gap: '4px' }}>
                <Tooltip title="从练习计划导入">
                  <Button
                    type="primary"
                    icon={<ImportOutlined />}
                    onClick={handleOpenImportModal}
                    disabled={!isEditMode}
                    size="small"
                  />
                </Tooltip>
                <Tooltip title="批量设置老师">
                  <Button
                    type="primary"
                    icon={<UserSwitchOutlined />}
                    onClick={handleOpenBatchTeacherModal}
                    disabled={!isEditMode}
                    size="small"
                  />
                </Tooltip>
                <Tooltip title="刷新列表">
                  <Button
                    type="primary"
                    icon={<ReloadOutlined />}
                    onClick={handleRefreshExercises}
                    loading={exercisesLoading}
                    disabled={isSortMode || isEditMode}
                    size="small"
                  />
                </Tooltip>
                <Tooltip title={isEditMode ? "退出编辑" : "编辑"}>
                  <Button
                    type={isEditMode ? "primary" : "text"}
                    icon={isEditMode ? <RollbackOutlined /> : <EditOutlined />}
                    onClick={toggleEditMode}
                    disabled={isSortMode}
                    size="small"
                  />
                </Tooltip>
                <Tooltip title={isSortMode ? "退出排序" : "调整排序"}>
                  <Button
                    type={isSortMode ? "primary" : "text"}
                    icon={<DragOutlined />}
                    onClick={toggleSortMode}
                    disabled={isEditMode}
                    size="small"
                  />
                </Tooltip>
              </div>
            </div>
          }
          style={{ height: '100%' }}
          styles={{ 
            body: {
              height: 'calc(100% - 57px)',
              overflow: 'auto',
              padding: '12px'
            }
          }}
        >
          <DndContext 
            sensors={sensors} 
            collisionDetection={closestCenter} 
            onDragEnd={handleDragEnd}
          >
            <Spin spinning={exercisesLoading}>
              {clazzExercises.length === 0 ? (
                <Empty
                  description="暂无练习数据"
                  style={{ marginTop: '60px' }}
                />
              ) : (
                <SortableContext 
                  items={clazzExercises.map(exercise => `exercise-${exercise.id}`)} 
                  strategy={verticalListSortingStrategy}
                >
                  <div>
                    {clazzExercises.map((clazzExercise, index) => (
                      <SortableClazzExerciseItem
                        key={clazzExercise.id}
                        clazzExercise={clazzExercise}
                        index={index}
                        isSortMode={isSortMode}
                        isEditMode={isEditMode}
                        onDelete={handleDeleteExercise}
                        onToggleDepend={handleToggleDepend}
                        getExerciseTypeTag={getExerciseTypeTag}
                        onTeacherSelect={handleTeacherSelect}
                      />
                    ))}
                  </div>
                </SortableContext>
              )}
            </Spin>
          </DndContext>
        </Card>
      </Col>

      {/* 右侧分栏：练习库 */}
      <Col span={14} style={{ height: '100%' }}>
        <Card
          title={
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <span>
                <DatabaseOutlined style={{ marginRight: 8 }} />
                练习库
                {!isEditMode && (
                  <span style={{ fontSize: '12px', color: '#999', marginLeft: '8px' }}>
                    （请先点击"编辑"按钮）
                  </span>
                )}
                {isEditMode && (
                  <span style={{ fontSize: '12px', color: '#999', marginLeft: '8px' }}>
                    （请输入条件缩小搜索范围）
                  </span>
                )}
              </span>
              <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                <Select
                  placeholder="类型"
                  value={searchType}
                  onChange={setSearchType}
                  style={{ width: 100 }}
                  size="small"
                  disabled={!isEditMode}
                  allowClear
                >
                  <Option value={1}>作业单</Option>
                  <Option value={2}>场景</Option>
                </Select>
                <Search
                  placeholder="搜索标题"
                  allowClear
                  onSearch={handleTitleSearch}
                  style={{ width: 120 }}
                  ref={searchTitleInputRef}
                  value={searchTitleValue}
                  onChange={handleTitleInputChange}
                  size="small"
                  disabled={!isEditMode}
                />
                <Search
                  placeholder="搜索备注"
                  allowClear
                  onSearch={handleNotesSearch}
                  style={{ width: 120 }}
                  ref={searchNotesInputRef}
                  value={searchNotesValue}
                  onChange={handleNotesInputChange}
                  size="small"
                  disabled={!isEditMode}
                />
                <Tooltip title="重置搜索">
                  <Button
                    type="primary"
                    icon={<ReloadOutlined />}
                    onClick={handleResetLibrarySearch}
                    size="small"
                    disabled={!isEditMode}
                  />
                </Tooltip>
              </div>
            </div>
          }
          style={{ 
            height: '100%',
            opacity: isEditMode ? 1 : 0.6
          }}
          styles={{ 
            body: {
              height: 'calc(100% - 57px)',
              overflow: 'auto',
              padding: '12px'
            }
          }}
        >
          <Spin spinning={libraryLoading}>
            {!isEditMode ? (
              <Empty
                description={
                  <div>
                    <div>练习库暂不可用</div>
                    <div style={{ fontSize: '12px', color: '#999', marginTop: '8px' }}>
                      请先点击左上角的"编辑"按钮进入编辑模式
                    </div>
                  </div>
                }
                style={{ marginTop: '60px' }}
              />
            ) : availableExercises.length === 0 ? (
              <Empty
                description="暂无符合条件的练习"
                style={{ marginTop: '60px' }}
              />
            ) : (
              <div>
                {availableExercises.map((exercise) => {
                  const isSelected = selectedExerciseIds.has(exercise.id);
                  return (
                    <div
                      key={exercise.id}
                      style={{
                        border: isSelected ? '1px solid #1890ff' : '1px solid #f0f0f0',
                        borderRadius: '6px',
                        padding: '12px',
                        marginBottom: '8px',
                        cursor: isEditMode ? 'pointer' : 'not-allowed',
                        transition: 'all 0.2s',
                        backgroundColor: isSelected ? '#e6f7ff' : 'transparent',
                        opacity: isEditMode ? 1 : 0.6
                      }}
                      onMouseEnter={(e) => {
                        if (isEditMode && !isSelected) {
                          e.currentTarget.style.backgroundColor = '#f5f5f5';
                          e.currentTarget.style.borderColor = '#d9d9d9';
                        }
                      }}
                      onMouseLeave={(e) => {
                        if (isEditMode && !isSelected) {
                          e.currentTarget.style.backgroundColor = 'transparent';
                          e.currentTarget.style.borderColor = '#f0f0f0';
                        }
                      }}
                      onClick={() => {
                        if (isEditMode) {
                          handleLibraryExerciseClick(exercise);
                        }
                      }}
                    >
                      <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                        {/* 图片 */}
                        <div style={{ 
                          width: '48px', 
                          height: '48px', 
                          borderRadius: '4px',
                          overflow: 'hidden',
                          backgroundColor: '#f5f5f5',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          flexShrink: 0
                        }}>
                          {exercise.pic ? (
                            <img 
                              src={exercise.pic} 
                              alt={exercise.title}
                              style={{ 
                                width: '100%', 
                                height: '100%', 
                                objectFit: 'cover' 
                              }}
                              onError={(e) => {
                                e.currentTarget.style.display = 'none';
                                e.currentTarget.parentElement!.innerHTML = '<div style="width: 100%; height: 100%; background: #f0f0f0; display: flex; align-items: center; justify-content: center; color: #ccc; font-size: 12px;">无图</div>';
                              }}
                            />
                          ) : (
                            <div style={{ color: '#ccc', fontSize: '12px' }}>无图</div>
                          )}
                        </div>

                        {/* 标题和类型 */}
                        <div style={{ 
                          flex: 1, 
                          minWidth: 0,
                          height: '48px',
                          display: 'flex',
                          flexDirection: 'column',
                          justifyContent: 'space-between'
                        }}>
                          {/* 标题行：标题 + info图标 + 备注图标 */}
                          <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                            <Text strong style={{ 
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap',
                              fontSize: '14px',
                              lineHeight: '20px'
                            }}>
                              {exercise.title}
                            </Text>
                            {/* 信息图标（悬停显示简介） */}
                            {exercise.intro && (
                              <Tooltip title={exercise.intro} placement="top">
                                <InfoCircleOutlined 
                                  style={{ 
                                    color: '#999', 
                                    fontSize: '12px',
                                    flexShrink: 0
                                  }} 
                                />
                              </Tooltip>
                            )}
                            {/* 备注图标（悬停显示备注） */}
                            {exercise.notes && (
                              <Tooltip title={exercise.notes} placement="top">
                                <CommentOutlined 
                                  style={{ 
                                    color: '#999', 
                                    fontSize: '12px',
                                    flexShrink: 0
                                  }} 
                                />
                              </Tooltip>
                            )}
                          </div>
                          {/* 类型标签行：底部对齐 */}
                          <div style={{ alignSelf: 'flex-start' }}>
                            {getExerciseTypeTag(exercise.type)}
                          </div>
                        </div>

                        {/* 选中状态图标（靠右） */}
                        {isSelected && (
                          <div style={{ display: 'flex', alignItems: 'center' }}>
                            <Tag color="success" style={{ margin: 0, fontSize: '12px' }}>
                              已选
                            </Tag>
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </Spin>
        </Card>
      </Col>

      {/* 导入练习计划弹窗 */}
      <Modal
        title="从练习计划导入"
        open={isImportModalVisible}
        onCancel={handleCloseImportModal}
        onOk={handleImportFromPlan}
        okText="确认导入"
        cancelText="取消"
        okButtonProps={{ loading: importLoading }}
        width={600}
      >
        <div style={{ marginBottom: '16px' }}>
          <Text type="secondary" style={{ marginBottom: '8px', display: 'block' }}>
            请输入练习计划名称进行搜索：
          </Text>
          <AutoComplete
            style={{ width: '100%' }}
            options={planOptions}
            value={planSearchValue}
            onSearch={handlePlanSearch}
            onSelect={handlePlanSelect}
            placeholder="请输入练习计划名称"
            allowClear
            notFoundContent={plansLoading ? <Spin size="small" /> : (planSearchValue ? '暂无匹配的练习计划' : '请输入关键词搜索')}
          />
        </div>
      </Modal>

      {/* 选择老师弹窗 */}
      <Modal
        title="选择老师"
        open={isTeacherSelectModalVisible}
        onCancel={handleCloseTeacherModal}
        onOk={handleSetTeacher}
        okText="确认"
        cancelText="取消"
        okButtonProps={{ loading: teacherSelectLoading }}
        width={600}
      >
        <div style={{ marginBottom: '16px' }}>
          <Text type="secondary" style={{ marginBottom: '8px', display: 'block' }}>
            请选择老师：
          </Text>
          <AutoComplete
            style={{ width: '100%' }}
            options={teacherOptions}
            value={teacherSearchValue}
            onChange={handleTeacherSearchChange}
            onSearch={handleTeacherSearch}
            onSelect={handleTeacherSelectConfirm}
            placeholder="请输入老师名称"
            allowClear
            popupRender={(menu) => {
              // 如果已选择老师且有头像/姓名，显示选中状态
              if (selectedTeacherId && teacherSearchValue && !teachersLoading) {
                return (
                  <div>
                    <div 
                      style={{ 
                        padding: '8px 12px',
                        borderBottom: '1px solid #f0f0f0',
                        backgroundColor: '#f5f5f5',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '8px'
                      }}
                    >
                      <Avatar 
                          src={currentSelectingExercise?.tavatar || undefined} 
                          icon={(!currentSelectingExercise?.tavatar || currentSelectingExercise?.tavatar.trim() === '') ? <UserOutlined /> : undefined}
                         size={24}
                         style={{ 
                            backgroundColor: (!currentSelectingExercise?.tavatar || currentSelectingExercise?.tavatar.trim() === '') ? '#d9d9d9' : undefined,
                            color: (!currentSelectingExercise?.tavatar || currentSelectingExercise?.tavatar.trim() === '') ? '#999' : undefined
                         }}
                      />
                      <span style={{ color: '#1890ff', fontWeight: 'bold' }}>
                        当前选中: {teacherSearchValue}
                      </span>
                    </div>
                    {menu}
                  </div>
                );
              }
              return menu;
            }}
            notFoundContent={teachersLoading ? <Spin size="small" /> : (teacherSearchValue ? '暂无匹配的老师' : '请输入关键词搜索')}
          />
          {selectedTeacherId && (
            <div style={{ marginTop: '8px', fontSize: '12px', color: '#999' }}>
              提示：清空搜索框可以移除老师设置
            </div>
          )}
        </div>
      </Modal>

      {/* 批量设置老师弹窗 */}
      <Modal
        title="批量设置老师"
        open={isBatchTeacherModalVisible}
        onCancel={handleCloseBatchTeacherModal}
        onOk={handleBatchSetTeacher}
        okText="确认"
        cancelText="取消"
        okButtonProps={{ loading: batchTeacherLoading }}
        width={600}
      >
        <div style={{ marginBottom: '16px' }}>
          <Text type="secondary" style={{ marginBottom: '8px', display: 'block' }}>
            请选择要设置的老师：
          </Text>
          <AutoComplete
            style={{ width: '100%' }}
            options={batchTeacherOptions}
            value={batchTeacherSearchValue}
            onChange={handleBatchTeacherSearchChange}
            onSearch={handleBatchTeacherSearch}
            onSelect={handleBatchTeacherSelectConfirm}
            placeholder="请输入老师名称"
            allowClear
            popupRender={(menu) => {
              // 如果已选择老师，显示选中状态
              if (batchSelectedTeacherId && batchTeacherSearchValue && !batchTeachersLoading) {
                return (
                  <div>
                    <div 
                      style={{ 
                        padding: '8px 12px',
                        borderBottom: '1px solid #f0f0f0',
                        backgroundColor: '#f5f5f5',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '8px'
                      }}
                    >
                      <Avatar 
                        icon={<UserOutlined />}
                        size={24}
                        style={{ 
                          backgroundColor: '#d9d9d9',
                          color: '#999'
                        }}
                      />
                      <span style={{ color: '#1890ff', fontWeight: 'bold' }}>
                        当前选中: {batchTeacherSearchValue}
                      </span>
                    </div>
                    {menu}
                  </div>
                );
              }
              return menu;
            }}
            notFoundContent={batchTeachersLoading ? <Spin size="small" /> : (batchTeacherSearchValue ? '暂无匹配的老师' : '请输入关键词搜索')}
          />
          {batchSelectedTeacherId && (
            <div style={{ marginTop: '8px', fontSize: '12px', color: '#999' }}>
              提示：清空搜索框可以移除所有练习的老师设置
            </div>
          )}
        </div>
      </Modal>
    </Row>
  );
};

export default ClazzExercisesTab; 