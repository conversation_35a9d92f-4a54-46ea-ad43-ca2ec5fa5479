import React, { useRef, useEffect, useState, useCallback, useMemo } from 'react';
import MarkdownRenderer from './MarkdownRenderer';
import { debounce } from 'lodash';

interface StreamingMarkdownRendererProps {
  content: string;
  isStreaming: boolean;
  className?: string;
}

const StreamingMarkdownRenderer: React.FC<StreamingMarkdownRendererProps> = ({
  content,
  isStreaming,
  className
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const cursorRef = useRef<HTMLSpanElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const [debouncedContent, setDebouncedContent] = useState<string>(content);
  const [, setContainsMermaid] = useState<boolean>(false);
  const [, setMermaidComplete] = useState<boolean>(false);

  // 使用防抖函数更新内容，避免频繁渲染不完整的Mermaid图表
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedSetContent = useCallback(
    debounce((newContent: string) => {
      setDebouncedContent(newContent);
    }, 300), // 300ms的防抖时间，可以根据需要调整
    []
  );

  // 跟踪流式生成是否刚刚完成
  const [justCompleted, setJustCompleted] = useState<boolean>(false);
  const prevStreamingRef = useRef<boolean>(true);

  // 缓存mermaid检测结果，避免重复计算
  const mermaidInfo = useMemo(() => {
    const unfinishedMermaidPattern = /```mermaid[\s\S]*?(?!```)/;
    const hasUnfinishedMermaid = unfinishedMermaidPattern.test(content);
    const completeMermaidPattern = /```mermaid[\s\S]*?```/g;
    const mermaidBlocks = content.match(completeMermaidPattern) || [];
    const hasMermaid = hasUnfinishedMermaid || mermaidBlocks.length > 0;
    
    let isComplete = false;
    if (hasMermaid && !hasUnfinishedMermaid && mermaidBlocks.length > 0) {
      isComplete = mermaidBlocks.every(block => {
        const isBlockComplete = block.endsWith('```');
        const hasGraphType = /^```mermaid\s*\n(graph|flowchart|sequenceDiagram|classDiagram|stateDiagram|stateDiagram-v2|erDiagram|journey|gantt|pie|gitGraph)/m.test(block);
        return isBlockComplete && hasGraphType;
      });
    }
    
    return { hasMermaid, isComplete, blockCount: mermaidBlocks.length };
  }, [content]);

  // 当原始内容变化时检测Mermaid代码块并更新防抖内容
  useEffect(() => {
    // 更新mermaid状态
    setContainsMermaid(mermaidInfo.hasMermaid);
    setMermaidComplete(mermaidInfo.isComplete);

    // 检测流式生成是否刚刚完成
    if (prevStreamingRef.current && !isStreaming) {
      setJustCompleted(true);
    }
    prevStreamingRef.current = isStreaming;

    // 如果不再流式生成，立即更新内容
    if (!isStreaming) {
      // 流式生成完成后，立即更新内容，不使用防抖
      setDebouncedContent(content);
      // 取消任何未执行的防抖函数
      debouncedSetContent.cancel();

      // 流式生成完成后，如果包含Mermaid图表，确保内容容器高度自然流动
      if (mermaidInfo.hasMermaid && contentRef.current) {
        // 短暂延迟后调整容器高度，确保Mermaid图表已渲染
        setTimeout(() => {
          if (contentRef.current) {
            contentRef.current.style.minHeight = 'auto';
          }
        }, 300);
      }
    } else {
      // 如果正在流式生成且包含Mermaid图表，使用防抖更新内容
      // 这样可以减少对未完成Mermaid图表的渲染尝试次数
      if (mermaidInfo.hasMermaid && !mermaidInfo.isComplete) {
        debouncedSetContent(content);
      } else {
        // 对于不包含Mermaid的内容，直接更新，确保用户看到最新内容
        setDebouncedContent(content);
      }
    }
  }, [content, isStreaming, debouncedSetContent, mermaidInfo]);

  // 当流式生成刚刚完成且包含Mermaid图表时，触发重新渲染
  useEffect(() => {
    if (justCompleted && mermaidInfo.hasMermaid) {
      // 短暂延迟后重新渲染Mermaid图表
      const timer = setTimeout(() => {
        // 重置完成标志
        setJustCompleted(false);

        // 在Mermaid图表渲染完成后，移除预设高度，让内容自然流动
        if (contentRef.current) {
          // 使用requestAnimationFrame确保在下一帧渲染时调整高度
          requestAnimationFrame(() => {
            if (contentRef.current) {
              // 移除固定高度限制，让内容自然流动
              contentRef.current.style.minHeight = 'auto';

              // 再次检查是否有Mermaid图表后的文本，如果有，确保它们正确显示
              const markdownContent = contentRef.current.querySelector('.markdown-content');
              if (markdownContent) {
                // 确保所有内容都可见
                (markdownContent as HTMLElement).style.overflow = 'visible';
              }
            }
          });
        }
      }, 200);

      return () => clearTimeout(timer);
    }
  }, [justCompleted, mermaidInfo.hasMermaid]);

  // 在内容更新后，确保光标位于最后一个字符后面
  useEffect(() => {
    if (isStreaming && containerRef.current && cursorRef.current) {
      // 滚动到容器底部，确保可以看到最新内容
      const container = containerRef.current;
      if (container.scrollHeight > container.clientHeight) {
        container.scrollTop = container.scrollHeight;
      }

      // 定位光标的函数
      const positionCursor = () => {
        if (!contentRef.current) return;

        const contentElement = contentRef.current;
        const cursorElement = cursorRef.current;
        if (!cursorElement) return;

        cursorElement.style.position = 'absolute';

        // 如果内容为空，将光标放在容器的左上角
        if (!content) {
          // 获取markdown-content容器
          const markdownContainer = contentElement.querySelector('.markdown-content');
          if (markdownContainer) {
            const containerRect = markdownContainer.getBoundingClientRect();
            const parentRect = container.getBoundingClientRect();
            // 将光标放在容器的左上角
            cursorElement.style.top = `${containerRect.top - parentRect.top}px`;
            cursorElement.style.left = '0px';
            cursorElement.style.height = '20px';
          } else {
            // 如果找不到容器，使用默认值
            cursorElement.style.top = '0px';
            cursorElement.style.left = '0px';
            cursorElement.style.height = '20px';
          }
          return;
        }

        // 获取Markdown内容容器
        const markdownContainer = contentElement.querySelector('.markdown-content');
        if (!markdownContainer) {
          // 如果找不到容器，使用默认值
          cursorElement.style.top = '0px';
          cursorElement.style.left = '0px';
          cursorElement.style.height = '20px';
          return;
        }

        // 使用更精确的方法定位光标到最后一个字符后面
        const markdownRect = markdownContainer.getBoundingClientRect();
        const parentRect = container.getBoundingClientRect();
        
        // 获取所有文本节点
        const walker = document.createTreeWalker(
          markdownContainer,
          NodeFilter.SHOW_TEXT,
          null
        );
        
        let lastTextNode = null;
        let currentNode;
        while ((currentNode = walker.nextNode())) {
          if (currentNode.textContent && currentNode.textContent.trim()) {
            lastTextNode = currentNode;
          }
        }
        
        if (lastTextNode && lastTextNode.textContent) {
          // 创建一个range来获取最后一个字符的位置
          const range = document.createRange();
          const textLength = lastTextNode.textContent.length;
          range.setStart(lastTextNode, textLength);
          range.setEnd(lastTextNode, textLength);
          
          try {
            const rangeRect = range.getBoundingClientRect();
            if (rangeRect.width === 0 && rangeRect.height === 0) {
              // 如果range没有尺寸，使用父元素的位置
              const parentElement = lastTextNode.parentElement;
              if (parentElement) {
                const parentElementRect = parentElement.getBoundingClientRect();
                cursorElement.style.top = `${parentElementRect.top - parentRect.top}px`;
                cursorElement.style.left = `${parentElementRect.right - parentRect.left}px`;
                cursorElement.style.height = `${parentElementRect.height || 20}px`;
              }
            } else {
              cursorElement.style.top = `${rangeRect.top - parentRect.top}px`;
              cursorElement.style.left = `${rangeRect.right - parentRect.left}px`;
              cursorElement.style.height = `${rangeRect.height || 20}px`;
            }
          } catch {
            // 如果出错，使用容器末尾位置
            cursorElement.style.top = `${markdownRect.top - parentRect.top}px`;
            cursorElement.style.left = `${markdownRect.right - parentRect.left}px`;
            cursorElement.style.height = '20px';
          }
        } else {
          // 如果没有文本内容，将光标放在容器的开始位置
          cursorElement.style.top = `${markdownRect.top - parentRect.top}px`;
          cursorElement.style.left = `${markdownRect.left - parentRect.left}px`;
          cursorElement.style.height = '20px';
        }
      };

      // 使用requestAnimationFrame确保在DOM更新后定位光标
      requestAnimationFrame(() => {
        positionCursor();
      });
    }
  }, [content, isStreaming, debouncedContent]);

  // 生成稳定的key，避免不必要的重渲染
  const stableKey = useMemo(() => {
    // 如果不包含mermaid或者流式生成已完成，使用内容的hash作为key
    if (!mermaidInfo.hasMermaid || !isStreaming) {
      return `stable-${debouncedContent.length}-${debouncedContent.slice(0, 50).replace(/\s/g, '')}`;
    }
    // 如果包含mermaid且正在流式生成，使用mermaid块数量和完成状态作为key
    return `streaming-${mermaidInfo.blockCount}-${mermaidInfo.isComplete}-${debouncedContent.length}`;
  }, [debouncedContent, mermaidInfo, isStreaming]);

  return (
    <div
      ref={containerRef}
      className={`streaming-markdown-container ${className || ''}`}
      style={{
        position: 'relative',
        display: 'block',
        wordBreak: 'break-word',
        wordWrap: 'break-word'
      }}
    >
      <div
        ref={contentRef}
        style={{
          position: 'relative',
          minHeight: '20px'
        }}
      >
        {/* 始终渲染MarkdownRenderer组件，确保内边距一致 */}
        <MarkdownRenderer
          key={stableKey}
          content={debouncedContent}
          renderMermaid={!isStreaming || justCompleted || (mermaidInfo.hasMermaid && mermaidInfo.isComplete)}
        />
        {isStreaming && (
          <span
            ref={cursorRef}
            className="blinking-cursor"
            style={{
              position: 'absolute',
              display: 'inline-block',
              width: '2px',
              height: '20px',
              backgroundColor: '#000000',
              marginLeft: '0px',
              verticalAlign: 'top',
              zIndex: 1,
              animation: 'blink 1s infinite'
            }}
            aria-hidden="true"
          />
        )}
      </div>
      <style>{`
        @keyframes blink {
          0%, 50% { opacity: 1; }
          51%, 100% { opacity: 0; }
        }
      `}</style>
    </div>
  );
};

export default StreamingMarkdownRenderer;