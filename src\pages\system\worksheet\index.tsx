import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Card, Table, Button, Space, Tooltip, Modal, Tag, Breadcrumb, Input, Select, DatePicker, Avatar } from 'antd';
import { PlusOutlined, EditOutlined, ReloadOutlined, ExclamationCircleOutlined, HomeOutlined, StopOutlined, DeleteFilled, RollbackOutlined, UndoOutlined, SendOutlined, EyeOutlined, FileImageOutlined } from '@ant-design/icons';
import type { SortOrder } from 'antd/es/table/interface';
import dayjs from 'dayjs';
import { 
  getWorksheets, 
  updateWorksheet,
  type WorksheetResponse 
} from '../../../services/system/worksheet';
import { showError, showSuccess } from '../../../utils/errorHandler';
import { getGlobalTenantInfo, type GlobalTenantInfo } from '../../../components/GlobalTenantSelector';
import WorksheetForm from './WorksheetForm';
import PicUploader from './PicUploader';
import { Link, useNavigate } from 'react-router-dom';

const { confirm } = Modal;
const { Search } = Input;
const { RangePicker } = DatePicker;

// 发布状态映射
const publishedMap: Record<number, { name: string; color: string }> = {
  0: { name: '未发布', color: 'default' },
  1: { name: '已发布', color: 'green' },
};

const WorksheetManagement: React.FC = () => {
  const [worksheets, setWorksheets] = useState<WorksheetResponse[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [picUploaderVisible, setPicUploaderVisible] = useState(false);
  const [currentWorksheet, setCurrentWorksheet] = useState<WorksheetResponse | null>(null);
  const [selectedTenantId, setSelectedTenantId] = useState<number | undefined>(undefined);
  const navigate = useNavigate();
  const [searchKeyword, setSearchKeyword] = useState<string>('');
  const [searchKeywordValue, setSearchKeywordValue] = useState<string>(''); // 用于存储搜索框的当前值
  const [isRecycleBin, setIsRecycleBin] = useState(false);
  const [selectedPublishedStatus, setSelectedPublishedStatus] = useState<number | undefined>(undefined);
  const [dateRange, setDateRange] = useState<[string, string] | null>(null);
  const [tableKey, setTableKey] = useState<number>(0);
  
  // 分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  
  // 排序状态
  const [sortOrders, setSortOrders] = useState<{
    [key: string]: 'asc' | 'desc' | undefined
  }>({
    id: 'desc', // 默认按ID降序排序
  });

  // 用于跟踪是否是首次渲染
  const isFirstRender = useRef(true);

  // 创建对搜索框和日期选择器的引用
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const searchKeywordInputRef = useRef<any>(null);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const datePickerRef = useRef<any>(null);

  // 从 GlobalTenantSelector 读取全局租户信息
  const loadGlobalTenant = useCallback(() => {
    const tenantInfo: GlobalTenantInfo | null = getGlobalTenantInfo();
    if (tenantInfo) {
      setSelectedTenantId(tenantInfo.id);
      return tenantInfo;
    } else {
      setSelectedTenantId(undefined);
      return null;
    }
  }, []);

  // 监听全局租户变化
  useEffect(() => {
    // 初始加载
    loadGlobalTenant();

    // 监听 localStorage 变化（跨标签页）
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'globalTenant') {
        loadGlobalTenant();
      }
    };

    // 监听自定义事件（同一页面内的更新）
    const handleGlobalTenantChange = (e: Event) => {
      const customEvent = e as CustomEvent;
      const { id } = customEvent.detail;
      if (id) {
        setSelectedTenantId(id);
      } else {
        setSelectedTenantId(undefined);
      }
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('globalTenantChanged', handleGlobalTenantChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('globalTenantChanged', handleGlobalTenantChange);
    };
  }, [loadGlobalTenant]);

  // 处理标题搜索
  const handleKeywordSearch = (value: string) => {
    setSearchKeyword(value);
    setSearchKeywordValue(value);
  };

  // 处理标题搜索输入框值变化，但不触发搜索
  const handleKeywordInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchKeywordValue(e.target.value);
  };

  // 处理发布状态过滤
  const handlePublishedStatusChange = (value: number | undefined) => {
    setSelectedPublishedStatus(value);
  };

  // 处理日期范围变化
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleDateRangeChange = (dates: any) => {
    if (dates && dates[0] && dates[1]) {
      // 转换为ISO 8601格式，保持本地时区
      const startDate = dayjs(dates[0].format('YYYY-MM-DD 00:00:00')).format('YYYY-MM-DDTHH:mm:ss');
      const endDate = dayjs(dates[1].format('YYYY-MM-DD 23:59:59')).format('YYYY-MM-DDTHH:mm:ss');
      setDateRange([startDate, endDate]);
    } else {
      setDateRange(null);
    }
  };

  // 切换回收站模式
  const toggleRecycleBin = () => {
    setIsRecycleBin(!isRecycleBin);
    // 重置分页状态，避免切换模式时出现显示异常
    setPagination(prev => ({
      ...prev,
      current: 1,
    }));
  };

  // 获取作业单列表
  const fetchWorksheets = useCallback(async () => {
    if (!selectedTenantId) {
      setWorksheets([]);
      setPagination(prev => ({ ...prev, total: 0 }));
      return;
    }

    try {
      setLoading(true);
      
      // 构建排序参数
      let sortBy: string | undefined;
      let sortOrder: string | undefined;
      
      if (sortOrders.id) {
        sortBy = 'id';
        sortOrder = sortOrders.id;
      }
      
      const response = await getWorksheets({
        tenant_id: selectedTenantId,
        skip: (pagination.current - 1) * pagination.pageSize,
        limit: pagination.pageSize,
        sort_by: sortBy,
        sort_order: sortOrder,
        title: searchKeyword || undefined,
        active: isRecycleBin ? 0 : 1, // 回收站模式显示禁用的，正常模式显示启用的
        published: selectedPublishedStatus,
        start_time: dateRange ? dateRange[0] : undefined,
        end_time: dateRange ? dateRange[1] : undefined,
      });
      
      setWorksheets(response.items);
      
      // 更新分页信息
      setPagination(prev => ({
        ...prev,
        total: response.total
      }));
    } catch (error) {
      showError(error, '获取作业单列表失败');
    } finally {
      setLoading(false);
    }
  }, [selectedTenantId, searchKeyword, isRecycleBin, selectedPublishedStatus, dateRange, sortOrders, pagination.current, pagination.pageSize]);

  useEffect(() => {
    if (selectedTenantId) {
      fetchWorksheets();
    }
  }, [fetchWorksheets]);

  // 处理添加作业单
  const handleAdd = () => {
    if (!selectedTenantId) {
      showError(null, '请先在顶部导航栏选择租户');
      return;
    }
    setCurrentWorksheet(null);
    setModalVisible(true);
  };

  // 处理编辑作业单
  const handleEdit = (record: WorksheetResponse) => {
    setCurrentWorksheet(record);
    setModalVisible(true);
  };

  // 处理禁用作业单（将删除改为禁用）
  const handleDisable = (id: number) => {
    const actionText = '禁用';
    confirm({
      title: `确认${actionText}`,
      icon: <ExclamationCircleOutlined />,
      content: `确定要${actionText}这个作业单吗？禁用后可在回收站中恢复。`,
      okText: '确认',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          // 将作业单禁用（设置active为0）
          await updateWorksheet(id, { active: 0 });
          showSuccess(`${actionText}成功`);
          fetchWorksheets();
        } catch (error: unknown) {
          showError(error, `${actionText}作业单失败`);
        }
      },
    });
  };

  // 处理恢复作业单
  const handleRestore = async (worksheetId: number) => {
    try {
      await updateWorksheet(worksheetId, { active: 1 });
      showSuccess('恢复成功');
      fetchWorksheets();
    } catch (error) {
      showError(error, '恢复作业单失败');
    }
  };

  // 处理发布作业单
  const handlePublish = async (worksheetId: number) => {
    try {
      await updateWorksheet(worksheetId, { published: 1 });
      showSuccess('发布成功');
      fetchWorksheets();
    } catch (error) {
      showError(error, '发布作业单失败');
    }
  };

  // 处理撤销发布作业单
  const handleUnpublish = async (worksheetId: number) => {
    try {
      await updateWorksheet(worksheetId, { published: 0 });
      showSuccess('撤销发布成功');
      fetchWorksheets();
    } catch (error) {
      showError(error, '撤销发布作业单失败');
    }
  };

  // 处理查看详情
  const handleViewDetail = (record: WorksheetResponse) => {
    navigate(`/system/worksheet/${record.id}`);
  };

  // 处理上传图片
  const handleUploadPic = (record: WorksheetResponse) => {
    setCurrentWorksheet(record);
    setPicUploaderVisible(true);
  };

  // 图片上传成功后的回调
  const handlePicUploadSuccess = () => {
    setPicUploaderVisible(false);
    fetchWorksheets();
  };

  // 表单提交成功后的回调
  const handleFormSuccess = () => {
    setModalVisible(false);
    fetchWorksheets();
  };

  // 处理重置刷新按钮点击
  const handleRefresh = async () => {
    // 暂时禁用 useEffect 触发的自动获取数据
    isFirstRender.current = true;

    // 重置搜索关键词
    setSearchKeyword('');
    setSearchKeywordValue('');

    // 重置发布状态过滤
    setSelectedPublishedStatus(undefined);

    // 重置时间范围
    setDateRange(null);

    // 重置排序为ID降序
    setSortOrders({
      id: 'desc',
    });

    // 重置分页状态
    setPagination(prev => ({
      ...prev,
      current: 1,
      pageSize: 10,
    }));

    try {
      if (!selectedTenantId) {
        setWorksheets([]);
        setPagination(prev => ({ ...prev, total: 0 }));
        return;
      }

      setLoading(true);
      const response = await getWorksheets({
        tenant_id: selectedTenantId,
        skip: 0,
        limit: 10,
        sort_by: 'id',
        sort_order: 'desc',
        active: isRecycleBin ? 0 : 1,
      });
      
      setWorksheets(response.items);
      
      // 更新分页信息
      setPagination(prev => ({
        ...prev,
        total: response.total
      }));

      // 强制重新渲染表格组件，以重置筛选 UI
      setTableKey(prevKey => prevKey + 1);
    } catch (error) {
      showError(error, '获取作业单列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理表格排序变化
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleTableChange = (paginationConfig: any, _filters: Record<string, unknown>, sorter: unknown) => {
    // 处理分页变化
    if (paginationConfig) {
      setPagination(prev => ({
        ...prev,
        current: paginationConfig.current || 1,
        pageSize: paginationConfig.pageSize || 10,
      }));
    }

    // 定义排序接口
    interface SorterInfo {
      field?: string;
      order?: 'ascend' | 'descend';
    }

    // 转换为单个sorter对象
    const currentSorter = Array.isArray(sorter) ? sorter[0] : sorter;
    
    // 重置所有排序状态
    const newSortOrders: { [key: string]: "asc" | "desc" | undefined } = {
      id: undefined,
    };

    // 根据点击的列头设置对应的sortBy和sortOrder
    if (currentSorter && (currentSorter as SorterInfo).field) {
      const field = (currentSorter as SorterInfo).field;
      const order = (currentSorter as SorterInfo).order;
      
      if (field === 'id') {
        if (order === 'ascend') {
          newSortOrders.id = 'asc';
        } else if (order === 'descend') {
          newSortOrders.id = 'desc';
        }
      }
    }

    // 如果所有排序都被取消，则使用默认排序（ID 降序）
    if (Object.values(newSortOrders).every(v => v === undefined)) {
      newSortOrders.id = 'desc';
    }

    // 更新排序状态
    setSortOrders(newSortOrders);
  };

  // 表格列定义
  const getColumns = () => {
    const baseColumns = [
      {
        title: 'ID',
        dataIndex: 'id',
        key: 'id',
        width: 80,
        sorter: true,
        sortOrder: sortOrders.id === 'asc' ? 'ascend' as SortOrder : sortOrders.id === 'desc' ? 'descend' as SortOrder : false as unknown as SortOrder,
      },
      {
        title: '图片',
        dataIndex: 'pic',
        key: 'pic',
        width: 80,
        render: (pic: string | null, record: WorksheetResponse) => (
          <div
            style={{ cursor: 'pointer' }}
            onClick={() => handleUploadPic(record)}
          >
            <Avatar
              size={40}
              src={pic}
              icon={<FileImageOutlined />}
              shape="square"
              style={{ backgroundColor: '#f0f0f0' }}
            />
          </div>
        ),
      },
      {
        title: '标题',
        dataIndex: 'title',
        key: 'title',
        width: '20%',
        ellipsis: {
          showTitle: false,
        },
        render: (text: string) => (
          <Tooltip title={text} placement="topLeft">
            {text || '-'}
          </Tooltip>
        ),
      },
      {
        title: '练习时长',
        dataIndex: 'duration',
        key: 'duration',
        render: (duration: number) => duration ? `${duration}分钟` : '-',
      },
      {
        title: '版本',
        dataIndex: 'version',
        key: 'version',
        ellipsis: {
          showTitle: false,
        },
        render: (text: string) => (
          <Tooltip title={text} placement="top">
            {text || '-'}
          </Tooltip>
        ),
      },
      {
        title: '备注',
        dataIndex: 'notes',
        key: 'notes',
        ellipsis: {
          showTitle: false,
        },
        render: (text: string) => (
          <Tooltip title={text} placement="topLeft">
            {text || '-'}
          </Tooltip>
        ),
      },
      {
        title: '发布状态',
        dataIndex: 'published',
        key: 'published',
        width: '8%',
        render: (published: number) => {
          const statusInfo = publishedMap[published] || { name: '未知状态', color: 'default' };
          return <Tag color={statusInfo.color}>{statusInfo.name}</Tag>;
        },
      },
      {
        title: '创建时间',
        dataIndex: 'ctime',
        width: '15%',
        key: 'ctime',
        render: (text: string) => new Date(text).toLocaleString(),
      },
    ];

    // 根据是否为回收站模式，显示不同的操作按钮
    if (isRecycleBin) {
      // 回收站模式下的操作列
      baseColumns.push({
        title: '操作',
        key: 'action',
        width: 160,
        render: (_: unknown, record: WorksheetResponse) => (
          <Space size="small">
            <Tooltip title="详情">
              <Button
                type="primary"
                icon={<EyeOutlined />}
                size="small"
                onClick={() => handleViewDetail(record)}
              />
            </Tooltip>
            <Tooltip title="编辑">
              <Button
                type="primary"
                icon={<EditOutlined />}
                size="small"
                onClick={() => handleEdit(record)}
              />
            </Tooltip>
            <Tooltip title="恢复">
              <Button
                type="primary"
                style={{ backgroundColor: '#52c41a', borderColor: '#52c41a' }}
                icon={<UndoOutlined />}
                size="small"
                onClick={() => handleRestore(record.id)}
              />
            </Tooltip>
          </Space>
        ),
      } as never);
    } else {
      // 正常模式下的操作列
      baseColumns.push({
        title: '操作',
        key: 'action',
        width: 160,
        render: (_: unknown, record: WorksheetResponse) => (
          <Space size="small">
            <Tooltip title="详情">
              <Button
                type="primary"
                icon={<EyeOutlined />}
                size="small"
                onClick={() => handleViewDetail(record)}
              />
            </Tooltip>
            <Tooltip title="编辑">
              <Button
                type="primary"
                icon={<EditOutlined />}
                size="small"
                onClick={() => handleEdit(record)}
              />
            </Tooltip>
            {record.published === 1 ? (
              <Tooltip title="撤销发布">
                <Button
                  type="primary"
                  style={{ backgroundColor: '#faad14', borderColor: '#faad14' }}
                  icon={<RollbackOutlined />}
                  size="small"
                  onClick={() => handleUnpublish(record.id)}
                />
              </Tooltip>
            ) : (
              <Tooltip title="发布">
                <Button
                  type="primary"
                  style={{ backgroundColor: '#52c41a', borderColor: '#52c41a' }}
                  icon={<SendOutlined />}
                  size="small"
                  onClick={() => handlePublish(record.id)}
                />
              </Tooltip>
            )}
            <Tooltip title="禁用">
              <Button
                type="primary"
                style={{ backgroundColor: '#ff4d4f', borderColor: '#ff4d4f' }}
                icon={<StopOutlined />}
                size="small"
                onClick={() => handleDisable(record.id)}
              />
            </Tooltip>
          </Space>
        ),
      } as never);
    }

    return baseColumns;
  };

  return (
    <div>
      {/* 面包屑导航 */}
      <Breadcrumb 
        style={{ marginBottom: 16 }}
        items={[
          {
            title: (
              <Link to="/system/home">
                <HomeOutlined /> 主页
              </Link>
            ),
          },
          {
            title: '平台管理',
          },
          {
            title: '作业单管理',
          },
        ]}
      />

      <Card
        title={isRecycleBin ? "作业单回收站" : "作业单管理"}
        extra={
          <Space>
            <Search
              placeholder="搜索标题"
              allowClear
              onSearch={handleKeywordSearch}
              style={{ width: 160 }}
              ref={searchKeywordInputRef}
              value={searchKeywordValue}
              onChange={handleKeywordInputChange}
            />
            <Select
              placeholder="发布状态"
              allowClear
              style={{ width: 120 }}
              value={selectedPublishedStatus}
              onChange={handlePublishedStatusChange}
            >
              <Select.Option value={0}>未发布</Select.Option>
              <Select.Option value={1}>已发布</Select.Option>
            </Select>
            <RangePicker
              onChange={handleDateRangeChange}
              placeholder={['开始时间', '结束时间']}
              style={{ width: 240 }}
              showTime={false}
              format="YYYY-MM-DD"
              ref={datePickerRef}
              value={dateRange ? [dayjs(dateRange[0]), dayjs(dateRange[1])] : null}
            />
            {/* 添加作业单按钮只在非回收站模式下显示 */}
            {!isRecycleBin && selectedTenantId && (
              <Tooltip title="添加作业单">
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAdd}
                />
              </Tooltip>
            )}
            <Tooltip title="重置刷新">
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
              />
            </Tooltip>
            {/* 回收站/返回按钮 */}
            {isRecycleBin ? (
              <Tooltip title="返回">
                <Button
                  type="primary"
                  icon={<RollbackOutlined />}
                  onClick={toggleRecycleBin}
                />
              </Tooltip>
            ) : (
              <Tooltip title="回收站">
                <Button
                  type="default"
                  icon={<DeleteFilled />}
                  onClick={toggleRecycleBin}
                />
              </Tooltip>
            )}
          </Space>
        }
      >
        <Table
          key={tableKey}
          columns={getColumns()}
          dataSource={worksheets}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          locale={{
            emptyText: selectedTenantId ? (isRecycleBin ? '回收站中没有作业单' : '暂无作业单数据') : '请先在顶部导航栏选择租户'
          }}
          onChange={handleTableChange}
          sortDirections={['ascend', 'descend', 'ascend']}
          showSorterTooltip={false}
        />
      </Card>

      {selectedTenantId && (
        <WorksheetForm
          visible={modalVisible}
          onCancel={() => setModalVisible(false)}
          onSuccess={handleFormSuccess}
          worksheet={currentWorksheet}
          tenantId={selectedTenantId}
        />
      )}

      {selectedTenantId && currentWorksheet && (
        <PicUploader
          visible={picUploaderVisible}
          onCancel={() => setPicUploaderVisible(false)}
          onSuccess={handlePicUploadSuccess}
          exerciseId={currentWorksheet.eid}
          tenantId={selectedTenantId}
          currentPic={currentWorksheet.pic}
        />
      )}
    </div>
  );
};

export default WorksheetManagement; 